{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pile Height Measurement from ML-Corrected Point Cloud\n", "\n", "This notebook measures actual pile heights from the ML-corrected point cloud that is now in IFC coordinate system.\n", "\n", "**Workflow:**\n", "1. Load ML-corrected point cloud (in IFC coordinates)\n", "2. Identify visible piles using ground truth validation data\n", "3. Extract pile point clusters for each visible pile\n", "4. Measure pile heights (top - ground level)\n", "5. <PERSON><PERSON><PERSON> with IFC design heights\n", "6. Generate compliance reports\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Parameters\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"\n", "pile_search_radius = 2.0  # meters\n", "min_pile_points = 50\n", "height_tolerance = 0.5  # meters for compliance\n", "save_results = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "from sklearn.cluster import DBSCAN\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add project root to path\n", "project_root = Path().resolve().parents[2]\n", "sys.path.append(str(project_root))\n", "\n", "from src.config.paths import get_processed_data_path\n", "\n", "print(\"=== PILE HEIGHT MEASUREMENT ===\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load ML-corrected point cloud (now in IFC coordinate system)\n", "ml_corrected_file = f\"../../../data/processed/{site_name}/ml_local_alignment/{site_name}_ml_corrected.ply\"\n", "corrected_pcd = o3d.io.read_point_cloud(ml_corrected_file)\n", "corrected_points = np.asarray(corrected_pcd.points)\n", "\n", "print(f\"Loaded ML-corrected point cloud: {len(corrected_points):,} points\")\n", "print(f\"Z range: [{corrected_points[:, 2].min():.2f}, {corrected_points[:, 2].max():.2f}]m\")\n", "\n", "# Load IFC pile metadata\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_metadata_file)\n", "pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)\n", "ifc_piles = ifc_df[pile_mask][['Name', 'X', 'Y', 'Z']].dropna()\n", "\n", "print(f\"Loaded IFC pile metadata: {len(ifc_piles):,} design piles\")\n", "\n", "# Load ground truth validation data (actual visible piles from drone survey)\n", "ground_truth_file = f\"../../../data/validation/Trino_piles.csv\"\n", "if Path(ground_truth_file).exists():\n", "    ground_truth = pd.read_csv(ground_truth_file)\n", "    print(f\"Loaded ground truth: {len(ground_truth):,} surveyed piles\")\n", "    print(f\"Ground truth columns: {list(ground_truth.columns)}\")\nelse:\n    print(\"Ground truth file not found, using IFC subset for demonstration\")\n    # Use a subset of IFC piles as proxy for visible piles\n    ground_truth = ifc_piles.sample(n=min(1000, len(ifc_piles)), random_state=42)\n    ground_truth.columns = ['Name', 'X', 'Y', 'Z']  # Standardize column names"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Identify Visible Piles"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_visible_piles(ground_truth, ifc_piles, max_distance=5.0):\n", "    \"\"\"Match ground truth surveyed piles to IFC design piles\"\"\"\n", "    \n", "    # Create spatial index for IFC piles\n", "    ifc_coords = ifc_piles[['X', 'Y']].values\n", "    ifc_tree = cKDTree(ifc_coords)\n", "    \n", "    visible_piles = []\n", "    \n", "    for idx, gt_pile in ground_truth.iterrows():\n", "        # Find nearest IFC pile\n", "        gt_coords = [gt_pile['X'], gt_pile['Y']]\n", "        distances, indices = ifc_tree.query(gt_coords, k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            ifc_pile = ifc_piles.iloc[indices]\n", "            \n", "            visible_piles.append({\n", "                'gt_name': gt_pile.get('Name', f'GT_{idx}'),\n", "                'ifc_name': ifc_pile['Name'],\n", "                'gt_x': gt_pile['X'],\n", "                'gt_y': gt_pile['Y'], \n", "                'gt_z': gt_pile['Z'],\n", "                'ifc_x': ifc_pile['X'],\n", "                'ifc_y': ifc_pile['Y'],\n", "                'ifc_z': ifc_pile['Z'],\n", "                'xy_distance': distances\n", "            })\n", "    \n", "    return pd.DataFrame(visible_piles)\n", "\n", "# Find visible piles\n", "visible_piles = find_visible_piles(ground_truth, ifc_piles)\n", "print(f\"\\nIdentified {len(visible_piles)} visible piles\")\n", "print(f\"Average XY distance to IFC: {visible_piles['xy_distance'].mean():.2f}m\")\n", "\n", "# Display sample\n", "print(\"\\nSample visible piles:\")\n", "print(visible_piles.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Extract Pile Point Clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_pile_points(point_cloud, pile_location, search_radius=2.0, min_points=50):\n", "    \"\"\"Extract point cluster around a pile location\"\"\"\n", "    \n", "    # Create spatial index\n", "    tree = cKDTree(point_cloud[:, :2])  # XY only\n", "    \n", "    # Find points within radius\n", "    pile_xy = [pile_location['ifc_x'], pile_location['ifc_y']]\n", "    indices = tree.query_ball_point(pile_xy, r=search_radius)\n", "    \n", "    if len(indices) < min_points:\n", "        return None, None\n", "    \n", "    pile_points = point_cloud[indices]\n", "    \n", "    # Filter by height (remove obvious ground points)\n", "    # Assume piles are at least 0.5m above local ground\n", "    local_ground_z = np.percentile(pile_points[:, 2], 10)  # 10th percentile as ground\n", "    pile_mask = pile_points[:, 2] > (local_ground_z + 0.5)\n", "    \n", "    if np.sum(pile_mask) < min_points:\n", "        return pile_points, local_ground_z  # Return all points if filtering removes too many\n", "    \n", "    return pile_points[pile_mask], local_ground_z\n", "\n", "def measure_pile_height(pile_points, ground_z):\n", "    \"\"\"Measure pile height from point cluster\"\"\"\n", "    \n", "    if pile_points is None or len(pile_points) == 0:\n", "        return None\n", "    \n", "    # Pile top: 95th percentile of Z values (robust to outliers)\n", "    pile_top_z = np.percentile(pile_points[:, 2], 95)\n", "    \n", "    # Pile height\n", "    pile_height = pile_top_z - ground_z\n", "    \n", "    return {\n", "        'pile_top_z': pile_top_z,\n", "        'ground_z': ground_z,\n", "        'pile_height': pile_height,\n", "        'num_points': len(pile_points),\n", "        'z_std': np.std(pile_points[:, 2])\n", "    }\n", "\n", "# Extract pile measurements\n", "pile_measurements = []\n", "\n", "print(\"Extracting pile point clusters and measuring heights...\")\n", "for idx, pile in visible_piles.iterrows():\n", "    # Extract points\n", "    pile_points, ground_z = extract_pile_points(\n", "        corrected_points, pile, search_radius=pile_search_radius, min_points=min_pile_points\n", "    )\n", "    \n", "    # Measure height\n", "    measurement = measure_pile_height(pile_points, ground_z)\n", "    \n", "    if measurement is not None:\n", "        # Combine with pile info\n", "        result = {**pile.to_dict(), **measurement}\n", "        \n", "        # Calculate design height (IFC top - local ground)\n", "        result['design_height'] = pile['ifc_z'] - ground_z\n", "        result['height_difference'] = result['pile_height'] - result['design_height']\n", "        \n", "        pile_measurements.append(result)\n", "    \n", "    if idx % 100 == 0:\n", "        print(f\"Processed {idx}/{len(visible_piles)} piles\")\n", "\n", "pile_results = pd.DataFrame(pile_measurements)\n", "print(f\"\\nSuccessfully measured {len(pile_results)} piles\")\n", "print(f\"Failed to measure {len(visible_piles) - len(pile_results)} piles (insufficient points)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON> Pile Heights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(pile_results) > 0:\n", "    print(\"=== PILE HEIGHT ANALYSIS ===\")\n", "    \n", "    # Basic statistics\n", "    print(f\"\\nMeasured pile heights:\")\n", "    print(f\"  Mean: {pile_results['pile_height'].mean():.2f}m\")\n", "    print(f\"  Median: {pile_results['pile_height'].median():.2f}m\")\n", "    print(f\"  Std: {pile_results['pile_height'].std():.2f}m\")\n", "    print(f\"  Range: [{pile_results['pile_height'].min():.2f}, {pile_results['pile_height'].max():.2f}]m\")\n", "    \n", "    print(f\"\\nDesign pile heights:\")\n", "    print(f\"  Mean: {pile_results['design_height'].mean():.2f}m\")\n", "    print(f\"  Median: {pile_results['design_height'].median():.2f}m\")\n", "    print(f\"  Std: {pile_results['design_height'].std():.2f}m\")\n", "    print(f\"  Range: [{pile_results['design_height'].min():.2f}, {pile_results['design_height'].max():.2f}]m\")\n", "    \n", "    print(f\"\\nHeight differences (measured - design):\")\n", "    print(f\"  Mean: {pile_results['height_difference'].mean():.2f}m\")\n", "    print(f\"  Median: {pile_results['height_difference'].median():.2f}m\")\n", "    print(f\"  Std: {pile_results['height_difference'].std():.2f}m\")\n", "    print(f\"  Range: [{pile_results['height_difference'].min():.2f}, {pile_results['height_difference'].max():.2f}]m\")\n", "    \n", "    # Compliance analysis\n", "    within_tolerance = np.abs(pile_results['height_difference']) <= height_tolerance\n", "    compliance_rate = np.sum(within_tolerance) / len(pile_results) * 100\n", "    \n", "    print(f\"\\nCOMPLIANCE ANALYSIS (±{height_tolerance}m tolerance):\")\n", "    print(f\"  Compliant piles: {np.sum(within_tolerance)}/{len(pile_results)} ({compliance_rate:.1f}%)\")\n", "    print(f\"  Non-compliant piles: {len(pile_results) - np.sum(within_tolerance)} ({100-compliance_rate:.1f}%)\")\n", "    \n", "    # Identify problematic piles\n", "    problematic = pile_results[~within_tolerance].copy()\n", "    if len(problematic) > 0:\n", "        print(f\"\\nMost problematic piles:\")\n", "        problematic_sorted = problematic.nlargest(5, 'height_difference')\n", "        for _, pile in problematic_sorted.iterrows():\n", "            print(f\"  {pile['ifc_name']}: {pile['height_difference']:+.2f}m difference\")\nelse:\n    print(\"No pile measurements available for analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(pile_results) > 0:\n", "    # Create comprehensive visualization\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    fig.suptitle('Pile Height Measurement Analysis', fontsize=16)\n", "    \n", "    # 1. Height distribution\n", "    axes[0, 0].hist(pile_results['pile_height'], bins=30, alpha=0.7, color='blue', label='Measured')\n", "    axes[0, 0].hist(pile_results['design_height'], bins=30, alpha=0.7, color='red', label='Design')\n", "    axes[0, 0].set_xlabel('Pile Height (m)')\n", "    axes[0, 0].set_ylabel('Frequency')\n", "    axes[0, 0].set_title('Pile Height Distribution')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. Measured vs Design scatter\n", "    axes[0, 1].scatter(pile_results['design_height'], pile_results['pile_height'], \n", "                      alpha=0.6, s=20)\n", "    # Perfect correlation line\n", "    min_h = min(pile_results['design_height'].min(), pile_results['pile_height'].min())\n", "    max_h = max(pile_results['design_height'].max(), pile_results['pile_height'].max())\n", "    axes[0, 1].plot([min_h, max_h], [min_h, max_h], 'r--', label='Perfect match')\n", "    axes[0, 1].set_xlabel('Design Height (m)')\n", "    axes[0, 1].set_ylabel('Measured Height (m)')\n", "    axes[0, 1].set_title('Measured vs Design Heights')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. Height difference distribution\n", "    axes[0, 2].hist(pile_results['height_difference'], bins=30, alpha=0.7, color='green')\n", "    axes[0, 2].axvline(0, color='red', linestyle='--', label='Perfect match')\n", "    axes[0, 2].axvline(-height_tolerance, color='orange', linestyle=':', label=f'±{height_tolerance}m tolerance')\n", "    axes[0, 2].axvline(height_tolerance, color='orange', linestyle=':')\n", "    axes[0, 2].set_xlabel('Height Difference (m)')\n", "    axes[0, 2].set_ylabel('Frequency')\n", "    axes[0, 2].set_title('Height Difference Distribution')\n", "    axes[0, 2].legend()\n", "    axes[0, 2].grid(True, alpha=0.3)\n", "    \n", "    # 4. Spatial distribution of height differences\n", "    scatter = axes[1, 0].scatter(pile_results['ifc_x'], pile_results['ifc_y'], \n", "                                c=pile_results['height_difference'], \n", "                                cmap='RdYlBu_r', s=20, alpha=0.7)\n", "    plt.colorbar(scatter, ax=axes[1, 0], label='Height Difference (m)')\n", "    axes[1, 0].set_xlabel('X (UTM Easting)')\n", "    axes[1, 0].set_ylabel('Y (UTM Northing)')\n", "    axes[1, 0].set_title('Spatial Distribution of Height Differences')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 5. Compliance map\n", "    compliant_color = ['red' if not compliant else 'green' \n", "                      for compliant in within_tolerance]\n", "    axes[1, 1].scatter(pile_results['ifc_x'], pile_results['ifc_y'], \n", "                      c=compliant_color, s=20, alpha=0.7)\n", "    axes[1, 1].set_xlabel('X (UTM Easting)')\n", "    axes[1, 1].set_ylabel('Y (UTM Northing)')\n", "    axes[1, 1].set_title(f'Compliance Map (±{height_tolerance}m)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # Add legend for compliance\n", "    from matplotlib.patches import Patch\n", "    legend_elements = [Patch(facecolor='green', label='Compliant'),\n", "                      Patch(facecolor='red', label='Non-compliant')]\n", "    axes[1, 1].legend(handles=legend_elements)\n", "    \n", "    # 6. Point count vs measurement quality\n", "    axes[1, 2].scatter(pile_results['num_points'], np.abs(pile_results['height_difference']), \n", "                      alpha=0.6, s=20)\n", "    axes[1, 2].set_xlabel('Number of Points in Pile Cluster')\n", "    axes[1, 2].set_ylabel('Absolute Height Difference (m)')\n", "    axes[1, 2].set_title('Measurement Quality vs Point Density')\n", "    axes[1, 2].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    if save_results:\n", "        output_dir = get_processed_data_path(site_name, \"pile_measurements\")\n", "        output_dir.mkdir(parents=True, exist_ok=True)\n", "        plt.savefig(output_dir / \"pile_height_analysis.png\", dpi=300, bbox_inches='tight')\n", "        print(f\"Saved visualization: {output_dir / 'pile_height_analysis.png'}\")\nelse:\n    print(\"No measurements available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_results and len(pile_results) > 0:\n", "    print(\"=== SAVING PILE MEASUREMENT RESULTS ===\")\n", "    \n", "    output_dir = get_processed_data_path(site_name, \"pile_measurements\")\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save detailed results\n", "    results_file = output_dir / f\"{site_name}_pile_height_measurements.csv\"\n", "    pile_results.to_csv(results_file, index=False)\n", "    print(f\"✅ Saved detailed results: {results_file}\")\n", "    \n", "    # Save compliance summary\n", "    compliance_summary = {\n", "        'site_name': site_name,\n", "        'measurement_date': datetime.now().isoformat(),\n", "        'total_design_piles': len(ifc_piles),\n", "        'visible_piles_identified': len(visible_piles),\n", "        'successfully_measured': len(pile_results),\n", "        'measurement_success_rate': len(pile_results) / len(visible_piles) * 100,\n", "        'height_tolerance_m': height_tolerance,\n", "        'compliant_piles': int(np.sum(within_tolerance)),\n", "        'non_compliant_piles': int(len(pile_results) - np.sum(within_tolerance)),\n", "        'compliance_rate_percent': float(compliance_rate),\n", "        'statistics': {\n", "            'measured_height_mean': float(pile_results['pile_height'].mean()),\n", "            'measured_height_std': float(pile_results['pile_height'].std()),\n", "            'design_height_mean': float(pile_results['design_height'].mean()),\n", "            'design_height_std': float(pile_results['design_height'].std()),\n", "            'height_difference_mean': float(pile_results['height_difference'].mean()),\n", "            'height_difference_std': float(pile_results['height_difference'].std()),\n", "            'height_difference_rmse': float(np.sqrt(np.mean(pile_results['height_difference']**2)))\n", "        },\n", "        'parameters': {\n", "            'pile_search_radius_m': pile_search_radius,\n", "            'min_pile_points': min_pile_points,\n", "            'ground_method': ground_method\n", "        }\n", "    }\n", "    \n", "    summary_file = output_dir / f\"{site_name}_pile_measurement_summary.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(compliance_summary, f, indent=2)\n", "    print(f\"✅ Saved compliance summary: {summary_file}\")\n", "    \n", "    # Save non-compliant piles for follow-up\n", "    if len(problematic) > 0:\n", "        non_compliant_file = output_dir / f\"{site_name}_non_compliant_piles.csv\"\n", "        problematic.to_csv(non_compliant_file, index=False)\n", "        print(f\"✅ Saved non-compliant piles: {non_compliant_file}\")\n", "    \n", "    print(f\"\\n📁 All results saved to: {output_dir}\")\n", "    print(f\"\\n🎯 SUMMARY:\")\n", "    print(f\"   Total design piles: {len(ifc_piles):,}\")\n", "    print(f\"   Visible piles identified: {len(visible_piles):,}\")\n", "    print(f\"   Successfully measured: {len(pile_results):,}\")\n", "    print(f\"   Compliance rate: {compliance_rate:.1f}%\")\n", "    print(f\"   Height difference RMSE: {np.sqrt(np.mean(pile_results['height_difference']**2)):.3f}m\")\nelse:\n    print(\"Skipping save (save_results=False or no measurements available)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}