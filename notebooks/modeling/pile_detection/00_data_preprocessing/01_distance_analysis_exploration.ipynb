# Papermill parameters - these will be injected by <PERSON><PERSON>
import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]
print(f"Notebooks root: {notebooks_root}")

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import (
    get_data_path,
    get_output_path,
    get_processed_data_path
)

# Papermill parameters - these will be injected by <PERSON><PERSON>
from datetime import datetime

site_name = "trino_enel"  # Site name for output file naming

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Standardized paths
input_path = get_processed_data_path(site_name, f"reference-data")
CRS = "EPSG:32632"

import geopandas as gpd
import pandas as pd

import geopandas as gpd
import pandas as pd
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.spatial.distance import cdist
from scipy.spatial import cKDTree
from pathlib import Path

# Set plotting style
sns.set(style="whitegrid")


# Cell 2: Load KML data
print("=== LOADING KML DATA ===")

kml_path = input_path / "kml_piles_utm.csv"
print(f"KML path: {kml_path}")
kml_df = pd.read_csv(kml_path)

if 'Z' not in kml_df.columns:
    kml_df['Z'] = 0
print(f"KML piles: {len(kml_df)} records")
print(f"Columns: {list(kml_df.columns)}")

# Cell 3: Load IFC data
print("=== LOADING IFC DATA ===")
ifc_enhanced_path = '/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv'
ifc_piles_path = '/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv'

try:
    ifc_df = pd.read_csv(ifc_enhanced_path)
    print(f"Loaded IFC enhanced metadata: {len(ifc_df)} records")
except FileNotFoundError:
    ifc_df = pd.read_csv(ifc_piles_path)
    print(f"Loaded IFC piles file: {len(ifc_df)} records")

print(f"Columns: {list(ifc_df.columns)}")

# Cell 4: Load point cloud data
print("=== LOADING POINT CLOUD DATA ===")
ply_path = '/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected.ply'
point_cloud = o3d.io.read_point_cloud(ply_path)
ply_points = np.asarray(point_cloud.points)
print(f"Point cloud points: {len(ply_points):,}")
print(f"Point cloud shape: {ply_points.shape}")


# Cell 5: Load harmonized data (optional)
print("=== LOADING HARMONIZED DATA ===")
harmonized_path = Path('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/00_data_preparation/output/ml_patch_data/trino_enel_harmonized_piles_corrected.csv')

harmonized_df = None
if harmonized_path.exists():
    harmonized_df = pd.read_csv(harmonized_path)
    print(f"Harmonized piles: {len(harmonized_df)} records")
else:
    print("Harmonized file not found - skipping")

# %%
# Cell 6: Dataset summary
print("=== DATASET SUMMARY ===")
print(f"KML piles: {len(kml_df)}")
print(f"IFC piles: {len(ifc_df)}")
print(f"Point cloud points: {len(ply_points):,}")
if harmonized_df is not None:
    print(f"Harmonized piles: {len(harmonized_df)}")

# Cell 7: Coordinate range analysis
def analyze_coordinate_ranges(kml_df, ifc_df, ply_points):
    """Compare coordinate ranges between all datasets."""
    print("=== COORDINATE RANGES ===")
    
    # KML coordinates
    print("KML coordinates:")
    print(f"  X: {kml_df['X'].min():.2f} to {kml_df['X'].max():.2f}")
    print(f"  Y: {kml_df['Y'].min():.2f} to {kml_df['Y'].max():.2f}")
    
    # IFC coordinates
    print("IFC coordinates:")
    print(f"  X: {ifc_df['X'].min():.2f} to {ifc_df['X'].max():.2f}")
    print(f"  Y: {ifc_df['Y'].min():.2f} to {ifc_df['Y'].max():.2f}")
    
    # Point cloud coordinates
    print("Point cloud coordinates:")
    print(f"  X: {ply_points[:, 0].min():.2f} to {ply_points[:, 0].max():.2f}")
    print(f"  Y: {ply_points[:, 1].min():.2f} to {ply_points[:, 1].max():.2f}")
    print(f"  Z: {ply_points[:, 2].min():.2f} to {ply_points[:, 2].max():.2f}")
    
    # Check overlaps
    print("\n=== ALIGNMENT CHECK ===")
    kml_x_range = (kml_df['X'].min(), kml_df['X'].max())
    kml_y_range = (kml_df['Y'].min(), kml_df['Y'].max())
    ifc_x_range = (ifc_df['X'].min(), ifc_df['X'].max())
    ifc_y_range = (ifc_df['Y'].min(), ifc_df['Y'].max())
    ply_x_range = (ply_points[:, 0].min(), ply_points[:, 0].max())
    ply_y_range = (ply_points[:, 1].min(), ply_points[:, 1].max())
    
    # Check overlaps
    kml_ifc_x_overlap = kml_x_range[1] > ifc_x_range[0] and kml_x_range[0] < ifc_x_range[1]
    kml_ifc_y_overlap = kml_y_range[1] > ifc_y_range[0] and kml_y_range[0] < ifc_y_range[1]
    kml_ply_x_overlap = kml_x_range[1] > ply_x_range[0] and kml_x_range[0] < ply_x_range[1]
    kml_ply_y_overlap = kml_y_range[1] > ply_y_range[0] and kml_y_range[0] < ply_y_range[1]
    
    print("Coordinate overlaps:")
    print(f"  KML vs IFC: X={'Yes' if kml_ifc_x_overlap else 'No'}, Y={'Yes' if kml_ifc_y_overlap else 'No'}")
    print(f"  KML vs PLY: X={'Yes' if kml_ply_x_overlap else 'No'}, Y={'Yes' if kml_ply_y_overlap else 'No'}")
    
    return {
        'kml_ifc_overlap': (kml_ifc_x_overlap, kml_ifc_y_overlap),
        'kml_ply_overlap': (kml_ply_x_overlap, kml_ply_y_overlap)
    }

# Analyze coordinate ranges
overlap_results = analyze_coordinate_ranges(kml_df, ifc_df, ply_points)


# Cell 8: Distance analysis functions
def compute_nearest_distances(coordinates, ply_points):
    """
    For each coordinate point, find the distance to the nearest point cloud point.
    
    Parameters:
    coordinates: numpy array of [X, Y] coordinates
    ply_points: numpy array of point cloud coordinates [X, Y, Z]
    
    Returns:
    distances: array of distances to nearest points
    nearest_z_values: array of Z values of nearest points
    nearest_indices: array of indices of nearest points
    """
    # Build spatial index for point cloud
    tree = cKDTree(ply_points[:, :2])  # X,Y only
    
    distances_to_nearest = []
    nearest_z_values = []
    nearest_indices = []
    
    for coord_point in coordinates:
        # Find nearest point cloud point
        distance, index = tree.query(coord_point)
        distances_to_nearest.append(distance)
        nearest_z_values.append(ply_points[index, 2])  # Z coordinate
        nearest_indices.append(index)
    
    return np.array(distances_to_nearest), np.array(nearest_z_values), np.array(nearest_indices)

def analyze_distance_statistics(distances, dataset_name):
    """Print comprehensive distance statistics."""
    print(f"\nDistance from {dataset_name} to nearest PLY point:")
    print(f"  Mean: {np.mean(distances):.2f}m")
    print(f"  Median: {np.median(distances):.2f}m")
    print(f"  Min: {np.min(distances):.2f}m")
    print(f"  Max: {np.max(distances):.2f}m")
    print(f"  Standard deviation: {np.std(distances):.2f}m")
    
    print(f"\nPiles within different distance thresholds:")
    for threshold in [1, 2, 5, 10, 20]:
        count = sum(1 for d in distances if d <= threshold)
        percentage = (count / len(distances)) * 100
        print(f"  Within {threshold}m: {count}/{len(distances)} ({percentage:.1f}%)")

def analyze_systematic_offset(coordinates, ply_points, dataset_name):
    """Check for systematic offset patterns between coordinate data and point cloud."""
    print(f"\n=== SYSTEMATIC OFFSET CHECK ({dataset_name}) ===")
    
    # Build spatial index
    tree = cKDTree(ply_points[:, :2])
    
    x_offsets = []
    y_offsets = []
    
    for coord_point in coordinates:
        _, index = tree.query(coord_point)
        nearest_ply_point = ply_points[index, :2]
        x_offset = coord_point[0] - nearest_ply_point[0]
        y_offset = coord_point[1] - nearest_ply_point[1]
        x_offsets.append(x_offset)
        y_offsets.append(y_offset)
    
    print(f"Offset analysis ({dataset_name} - PLY):")
    print(f"  X offset - Mean: {np.mean(x_offsets):.2f}m, Median: {np.median(x_offsets):.2f}m")
    print(f"  Y offset - Mean: {np.mean(y_offsets):.2f}m, Median: {np.median(y_offsets):.2f}m")
    print(f"  X offset std: {np.std(x_offsets):.2f}m")
    print(f"  Y offset std: {np.std(y_offsets):.2f}m")
    
    # Check if there's a consistent directional bias
    if abs(np.median(x_offsets)) > 1 or abs(np.median(y_offsets)) > 1:
        print(f"  POTENTIAL SYSTEMATIC OFFSET DETECTED")
    else:
        print(f"  NO SIGNIFICANT SYSTEMATIC OFFSET")
    
    return x_offsets, y_offsets

# Cell 9: Point density analysis
def analyze_point_density(pile_df, ply_points, dataset_name, search_radius=5.0):
    """For each pile location, count nearby point cloud points within search radius."""
    print(f"=== POINT DENSITY CHECK ({dataset_name}) ===")
    
    # Build spatial index for point cloud
    tree = cKDTree(ply_points[:, :2])  # X,Y only
    
    nearby_counts = []
    for _, pile in pile_df.iterrows():
        pile_coords = [pile['X'], pile['Y']]
        indices = tree.query_ball_point(pile_coords, search_radius)
        nearby_counts.append(len(indices))
    
    pile_df[f'nearby_points_{search_radius}m'] = nearby_counts
    
    print(f"Points within {search_radius}m of {dataset_name} piles:")
    print(f"  Mean: {np.mean(nearby_counts):.1f}")
    print(f"  Median: {np.median(nearby_counts):.1f}")
    print(f"  Min: {np.min(nearby_counts)}")
    print(f"  Max: {np.max(nearby_counts)}")
    print(f"  Piles with >0 points: {sum(1 for x in nearby_counts if x > 0)}/{len(nearby_counts)}")
    
    return nearby_counts

# Analyze point density for both datasets
kml_density = analyze_point_density(kml_df, ply_points, "KML")
ifc_density = analyze_point_density(ifc_df, ply_points, "IFC")

# Cell 10: KML vs Point Cloud analysis
print("=== KML vs ML-CORRECTED PLY DISTANCE ANALYSIS ===")

kml_coords = kml_df[['X', 'Y']].values
kml_distances, kml_nearest_z, _ = compute_nearest_distances(kml_coords, ply_points)

# Add results to dataframe
kml_df['distance_to_nearest_ply'] = kml_distances
kml_df['nearest_ply_z'] = kml_nearest_z

# Print statistics
analyze_distance_statistics(kml_distances, "KML")

print(f"\nElevation analysis (nearest PLY points):")
print(f"  Z range: {np.min(kml_nearest_z):.2f}m to {np.max(kml_nearest_z):.2f}m")
print(f"  Z mean: {np.mean(kml_nearest_z):.2f}m")

# Analyze systematic offset
kml_x_offsets, kml_y_offsets = analyze_systematic_offset(kml_coords, ply_points, "KML")


# Cell 11: IFC vs Point Cloud analysis
print("=== IFC vs ML-CORRECTED PLY DISTANCE ANALYSIS ===")

ifc_coords = ifc_df[['X', 'Y']].values
print(f"Processing {len(ifc_coords)} IFC pile locations...")

ifc_distances, ifc_nearest_z, _ = compute_nearest_distances(ifc_coords, ply_points)

# Add results to dataframe
ifc_df['distance_to_nearest_ply'] = ifc_distances
ifc_df['nearest_ply_z'] = ifc_nearest_z

# Print statistics
analyze_distance_statistics(ifc_distances, "IFC")

print(f"\nElevation analysis (nearest PLY points):")
print(f"  Z range: {np.min(ifc_nearest_z):.2f}m to {np.max(ifc_nearest_z):.2f}m")
print(f"  Z mean: {np.mean(ifc_nearest_z):.2f}m")

# Compare IFC Z with nearest PLY Z if Z column exists
if 'Z' in ifc_df.columns:
    ifc_z_values = ifc_df['Z'].values
    z_differences = ifc_z_values - ifc_nearest_z
    print(f"  Z difference (IFC - PLY): Mean {np.mean(z_differences):.2f}m, Std {np.std(z_differences):.2f}m")

# Analyze systematic offset
ifc_x_offsets, ifc_y_offsets = analyze_systematic_offset(ifc_coords, ply_points, "IFC")


# Cell 12: Comparison summary
print("=== COMPARISON SUMMARY ===")
print(f"KML vs PLY: {np.mean(kml_distances):.2f}m mean, {np.median(kml_distances):.2f}m median")
print(f"IFC vs PLY: {np.mean(ifc_distances):.2f}m mean, {np.median(ifc_distances):.2f}m median")
print(f"Difference: {np.mean(ifc_distances) - np.mean(kml_distances):.2f}m mean, {np.median(ifc_distances) - np.median(kml_distances):.2f}m median")


# Cell 13: Visualization functions
def plot_distance_histogram(distances, dataset_name, bins=40):
    """Create histogram plot of distance distributions."""
    plt.figure(figsize=(10, 6))
    plt.hist(distances, bins=bins, alpha=0.7, edgecolor='black')
    plt.title(f'Distance Distribution: {dataset_name} to Nearest Point Cloud Point')
    plt.xlabel('Distance (meters)')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    
    # Add statistics text
    stats_text = f'Mean: {np.mean(distances):.2f}m\nMedian: {np.median(distances):.2f}m\nStd: {np.std(distances):.2f}m'
    plt.text(0.7, 0.8, stats_text, transform=plt.gca().transAxes, 
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def plot_offset_scatter(x_offsets, y_offsets, dataset_name):
    """Create scatter plot of X,Y offsets to visualize systematic bias."""
    plt.figure(figsize=(10, 8))
    plt.scatter(x_offsets, y_offsets, alpha=0.6)
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    plt.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    plt.xlabel('X Offset (meters)')
    plt.ylabel('Y Offset (meters)')
    plt.title(f'Systematic Offset Analysis: {dataset_name}')
    plt.grid(True, alpha=0.3)
    
    # Add mean offset marker
    mean_x, mean_y = np.mean(x_offsets), np.mean(y_offsets)
    plt.scatter(mean_x, mean_y, color='red', s=100, marker='x', label=f'Mean Offset ({mean_x:.2f}, {mean_y:.2f})')
    plt.legend()
    
    plt.tight_layout()
    plt.show()

def plot_comparative_histogram(kml_distances, ifc_distances):
    """Create comparative histogram of both datasets."""
    plt.figure(figsize=(12, 6))
    plt.hist(kml_distances, bins=30, alpha=0.7, label='KML', edgecolor='black')
    plt.hist(ifc_distances, bins=30, alpha=0.7, label='IFC', edgecolor='black')
    plt.title('Distance Comparison: KML vs IFC to Nearest Point Cloud Point')
    plt.xlabel('Distance (meters)')
    plt.ylabel('Frequency')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


# Cell 14: Create all visualizations
print("Creating visualizations...")

# Individual histograms
plot_distance_histogram(kml_distances, "KML")
plot_distance_histogram(ifc_distances, "IFC")

# Comparative histogram
plot_comparative_histogram(kml_distances, ifc_distances)

# Offset scatter plots
plot_offset_scatter(kml_x_offsets, kml_y_offsets, "KML")
plot_offset_scatter(ifc_x_offsets, ifc_y_offsets, "IFC")

print("Analysis complete!")