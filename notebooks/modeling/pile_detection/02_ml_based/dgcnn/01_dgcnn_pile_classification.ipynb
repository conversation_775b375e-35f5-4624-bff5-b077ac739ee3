from google.colab import drive
drive.mount('/content/drive')

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pickle
import json
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Configuration - Using same dataset as PointNet++ but 3D coords only
batch_size = 16
num_epochs = 50
learning_rate = 0.001
num_points = 1024  # Same as PointNet++ for fair comparison
k = 20  # Number of nearest neighbors
save_model = True

# Baselines to beat
BASELINE_F1 = 0.932  # Rule-based
POINTNET_TARGET = 0.95  # Let's aim higher than PointNet++!

print("=== DGCNN PILE Classification (3D Coords) ===")
print(f"Goal: Beat rule-based F1 of {BASELINE_F1:.3f}")
print(f"Stretch goal: Reach {POINTNET_TARGET:.3f} F1!")
print(f"Using {num_points} points with 3D coordinates only")

from pathlib import Path

def load_pile_data(data_path):
    """Load pile classification data from pickle files - Same as PointNet++"""
    datasets = {}
    file_mapping = {
        'train': 'train_pointnet.pkl',
        'val': 'val_pointnet.pkl',
        'test': 'test_pointnet.pkl'
    }

    for split, filename in file_mapping.items():
        filepath = data_path / filename

        if not filepath.exists():
            print(f"ERROR: {filepath} not found!")
            return None

        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        patches = data['points']
        labels = data['labels']

        # Extract only 3D coordinates for DGCNN
        patches_3d = patches[:, :, :3].astype(np.float32)
        labels = np.array(labels)

        print(f"{split}: {patches_3d.shape}, labels: {len(labels)}")

        datasets[split] = {
            'patches': patches_3d,
            'labels': labels,
            'metadata': data.get('metadata', [])
        }

    return datasets

def farthest_point_sampling(points, num_samples):
    """Farthest Point Sampling to preserve spatial structure"""
    if len(points) <= num_samples:
        return points

    # Start with a random point
    sampled_indices = [np.random.randint(len(points))]
    distances = np.full(len(points), np.inf)

    for _ in range(num_samples - 1):
        # Update distances to the nearest sampled point
        last_point = points[sampled_indices[-1]]
        new_distances = np.linalg.norm(points - last_point, axis=1)
        distances = np.minimum(distances, new_distances)

        # Select the farthest point
        farthest_idx = np.argmax(distances)
        sampled_indices.append(farthest_idx)

    return points[sampled_indices]

def preprocess_patches_for_dgcnn(patches, labels, num_points=1024):
    """Preprocess 3D patches for DGCNN - extract spatial coordinates only"""
    processed_patches = []
    processed_labels = []

    for patch, label in zip(patches, labels):
        patch = np.array(patch, dtype=np.float32)

        # Ensure we have 3D coordinates
        if patch.shape[1] != 3:
            print(f"Warning: Expected 3 coordinates, got {patch.shape[1]}")
            continue

        if len(patch) >= num_points:
            # Use minimal preprocessing since patches are too similar
            sampled = minimal_preprocessing_sampling(patch, num_points)
        else:
            # Structure-aware upsampling: interpolate between existing points
            upsampled = patch.copy()
            needed = num_points - len(patch)

            for _ in range(needed):
                # Select two random points and interpolate
                idx1, idx2 = np.random.choice(len(patch), 2, replace=False)
                # Random interpolation between the two points
                alpha = np.random.uniform(0.2, 0.8)  # Avoid endpoints
                new_point = alpha * patch[idx1] + (1 - alpha) * patch[idx2]
                # Add small noise to avoid exact duplicates
                new_point += np.random.normal(0, 0.01, 3)
                upsampled = np.vstack([upsampled, new_point])

            sampled = upsampled[:num_points]

        # Minimal normalization to preserve geometric differences
        # Only center, do not scale to preserve relative magnitudes
        centroid = np.mean(sampled, axis=0)
        sampled = sampled - centroid

        # Optional: very light scaling to prevent extreme values
        max_extent = np.max(np.abs(sampled))
        if max_extent > 10:  # Only scale if values are very large
            sampled = sampled / max_extent

        processed_patches.append(sampled)
        processed_labels.append(label)

    return np.array(processed_patches), np.array(processed_labels)

# Previous sampling methods (unused - kept for reference):
# - distance_weighted_sampling: center-biased like PointNet++
# - structure_aware_sampling: bimodal distribution for C/I-sections
# Current approach uses minimal_preprocessing_sampling due to high patch similarity

def minimal_preprocessing_sampling(patch, num_points):
    """Minimal preprocessing to preserve maximum geometric information"""
    if len(patch) <= num_points:
        return patch

    # Use farthest point sampling to preserve spatial distribution
    # without any bias that might destroy class differences
    return farthest_point_sampling(patch, num_points)

# Load and preprocess data using PointNet++ data path
GDRIVE_BASE = "/content/drive/MyDrive"
PROJECT_FOLDER = "pointnet_pile_detection"
project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER
data_path = project_path / "pointnet_data"

print("Loading PointNet++ data for DGCNN (3D coords only)...")
datasets = load_pile_data(data_path)

if datasets is None:
    raise ValueError("Failed to load data!")

# Analyze class distribution and patch characteristics
print("=== CLASS DISTRIBUTION ANALYSIS ===")
orig_train_labels = np.array(datasets['train']['labels'])
orig_val_labels = np.array(datasets['val']['labels'])
orig_test_labels = np.array(datasets['test']['labels'])

train_class_counts = np.bincount(orig_train_labels)
val_class_counts = np.bincount(orig_val_labels)
test_class_counts = np.bincount(orig_test_labels)

print(f"Training set:")
print(f"  Non-pile: {train_class_counts[0]} ({train_class_counts[0]/len(orig_train_labels)*100:.1f}%)")
print(f"  Pile: {train_class_counts[1]} ({train_class_counts[1]/len(orig_train_labels)*100:.1f}%)")

print(f"\nValidation set:")
print(f"  Non-pile: {val_class_counts[0]} ({val_class_counts[0]/len(orig_val_labels)*100:.1f}%)")
print(f"  Pile: {val_class_counts[1]} ({val_class_counts[1]/len(orig_val_labels)*100:.1f}%)")

print(f"\nTest set:")
print(f"  Non-pile: {test_class_counts[0]} ({test_class_counts[0]/len(orig_test_labels)*100:.1f}%)")
print(f"  Pile: {test_class_counts[1]} ({test_class_counts[1]/len(orig_test_labels)*100:.1f}%)")

# Calculate class weights for balancing
total_samples = len(orig_train_labels)
class_weights = total_samples / (2 * train_class_counts)
print(f"\nRecommended class weights: Non-pile={class_weights[0]:.3f}, Pile={class_weights[1]:.3f}")

# Analyze geometric characteristics of original patches
print("\n=== GEOMETRIC CHARACTERISTICS ANALYSIS ===")

def analyze_patch_geometry(patches, labels, split_name):
    pile_indices = np.where(labels == 1)[0]
    non_pile_indices = np.where(labels == 0)[0]

    print(f"\n{split_name} Set Geometry:")

    # Sample some patches for analysis (to avoid memory issues)
    max_samples = min(100, len(pile_indices), len(non_pile_indices))
    pile_sample = np.random.choice(pile_indices, max_samples, replace=False)
    non_pile_sample = np.random.choice(non_pile_indices, max_samples, replace=False)

    pile_extents = []
    non_pile_extents = []
    pile_z_std = []
    non_pile_z_std = []

    for idx in pile_sample:
        patch = np.array(patches[idx])
        if len(patch) > 0:
            extent = np.max(patch, axis=0) - np.min(patch, axis=0)
            pile_extents.append(extent)
            pile_z_std.append(np.std(patch[:, 2]))

    for idx in non_pile_sample:
        patch = np.array(patches[idx])
        if len(patch) > 0:
            extent = np.max(patch, axis=0) - np.min(patch, axis=0)
            non_pile_extents.append(extent)
            non_pile_z_std.append(np.std(patch[:, 2]))

    pile_extents = np.array(pile_extents)
    non_pile_extents = np.array(non_pile_extents)

    print(f"  Pile patches - Mean extent (X,Y,Z): ({np.mean(pile_extents[:,0]):.3f}, {np.mean(pile_extents[:,1]):.3f}, {np.mean(pile_extents[:,2]):.3f})")
    print(f"  Non-pile patches - Mean extent (X,Y,Z): ({np.mean(non_pile_extents[:,0]):.3f}, {np.mean(non_pile_extents[:,1]):.3f}, {np.mean(non_pile_extents[:,2]):.3f})")

    print(f"  Pile patches - Mean Z variation: {np.mean(pile_z_std):.3f}")
    print(f"  Non-pile patches - Mean Z variation: {np.mean(non_pile_z_std):.3f}")

    return {
        'pile_extents': pile_extents,
        'non_pile_extents': non_pile_extents,
        'pile_z_std': pile_z_std,
        'non_pile_z_std': non_pile_z_std
    }

# Analyze original patches before preprocessing
train_geom = analyze_patch_geometry(datasets['train']['patches'], orig_train_labels, "Training")

print("\nKey insights:")
height_diff = np.mean(train_geom['pile_extents'][:,2]) - np.mean(train_geom['non_pile_extents'][:,2])
z_var_diff = np.mean(train_geom['pile_z_std']) - np.mean(train_geom['non_pile_z_std'])
print(f"  Height difference: {height_diff:.3f}")
print(f"  Z-variation difference: {z_var_diff:.3f}")

# Check if patches are too similar
if abs(height_diff) < 0.01 and abs(z_var_diff) < 0.01:
    print("\nWARNING: Patches are geometrically very similar.")
    print("This may cause classification difficulties.")
    print("Using minimal preprocessing to preserve differences.")

# Store class weights for later use
CLASS_WEIGHTS = class_weights
print(f"\nClass weights: Non-pile={CLASS_WEIGHTS[0]:.2f}, Pile={CLASS_WEIGHTS[1]:.2f}")

print("\nPreprocessing patches for DGCNN...")
print("Using minimal preprocessing due to high patch similarity.")
train_patches, train_labels = preprocess_patches_for_dgcnn(
    datasets['train']['patches'],
    datasets['train']['labels'],
    num_points
)
val_patches, val_labels = preprocess_patches_for_dgcnn(
    datasets['val']['patches'],
    datasets['val']['labels'],
    num_points
)
test_patches, test_labels = preprocess_patches_for_dgcnn(
    datasets['test']['patches'],
    datasets['test']['labels'],
    num_points
)

print(f"\nFinal shapes:")
for split, data in zip(['Train', 'Val', 'Test'],
                       [(train_patches, train_labels), (val_patches, val_labels), (test_patches, test_labels)]):
    print(f"  {split}: {data[0].shape} patches, {data[1].shape} labels")

# Check class balance and data quality
for split, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:
    pile_rate = np.mean(labels) * 100
    print(f"  {split}: {pile_rate:.1f}% pile samples")

# Verify 3D coordinate data
sample_patch = train_patches[0]
print(f"\nData verification:")
print(f"  Shape: {sample_patch.shape}")
print(f"  Expected: ({num_points}, 3)")
print(f"  Coordinate ranges: {sample_patch.min():.3f} to {sample_patch.max():.3f}")
print(f"  Mean distance from origin: {np.mean(np.linalg.norm(sample_patch, axis=1)):.3f}")

# Quick peek at the data
pile_indices = np.where(train_labels == 1)[0]
non_pile_indices = np.where(train_labels == 0)[0]

if len(pile_indices) > 0 and len(non_pile_indices) > 0:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5), subplot_kw={'projection': '3d'})

    # Pile sample
    pile_patch = train_patches[pile_indices[0]]
    ax1.scatter(pile_patch[:, 0], pile_patch[:, 1], pile_patch[:, 2], s=1, c='red', alpha=0.6)
    ax1.set_title("Pile Patch")

    # Non-pile sample
    non_pile_patch = train_patches[non_pile_indices[0]]
    ax2.scatter(non_pile_patch[:, 0], non_pile_patch[:, 1], non_pile_patch[:, 2], s=1, c='blue', alpha=0.6)
    ax2.set_title("Non-pile Patch")

    plt.tight_layout()
    plt.show()
else:
    print("No pile or non-pile samples found for visualization")
    print(f"Pile samples: {len(pile_indices)}, Non-pile samples: {len(non_pile_indices)}")


# Get pile and non-pile indices
pile_indices = np.where(train_labels == 1)[0]
non_pile_indices = np.where(train_labels == 0)[0]

# === ORIGINAL PATCHES (Before Processing) ===
print("=== ORIGINAL PATCHES (Before Processing) ===")
if len(pile_indices) > 0 and len(non_pile_indices) > 0:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5), subplot_kw={'projection': '3d'})

    # Show ORIGINAL patches from datasets (before preprocessing)
    orig_pile_patch = datasets['train']['patches'][pile_indices[0]]
    orig_non_pile_patch = datasets['train']['patches'][non_pile_indices[0]]

    # Pile patch (original)
    ax1.scatter(orig_pile_patch[:, 0], orig_pile_patch[:, 1], orig_pile_patch[:, 2],
                s=1, c='red', alpha=0.6)
    ax1.set_title("Original Positive Patch (Pile)")

    # Non-pile patch (original)
    ax2.scatter(orig_neg_patch[:, 0], orig_neg_patch[:, 1], orig_neg_patch[:, 2],
                s=1, c='blue', alpha=0.6)
    ax2.set_title("Original Negative Patch (Non-pile)")

    plt.tight_layout()
    plt.show()

# === PROCESSED PATCHES (After Current Preprocessing) ===
print("=== PROCESSED PATCHES (After Current Preprocessing) ===")
if len(pos_indices) > 0 and len(neg_indices) > 0:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5), subplot_kw={'projection': '3d'})

    # Positive sample
    pos_patch = train_patches[pos_indices[0]]
    ax1.scatter(pos_patch[:, 0], pos_patch[:, 1], pos_patch[:, 2], s=1, c='red', alpha=0.6)
    ax1.set_title("Processed Positive Patch (Pile)")

    # Negative sample
    neg_patch = train_patches[neg_indices[0]]
    ax2.scatter(neg_patch[:, 0], neg_patch[:, 1], neg_patch[:, 2], s=1, c='blue', alpha=0.6)
    ax2.set_title("Processed Negative Patch (Non-pile)")

    plt.tight_layout()
    plt.show()
else:
    print("No positive or negative samples found for visualization")
    print(f"Positive samples: {len(pos_indices)}, Negative samples: {len(neg_indices)}")


class PileDataset(Dataset):
    def __init__(self, points, labels):
        self.points = torch.FloatTensor(points)
        self.labels = torch.LongTensor(labels)

    def __len__(self):
        return len(self.points)

    def __getitem__(self, idx):
        return self.points[idx], self.labels[idx]


# Create datasets
train_dataset = PileDataset(train_patches, train_labels)
val_dataset = PileDataset(val_patches, val_labels)
test_dataset = PileDataset(test_patches, test_labels)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"Datasets ready!")
print(f"  Train: {len(train_dataset)} | Val: {len(val_dataset)} | Test: {len(test_dataset)}")


def knn(x, k):
    """Find k nearest neighbors - building our dynamic graph!"""
    inner = -2 * torch.matmul(x.transpose(2, 1), x)
    xx = torch.sum(x**2, dim=1, keepdim=True)
    pairwise_distance = -xx - inner - xx.transpose(2, 1)

    idx = pairwise_distance.topk(k=k, dim=-1)[1]
    return idx


def get_graph_feature(x, k=20, idx=None):
    """Create graph features from k-NN - this is where the magic happens!"""
    batch_size = x.size(0)
    num_points = x.size(2)
    x = x.view(batch_size, -1, num_points)

    if idx is None:
        idx = knn(x, k=k)

    device = x.device
    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points
    idx = idx + idx_base
    idx = idx.view(-1)

    _, num_dims, _ = x.size()
    x = x.transpose(2, 1).contiguous()
    feature = x.view(batch_size * num_points, -1)[idx, :]
    feature = feature.view(batch_size, num_points, k, num_dims)
    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)

    # Edge features: [relative_pos, absolute_pos]
    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()

    return feature


class EdgeConv(nn.Module):
    """EdgeConv layer - learns from local graph structure!"""
    def __init__(self, in_channels, out_channels, k=20):
        super(EdgeConv, self).__init__()
        self.k = k
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.LeakyReLU(negative_slope=0.2)
        )

    def forward(self, x):
        x = get_graph_feature(x, k=self.k)
        x = self.conv(x)
        x = x.max(dim=-1, keepdim=False)[0]  # Max pooling over neighbors
        return x


class DGCNN(nn.Module):
    """Dynamic Graph CNN - let's see what you got!"""
    def __init__(self, num_classes=2, k=20):
        super(DGCNN, self).__init__()
        self.k = k

        # EdgeConv layers - getting progressively more abstract
        self.conv1 = EdgeConv(3, 64, k)       # 3D coords -> 64 features
        self.conv2 = EdgeConv(64, 64, k)      # 64 -> 64
        self.conv3 = EdgeConv(64, 128, k)     # 64 -> 128
        self.conv4 = EdgeConv(128, 256, k)    # 128 -> 256

        # Global feature aggregation
        self.conv5 = nn.Sequential(
            nn.Conv1d(512, 1024, kernel_size=1, bias=False),  # 64+64+128+256=512
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(negative_slope=0.2)
        )

        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.LeakyReLU(negative_slope=0.2),
            nn.Dropout(0.4),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.LeakyReLU(negative_slope=0.2),
            nn.Dropout(0.4),
            nn.Linear(256, num_classes)
        )

    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (B, 3, N)

        # Multi-scale EdgeConv features
        x1 = self.conv1(x)      # (B, 64, N)
        x2 = self.conv2(x1)     # (B, 64, N)
        x3 = self.conv3(x2)     # (B, 128, N)
        x4 = self.conv4(x3)     # (B, 256, N)

        # Concatenate all features - multi-scale representation!
        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)

        # Global feature extraction
        x = self.conv5(x)  # (B, 1024, N)
        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)

        # Final classification
        x = self.classifier(x)

        return x

# Initialize with class balancing
model = DGCNN(num_classes=2, k=k).to(device)

# Apply aggressive class weights to address class imbalance
# Increase non-pile weight to improve minority class detection
aggressive_weights = np.array([CLASS_WEIGHTS[0] * 3.0, CLASS_WEIGHTS[1] * 0.5])
class_weights_tensor = torch.FloatTensor(aggressive_weights).to(device)
criterion = nn.CrossEntropyLoss(weight=class_weights_tensor)
optimizer = optim.Adam(model.parameters(), lr=learning_rate)

print(f"Applied aggressive class weights: Non-pile={aggressive_weights[0]:.2f}, Pile={aggressive_weights[1]:.2f}")

param_count = sum(p.numel() for p in model.parameters())
print(f"DGCNN ready with {param_count:,} parameters")
print(f"Using k={k} nearest neighbors for graph construction")

def train_epoch(model, loader, criterion, optimizer, device):
    """Train for one epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    for data, target in loader:
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)

    return total_loss / len(loader), correct / total

def validate_epoch(model, loader, criterion, device):
    """Validation check"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)

    return total_loss / len(loader), correct / total


print("Starting DGCNN training...")

train_losses = []
val_losses = []
train_accs = []
val_accs = []

best_val_acc = 0
patience = 10
patience_counter = 0

pbar = tqdm(range(num_epochs), desc="DGCNN Training")

for epoch in pbar:
    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)

    train_losses.append(train_loss)
    val_losses.append(val_loss)
    train_accs.append(train_acc)
    val_accs.append(val_acc)

    # Update progress bar
    pbar.set_postfix({
        'Val Acc': f'{val_acc:.3f}',
        'Best': f'{best_val_acc:.3f}'
    })

    if (epoch + 1) % 10 == 0:
        print(f"\nEpoch {epoch+1}/{num_epochs}:")
        print(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}")
        print(f"  Val: Loss={val_loss:.4f}, Acc={val_acc:.4f}")

    # Early stopping with model saving
    # Note: Using regular accuracy for now, but should implement balanced accuracy
    if val_acc > best_val_acc:
        best_val_acc = val_acc
        patience_counter = 0
        if save_model:
            torch.save(model.state_dict(), 'best_dgcnn.pth')
            print(f"New best model saved! Val Acc: {val_acc:.4f}")
    else:
        patience_counter += 1
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch+1}")
            break

print(f"\nTraining complete! Best validation accuracy: {best_val_acc:.4f}")

# Load best model
if save_model:
    model.load_state_dict(torch.load('best_dgcnn.pth'))
    print("Loaded best model for testing")

# Test evaluation
model.eval()
all_preds = []
all_targets = []

print("Running final test...")
with torch.no_grad():
    for data, target in test_loader:
        data, target = data.to(device), target.to(device)
        output = model(data)
        pred = output.argmax(dim=1)

        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target.cpu().numpy())

# Calculate all the metrics!
test_accuracy = accuracy_score(all_targets, all_preds)
test_precision = precision_score(all_targets, all_preds)
test_recall = recall_score(all_targets, all_preds)
test_f1 = f1_score(all_targets, all_preds)

print("\n=== DGCNN TEST RESULTS ===")
print(f"Accuracy:  {test_accuracy:.4f}")
print(f"Precision: {test_precision:.4f}")
print(f"Recall:    {test_recall:.4f}")
print(f"F1-Score:  {test_f1:.4f}")


from collections import Counter
import numpy as np

pred_dist = Counter(all_preds)
target_dist = Counter(all_targets)

print("=== PREDICTION ANALYSIS ===")
print(f"Actual pile samples: {target_dist[1]} ({target_dist[1]/len(all_targets)*100:.1f}%)")
print(f"Predicted pile samples: {pred_dist[1]} ({pred_dist[1]/len(all_preds)*100:.1f}%)")

# Check prediction distribution
if pred_dist[1]/len(all_preds) > 0.95:
    print("Model shows strong bias toward pile class")
else:
    print("Model shows balanced class discrimination")

# Per-class metrics
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

print("\n=== PER-CLASS PERFORMANCE ===")
print(classification_report(all_targets, all_preds, target_names=['Non-pile', 'Pile']))

# Confusion Matrix
cm = confusion_matrix(all_targets, all_preds)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=['Non-pile', 'Pile'],
            yticklabels=['Non-pile', 'Pile'])
plt.title('DGCNN Confusion Matrix')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.tight_layout()
plt.show()

print(f"\nConfusion Matrix:")
print(f"True Non-pile: {cm[0,0]}")
print(f"False Pile: {cm[0,1]}")
print(f"False Non-pile: {cm[1,0]}")
print(f"True Pile: {cm[1,1]}")

# Plot training history
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

# Loss plot
epochs = range(1, len(train_losses) + 1)
ax1.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
ax1.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
ax1.set_title('DGCNN Training and Validation Loss')
ax1.set_xlabel('Epoch')
ax1.set_ylabel('Loss')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Accuracy plot
ax2.plot(epochs, train_accs, 'b-', label='Training Accuracy', linewidth=2)
ax2.plot(epochs, val_accs, 'r-', label='Validation Accuracy', linewidth=2)
ax2.set_title('DGCNN Training and Validation Accuracy')
ax2.set_xlabel('Epoch')
ax2.set_ylabel('Accuracy')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Training summary
print(f"\n=== TRAINING SUMMARY ===")
print(f"Total epochs: {len(train_losses)}")
print(f"Best validation accuracy: {best_val_acc:.4f}")
print(f"Final training loss: {train_losses[-1]:.4f}")
print(f"Final validation loss: {val_losses[-1]:.4f}")
print(f"Final training accuracy: {train_accs[-1]:.4f}")
print(f"Final validation accuracy: {val_accs[-1]:.4f}")

# Set up file paths for spatial analysis (same as PointNet++)
import geopandas as gpd
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

GDRIVE_BASE = "/content/drive/MyDrive"
PROJECT_FOLDER = "pointnet_pile_detection"

project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER
data_path = project_path / "pointnet_data"

ifc_path = project_path / "GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
kml_path = project_path / "pile.kml"
test_pkl_path = data_path / "test_pointnet.pkl"
harmonized_pile_dataset = project_path / "harmonized_pile_dataset_final.csv"

print(f"Using data paths:")
print(f"  IFC: {ifc_path}")
print(f"  KML: {kml_path}")
print(f"  Test data: {test_pkl_path}")
print(f"  Pile dataset: {harmonized_pile_dataset}")

def create_dgcnn_spatial_plot(all_preds, all_targets, test_pkl_path, results_path, metrics):
    """Create DGCNN spatial plot using the same method as PointNet++."""

    # Load reference data
    ifc_df = pd.read_csv(ifc_path)
    ifc_coords = ifc_df[['X', 'Y']].values

    gdf_kml = gpd.read_file(kml_path, driver='KML')
    gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)
    gdf_kml['geometry'] = gdf_kml.geometry.centroid
    kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)

    # Load the original pile dataset
    pile_df = pd.read_csv(harmonized_pile_dataset)

    # Load test metadata
    with open(test_pkl_path, 'rb') as f:
        test_data = pickle.load(f)
    test_metadata = test_data.get('metadata', [])

    # Reconstruct coordinates using pile IDs
    pred_coords = []
    valid_indices = []

    for i, meta in enumerate(test_metadata[:len(all_preds)]):
        pile_id = meta.get('pile_id') if isinstance(meta, dict) else None

        if pile_id and pile_id in pile_df['pile_id'].values:
            pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]
            pred_coords.append([pile_row['x'], pile_row['y']])
            valid_indices.append(i)

    if len(pred_coords) == 0:
        print("ERROR: No pile IDs found in metadata!")
        return

    pred_coords = np.array(pred_coords)

    # Filter predictions to match valid coordinates
    all_preds_filtered = [all_preds[i] for i in valid_indices]
    all_targets_filtered = [all_targets[i] for i in valid_indices]

    # Calculate accuracy for SPATIAL subset (for display purposes)
    correct_mask = (np.array(all_targets_filtered) == np.array(all_preds_filtered))
    error_mask = ~correct_mask
    spatial_accuracy = np.mean(correct_mask)

    # Use the TRUE accuracy from full test set for title
    true_accuracy = metrics['accuracy']

    print(f"Reconstructed {len(pred_coords)} coordinates from pile IDs")
    print(f"Spatial subset accuracy: {spatial_accuracy:.3f} ({len(pred_coords)} samples)")
    print(f"Full test set accuracy: {true_accuracy:.3f} ({len(all_preds)} samples)")
    print(f"Coordinate range: X[{pred_coords[:, 0].min():.0f}, {pred_coords[:, 0].max():.0f}], Y[{pred_coords[:, 1].min():.0f}, {pred_coords[:, 1].max():.0f}]")

    # Create plot exactly like PointNet++
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))

    # Plot reference data
    ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1],
              s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')
    ax.scatter(kml_coords[:, 0], kml_coords[:, 1],
              s=8, alpha=0.7, color='green', marker='s', label='KML Ground Truth')

    # Plot predictions
    if np.sum(correct_mask) > 0:
        ax.scatter(pred_coords[correct_mask, 0], pred_coords[correct_mask, 1],
                  c='blue', s=30, alpha=0.8, marker='o',
                  label=f'Correct ({np.sum(correct_mask)})')

    if np.sum(error_mask) > 0:
        ax.scatter(pred_coords[error_mask, 0], pred_coords[error_mask, 1],
                  c='red', s=50, alpha=0.9, marker='x', linewidth=2,
                  label=f'Errors ({np.sum(error_mask)})')

    # Use TRUE accuracy in title, but show spatial stats in legend
    ax.set_title(f'DGCNN\nAccuracy: {true_accuracy:.3f}')
    ax.set_xlabel('X (UTM)')
    ax.set_ylabel('Y (UTM)')
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal', adjustable='box')

    # Add text box with full stats
    stats_text = f'Full Test Set:\n{len(all_preds)} samples\n{true_accuracy:.1%} accuracy\n\nSpatial Subset:\n{len(pred_coords)} samples\n{spatial_accuracy:.1%} accuracy'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig('/content/drive/MyDrive/dgcnn_results/dgcnn_spatial_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()

    return {
        'spatial_accuracy': spatial_accuracy,
        'spatial_samples': len(pred_coords),
        'true_accuracy': true_accuracy,
        'total_samples': len(all_preds)
    }

# Create spatial analysis
dgcnn_metrics = {
    'accuracy': test_accuracy,
    'precision': test_precision,
    'recall': test_recall,
    'f1_score': test_f1
}

spatial_results = create_dgcnn_spatial_plot(all_preds, all_targets, test_pkl_path,
                                           Path('/content/drive/MyDrive/dgcnn_results'),
                                           dgcnn_metrics)

print(f"\nSpatial analysis complete. Results saved to Google Drive.")