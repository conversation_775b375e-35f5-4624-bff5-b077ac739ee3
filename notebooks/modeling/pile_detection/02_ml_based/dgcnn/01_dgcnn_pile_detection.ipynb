{"cells": [{"cell_type": "markdown", "metadata": {"id": "UnkeVcpAGr_W"}, "source": ["# DGCNN Pile Detection - 3D Coordinates\n", "\n", "This notebook implements Dynamic Graph CNN (DGCNN) architecture for pile detection using the same dataset as PointNet++ but focusing on 3D spatial coordinates only.\n", "\n", "**Key Differences from PointNet++:**\n", "- Uses only 3D coordinates (x, y, z) instead of 20 features\n", "- Dynamic graph construction with k-nearest neighbors\n", "- EdgeConv operations for local feature aggregation\n", "- Focuses on geometric relationships rather than feature engineering\n", "\n", "**Architecture:**\n", "- Input: (batch_size, 1024, 3) - 3D coordinates\n", "- Multi-scale EdgeConv layers\n", "- Global feature aggregation\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rOY3P6NvG910", "outputId": "19922744-96d4-4215-ac1d-0720eb1b17c3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "glNVwMDlHDqV", "outputId": "04d1b050-4d02-4efc-b5e2-0d3bb848c7c3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import pickle\n", "import json\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "spZHMrrQHHb5", "outputId": "1674df58-3a36-4f27-e3f7-43514c74d848"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DGCNN PILE DETECTION ===\n", "Goal: Beat rule-based F1 of 0.932\n", "<PERSON><PERSON>ch goal: Reach 0.950 F1!\n"]}], "source": ["# Configuration - Using same dataset as PointNet++ but 3D coords only\n", "batch_size = 16\n", "num_epochs = 50\n", "learning_rate = 0.001\n", "num_points = 1024  # Same as PointNet++ for fair comparison\n", "k = 20  # Number of nearest neighbors\n", "save_model = True\n", "\n", "# <PERSON><PERSON> to beat\n", "BASELINE_F1 = 0.932  # Rule-based\n", "POINTNET_TARGET = 0.95  # Let's aim higher than PointNet++!\n", "\n", "print(\"=== DGCNN PILE DETECTION (3D Coords) ===\")\n", "print(f\"Goal: Beat rule-based F1 of {BASELINE_F1:.3f}\")\n", "print(f\"Stretch goal: Reach {POINTNET_TARGET:.3f} F1!\")\n", "print(f\"Using {num_points} points with 3D coordinates only\")"]}, {"cell_type": "markdown", "metadata": {"id": "VxoHtNenHTBX"}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "vFuzcjxvHMGj"}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "def load_pile_data(data_path):\n", "    \"\"\"Load pile detection data from pickle files - Same as PointNet++\"\"\"\n", "    datasets = {}\n", "    file_mapping = {\n", "        'train': 'train_pointnet.pkl',\n", "        'val': 'val_pointnet.pkl',\n", "        'test': 'test_pointnet.pkl'\n", "    }\n", "\n", "    for split, filename in file_mapping.items():\n", "        filepath = data_path / filename\n", "\n", "        if not filepath.exists():\n", "            print(f\"ERROR: {filepath} not found!\")\n", "            return None\n", "\n", "        with open(filepath, 'rb') as f:\n", "            data = pickle.load(f)\n", "\n", "        patches = data['points']\n", "        labels = data['labels']\n", "\n", "        # Extract only 3D coordinates for DGCNN\n", "        patches_3d = patches[:, :, :3].astype(np.float32)\n", "        labels = np.array(labels)\n", "\n", "        print(f\"{split}: {patches_3d.shape}, labels: {len(labels)}\")\n", "\n", "        datasets[split] = {\n", "            'patches': patches_3d,\n", "            'labels': labels,\n", "            'metadata': data.get('metadata', [])\n", "        }\n", "\n", "    return datasets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🔧 **Critical Fix: Spatial-Aware Sampling**\n", "\n", "**Problem**: Random sampling was destroying geometric structure!\n", "```python\n", "# OLD (destroys structure):\n", "sampled_indices = np.random.choice(len(patch), num_points, replace=False)\n", "```\n", "\n", "**Solution**: Farthest Point Sampling preserves spatial distribution\n", "```python\n", "# NEW (preserves structure):\n", "sampled = farthest_point_sampling(patch, num_points)\n", "```\n", "\n", "This ensures pile patches maintain their **linear/cylindrical structure** while non-pile patches keep their **scattered distribution**."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "oy92xNBeHYcT"}, "outputs": [], "source": ["def farthest_point_sampling(points, num_samples):\n", "    \"\"\"Farthest Point Sampling to preserve spatial structure\"\"\"\n", "    if len(points) <= num_samples:\n", "        return points\n", "    \n", "    # Start with a random point\n", "    sampled_indices = [np.random.randint(len(points))]\n", "    distances = np.full(len(points), np.inf)\n", "    \n", "    for _ in range(num_samples - 1):\n", "        # Update distances to the nearest sampled point\n", "        last_point = points[sampled_indices[-1]]\n", "        new_distances = np.linalg.norm(points - last_point, axis=1)\n", "        distances = np.minimum(distances, new_distances)\n", "        \n", "        # Select the farthest point\n", "        farthest_idx = np.argmax(distances)\n", "        sampled_indices.append(farthest_idx)\n", "    \n", "    return points[sampled_indices]\n", "\n", "def preprocess_patches_for_dgcnn(patches, labels, num_points=1024):\n", "    \"\"\"Preprocess 3D patches for DGCNN - extract spatial coordinates only\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    for patch, label in zip(patches, labels):\n", "        patch = np.array(patch, dtype=np.float32)\n", "        \n", "        # Ensure we have 3D coordinates\n", "        if patch.shape[1] != 3:\n", "            print(f\"Warning: Expected 3 coordinates, got {patch.shape[1]}\")\n", "            continue\n", "\n", "        if len(patch) >= num_points:\n", "            # Spatial-aware sampling: preserve geometric structure\n", "            # Use farthest point sampling to maintain spatial distribution\n", "            sampled = farthest_point_sampling(patch, num_points)\n", "        else:\n", "            # Structure-aware upsampling: interpolate between existing points\n", "            upsampled = patch.copy()\n", "            needed = num_points - len(patch)\n", "\n", "            for _ in range(needed):\n", "                # Select two random points and interpolate\n", "                idx1, idx2 = np.random.choice(len(patch), 2, replace=False)\n", "                # Random interpolation between the two points\n", "                alpha = np.random.uniform(0.2, 0.8)  # Avoid endpoints\n", "                new_point = alpha * patch[idx1] + (1 - alpha) * patch[idx2]\n", "                # Add small noise to avoid exact duplicates\n", "                new_point += np.random.normal(0, 0.01, 3)\n", "                upsampled = np.vstack([upsampled, new_point])\n", "\n", "            sampled = upsampled[:num_points]\n", "\n", "        # Center the patch (preserve relative geometry)\n", "        centroid = np.mean(sampled, axis=0)\n", "        sampled = sampled - centroid\n", "        \n", "        # Scale by standard deviation to preserve shape differences\n", "        # This maintains geometric distinctions between pile/non-pile\n", "        std_dev = np.std(sampled)\n", "        if std_dev > 0:\n", "            sampled = sampled / std_dev\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    return np.array(processed_patches), np.array(processed_labels)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🔧 **Normalization Fix Applied**\n", "\n", "**Previous Issue**: Extent-based normalization was making all patches look similar\n", "```python\n", "# OLD (problematic):\n", "spatial_extent = np.max(np.linalg.norm(sampled, axis=1))\n", "sampled /= spatial_extent  # Made all patches same 'size'\n", "```\n", "\n", "**Current Solution**: Centroid + std-dev normalization preserves geometric differences\n", "```python\n", "# NEW (preserves shape):\n", "centroid = np.mean(sampled, axis=0)\n", "sampled = sampled - centroid  # Center\n", "sampled = sampled / np.std(sampled)  # Scale by variation\n", "```\n", "\n", "This maintains the **geometric distinctions** between pile and non-pile patches!"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uKHzvD2eHg-F", "outputId": "c7ed5b31-711f-4b3a-bd0b-b735547bdfdd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data from: /content/drive/MyDrive/Colab Notebooks/data/patches_20250722_160244\n", "  train: 3345 patches\n", "  val: 717 patches\n", "  test: 717 patches\n", "Preprocessing patches for DGCNN...\n", "\n", "Final shapes:\n", "  Train: (3345, 256, 3) patches, (3345,) labels\n", "  Val: (717, 256, 3) patches, (717,) labels\n", "  Test: (717, 256, 3) patches, (717,) labels\n"]}], "source": ["# Load and preprocess data using PointNet++ data path\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER\n", "data_path = project_path / \"pointnet_data\"\n", "\n", "print(\"Loading PointNet++ data for DGCNN (3D coords only)...\")\n", "datasets = load_pile_data(data_path)\n", "\n", "if datasets is None:\n", "    raise ValueError(\"Failed to load data!\")\n", "\n", "print(\"\\nPreprocessing patches for DGCNN...\")\n", "train_patches, train_labels = preprocess_patches_for_dgcnn(\n", "    datasets['train']['patches'], \n", "    datasets['train']['labels'], \n", "    num_points\n", ")\n", "val_patches, val_labels = preprocess_patches_for_dgcnn(\n", "    datasets['val']['patches'], \n", "    datasets['val']['labels'], \n", "    num_points\n", ")\n", "test_patches, test_labels = preprocess_patches_for_dgcnn(\n", "    datasets['test']['patches'], \n", "    datasets['test']['labels'], \n", "    num_points\n", ")\n", "\n", "print(f\"\\nFinal shapes:\")\n", "for split, data in zip(['<PERSON>', 'Val', 'Test'],\n", "                       [(train_patches, train_labels), (val_patches, val_labels), (test_patches, test_labels)]):\n", "    print(f\"  {split}: {data[0].shape} patches, {data[1].shape} labels\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UQ5x3hgLHmGh", "outputId": "7d71a87a-ebe2-4762-e824-2b8e6d1b20c6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Train: 80.2% positive samples\n", "  Val: 80.3% positive samples\n", "  Test: 80.2% positive samples\n"]}], "source": ["# Check class balance and data quality\n", "for split, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:\n", "    pos_rate = np.mean(labels) * 100\n", "    print(f\"  {split}: {pos_rate:.1f}% positive samples\")\n", "\n", "# Verify 3D coordinate data\n", "sample_patch = train_patches[0]\n", "print(f\"\\nData verification:\")\n", "print(f\"  Shape: {sample_patch.shape}\")\n", "print(f\"  Expected: ({num_points}, 3)\")\n", "print(f\"  Coordinate ranges: {sample_patch.min():.3f} to {sample_patch.max():.3f}\")\n", "print(f\"  Mean distance from origin: {np.mean(np.linalg.norm(sample_patch, axis=1)):.3f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 511}, "id": "AmOyy5x8HkXm", "outputId": "3d9a0559-5473-4724-a743-cf1eaf6df5ec"}, "outputs": [{"data": {"image/png": "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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Quick peek at the data\n", "pos_indices = np.where(train_labels == 1)[0]\n", "neg_indices = np.where(train_labels == 0)[0]\n", "\n", "if len(pos_indices) > 0 and len(neg_indices) > 0:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5), subplot_kw={'projection': '3d'})\n", "\n", "    # Positive sample\n", "    pos_patch = train_patches[pos_indices[0]]\n", "    ax1.scatter(pos_patch[:, 0], pos_patch[:, 1], pos_patch[:, 2], s=1, c='red', alpha=0.6)\n", "    ax1.set_title(\"Positive Patch (Pile)\")\n", "\n", "    # Negative sample\n", "    neg_patch = train_patches[neg_indices[0]]\n", "    ax2.scatter(neg_patch[:, 0], neg_patch[:, 1], neg_patch[:, 2], s=1, c='blue', alpha=0.6)\n", "    ax2.set_title(\"Negative Patch (Non-pile)\")\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No positive or negative samples found for visualization\")\n", "    print(f\"Positive samples: {len(pos_indices)}, Negative samples: {len(neg_indices)}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "w22ddG_8HsCY"}, "source": ["## Dataset & DataLoader\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "EFb9xMAnHv2d"}, "outputs": [], "source": ["class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5GEeXCb9H0v-", "outputId": "eb3d8945-f5c9-4b3b-dcf4-8dedc4badde5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Datasets ready!\n", "  Train: 3345 | Val: 717 | Test: 717\n"]}], "source": ["# Create datasets\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "# Create dataloaders\n", "train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Datasets ready!\")\n", "print(f\"  Train: {len(train_dataset)} | Val: {len(val_dataset)} | Test: {len(test_dataset)}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "_2K6tCZ9H9e7"}, "source": ["## DGCNN Architecture\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "DbnP8P5sH_4s"}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors - building our dynamic graph!\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "-ONkHYoiIB6U"}, "outputs": [], "source": ["def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN - this is where the magic happens!\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "\n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "\n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "\n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "\n", "    # Edge features: [relative_pos, absolute_pos]\n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "\n", "    return feature\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "-_owMx76IEvY"}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    \"\"\"EdgeConv layer - learns from local graph structure!\"\"\"\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]  # Max pooling over neighbors\n", "        return x\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "EPGN5G7BIHFE"}, "outputs": [], "source": ["class DGCNN(nn.Module):\n", "    \"\"\"Dynamic Graph CNN - let's see what you got!\"\"\"\n", "    def __init__(self, num_classes=2, k=20):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        # EdgeConv layers - getting progressively more abstract\n", "        self.conv1 = EdgeConv(3, 64, k)       # 3D coords -> 64 features\n", "        self.conv2 = EdgeConv(64, 64, k)      # 64 -> 64\n", "        self.conv3 = EdgeConv(64, 128, k)     # 64 -> 128\n", "        self.conv4 = EdgeConv(128, 256, k)    # 128 -> 256\n", "\n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(512, 1024, kernel_size=1, bias=False),  # 64+64+128+256=512\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.4),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.4),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = x.transpose(2, 1)  # (B, 3, N)\n", "\n", "        # Multi-scale EdgeConv features\n", "        x1 = self.conv1(x)      # (B, 64, N)\n", "        x2 = self.conv2(x1)     # (B, 64, N)\n", "        x3 = self.conv3(x2)     # (B, 128, N)\n", "        x4 = self.conv4(x3)     # (B, 256, N)\n", "\n", "        # Concatenate all features - multi-scale representation!\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "\n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "\n", "        # Final classification\n", "        x = self.classifier(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "XM8vijOzIK9Y"}, "source": ["## Model Training"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "csyW86I9IOb8", "outputId": "a1409ce8-85ab-46c1-fb8b-a012911d589a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN ready with 1,276,034 parameters\n", "Using k=20 nearest neighbors for graph construction\n"]}], "source": ["# Initialize\n", "model = DGCNN(num_classes=2, k=k).to(device)\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "param_count = sum(p.numel() for p in model.parameters())\n", "print(f\"DGCNN ready with {param_count:,} parameters\")\n", "print(f\"Using k={k} nearest neighbors for graph construction\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "eAddIiKGIS8S"}, "outputs": [], "source": ["def train_epoch(model, loader, criterion, optimizer, device):\n", "    \"\"\"Train for one epoch\"\"\"\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for data, target in loader:\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "w33yCHzAIVur"}, "outputs": [], "source": ["def validate_epoch(model, loader, criterion, device):\n", "    \"\"\"Validation check\"\"\"\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1teugZhxIYPS", "outputId": "1edcaf02-b6a7-4d19-875f-dba04cb97543"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting DGCNN training...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:   2%|▏         | 1/50 [00:12<10:01, 12.28s/it, Val Acc=0.900, Best=0.000]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.8996\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:   4%|▍         | 2/50 [00:22<09:00, 11.27s/it, Val Acc=0.919, Best=0.900]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.9191\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:   6%|▌         | 3/50 [00:33<08:38, 11.02s/it, Val Acc=0.922, Best=0.919]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.9219\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:   8%|▊         | 4/50 [00:44<08:25, 10.99s/it, Val Acc=0.927, Best=0.922]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.9275\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:  14%|█▍        | 7/50 [01:18<08:06, 11.33s/it, Val Acc=0.932, Best=0.927]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.9317\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:  16%|█▌        | 8/50 [01:30<08:00, 11.43s/it, Val Acc=0.933, Best=0.932]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.9331\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:  18%|█▊        | 9/50 [01:42<07:51, 11.51s/it, Val Acc=0.937, Best=0.933]"]}, {"name": "stdout", "output_type": "stream", "text": ["New best model saved! Val Acc: 0.9372\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:  20%|██        | 10/50 [01:53<07:38, 11.46s/it, Val Acc=0.932, Best=0.937]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Epoch 10/50:\n", "  Train: Loss=0.1352, Acc=0.9507\n", "  Val: Loss=0.1983, Acc=0.9317\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DGCNN Training:  36%|███▌      | 18/50 [03:35<06:23, 11.98s/it, Val Acc=0.898, Best=0.937]"]}, {"name": "stdout", "output_type": "stream", "text": ["Early stopping at epoch 19\n", "\n", "Training complete! Best validation accuracy: 0.9372\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["print(\"Starting DGCNN training...\")\n", "\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "best_val_acc = 0\n", "patience = 10\n", "patience_counter = 0\n", "\n", "pbar = tqdm(range(num_epochs), desc=\"DGCNN Training\")\n", "\n", "for epoch in pbar:\n", "    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\n", "    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update progress bar\n", "    pbar.set_postfix({\n", "        'Val Acc': f'{val_acc:.3f}',\n", "        'Best': f'{best_val_acc:.3f}'\n", "    })\n", "\n", "    if (epoch + 1) % 10 == 0:\n", "        print(f\"\\nEpoch {epoch+1}/{num_epochs}:\")\n", "        print(f\"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}\")\n", "        print(f\"  Val: Loss={val_loss:.4f}, Acc={val_acc:.4f}\")\n", "\n", "    # Early stopping with model saving\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        patience_counter = 0\n", "        if save_model:\n", "            torch.save(model.state_dict(), 'best_dgcnn.pth')\n", "            print(f\"New best model saved! Val Acc: {val_acc:.4f}\")\n", "    else:\n", "        patience_counter += 1\n", "        if patience_counter >= patience:\n", "            print(f\"Early stopping at epoch {epoch+1}\")\n", "            break\n", "\n", "print(f\"\\nTraining complete! Best validation accuracy: {best_val_acc:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "AYmrWX4wIfDf"}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AYPguGNPIcT0", "outputId": "15d131fb-da22-4feb-e3f1-104f762365de"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded best model for testing\n", "Running final test...\n", "\n", "=== DGCNN TEST RESULTS ===\n", "Accuracy:  0.9289\n", "Precision: 0.9367\n", "Recall:    0.9774\n", "F1-Score:  0.9566\n"]}], "source": ["# Load best model\n", "if save_model:\n", "    model.load_state_dict(torch.load('best_dgcnn.pth'))\n", "    print(\"Loaded best model for testing\")\n", "\n", "# Test evaluation\n", "model.eval()\n", "all_preds = []\n", "all_targets = []\n", "\n", "print(\"Running final test...\")\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        pred = output.argmax(dim=1)\n", "\n", "        all_preds.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "\n", "# Calculate all the metrics!\n", "test_accuracy = accuracy_score(all_targets, all_preds)\n", "test_precision = precision_score(all_targets, all_preds)\n", "test_recall = recall_score(all_targets, all_preds)\n", "test_f1 = f1_score(all_targets, all_preds)\n", "\n", "print(\"\\n=== DGCNN TEST RESULTS ===\")\n", "print(f\"Accuracy:  {test_accuracy:.4f}\")\n", "print(f\"Precision: {test_precision:.4f}\")\n", "print(f\"Recall:    {test_recall:.4f}\")\n", "print(f\"F1-Score:  {test_f1:.4f}\")\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TDUTBsSrZ--R", "outputId": "20a03f19-cef4-4950-dd64-b160b8a877f1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION ANALYSIS ===\n", "True positives: 575 (80.2%)\n", "Predicted positives: 600 (83.7%)\n", "Model shows good discrimination between classes\n", "\n", "=== PER-CLASS PERFORMANCE ===\n", "              precision    recall  f1-score   support\n", "\n", "    Non-pile       0.89      0.73      0.80       142\n", "        <PERSON>le       0.94      0.98      0.96       575\n", "\n", "    accuracy                           0.93       717\n", "   macro avg       0.91      0.85      0.88       717\n", "weighted avg       0.93      0.93      0.93       717\n", "\n"]}], "source": ["from collections import Counter\n", "import numpy as np\n", "\n", "pred_dist = Counter(all_preds)\n", "target_dist = Counter(all_targets)\n", "\n", "print(\"=== PREDICTION ANALYSIS ===\")\n", "print(f\"True positives: {target_dist[1]} ({target_dist[1]/len(all_targets)*100:.1f}%)\")\n", "print(f\"Predicted positives: {pred_dist[1]} ({pred_dist[1]/len(all_preds)*100:.1f}%)\")\n", "\n", "# Check if model is just predicting everything as positive\n", "if pred_dist[1]/len(all_preds) > 0.95:\n", "    print(\"Model might be biased - predicting almost everything as positive\")\n", "else:\n", "    print(\"Model shows good discrimination between classes\")\n", "\n", "# Per-class metrics\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "print(\"\\n=== PER-CLASS PERFORMANCE ===\")\n", "print(classification_report(all_targets, all_preds, target_names=['Non-pile', 'Pile']))\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(all_targets, all_preds)\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Non-pile', 'Pile'], \n", "            yticklabels=['Non-pile', 'Pile'])\n", "plt.title('DGCNN Confusion Matrix')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nConfusion Matrix:\")\n", "print(f\"True Negatives: {cm[0,0]}\")\n", "print(f\"False Positives: {cm[0,1]}\")\n", "print(f\"False Negatives: {cm[1,0]}\")\n", "print(f\"True Positives: {cm[1,1]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training History Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training history\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Loss plot\n", "epochs = range(1, len(train_losses) + 1)\n", "ax1.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)\n", "ax1.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)\n", "ax1.set_title('DGCNN Training and Validation Loss')\n", "ax1.set_xlabel('Epoch')\n", "ax1.set_ylabel('Loss')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Accuracy plot\n", "ax2.plot(epochs, train_accuracies, 'b-', label='Training Accuracy', linewidth=2)\n", "ax2.plot(epochs, val_accuracies, 'r-', label='Validation Accuracy', linewidth=2)\n", "ax2.set_title('DGCNN Training and Validation Accuracy')\n", "ax2.set_xlabel('Epoch')\n", "ax2.set_ylabel('Accuracy')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Training summary\n", "print(f\"\\n=== TRAINING SUMMARY ===\")\n", "print(f\"Total epochs: {len(train_losses)}\")\n", "print(f\"Best validation accuracy: {best_val_acc:.4f}\")\n", "print(f\"Final training loss: {train_losses[-1]:.4f}\")\n", "print(f\"Final validation loss: {val_losses[-1]:.4f}\")\n", "print(f\"Final training accuracy: {train_accuracies[-1]:.4f}\")\n", "print(f\"Final validation accuracy: {val_accuracies[-1]:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Spatial Analysis and Predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Spatial visualization of predictions\n", "import numpy as np\n", "\n", "# Get spatial coordinates from test patches (first 3 features)\n", "test_coords = []\n", "test_true_labels = []\n", "test_pred_labels = []\n", "\n", "# Sample some test patches for visualization\n", "sample_indices = np.random.choice(len(test_patches), min(500, len(test_patches)), replace=False)\n", "\n", "for idx in sample_indices:\n", "    patch = test_patches[idx]\n", "    # Get centroid of patch\n", "    centroid = np.mean(patch, axis=0)  # Average of all points in patch\n", "    test_coords.append(centroid[:3])  # x, y, z coordinates\n", "    test_true_labels.append(all_targets[idx])\n", "    test_pred_labels.append(all_preds[idx])\n", "\n", "test_coords = np.array(test_coords)\n", "test_true_labels = np.array(test_true_labels)\n", "test_pred_labels = np.array(test_pred_labels)\n", "\n", "# Create spatial plots\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Ground truth spatial distribution\n", "pile_mask_true = test_true_labels == 1\n", "ax1.scatter(test_coords[~pile_mask_true, 0], test_coords[~pile_mask_true, 1], \n", "           c='lightblue', alpha=0.6, s=20, label='Non-pile (True)')\n", "ax1.scatter(test_coords[pile_mask_true, 0], test_coords[pile_mask_true, 1], \n", "           c='red', alpha=0.8, s=30, label='<PERSON><PERSON> (True)')\n", "ax1.set_title('Ground Truth Spatial Distribution')\n", "ax1.set_xlabel('X Coordinate')\n", "ax1.set_ylabel('Y Coordinate')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Predicted spatial distribution\n", "pile_mask_pred = test_pred_labels == 1\n", "ax2.scatter(test_coords[~pile_mask_pred, 0], test_coords[~pile_mask_pred, 1], \n", "           c='lightblue', alpha=0.6, s=20, label='Non-pile (Pred)')\n", "ax2.scatter(test_coords[pile_mask_pred, 0], test_coords[pile_mask_pred, 1], \n", "           c='orange', alpha=0.8, s=30, label='Pile (Pred)')\n", "ax2.set_title('DGCNN Predicted Spatial Distribution')\n", "ax2.set_xlabel('X Coordinate')\n", "ax2.set_ylabel('Y Coordinate')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Error analysis spatial plots\n", "correct_mask = test_true_labels == test_pred_labels\n", "incorrect_mask = ~correct_mask\n", "\n", "# Correct predictions\n", "ax3.scatter(test_coords[correct_mask, 0], test_coords[correct_mask, 1], \n", "           c='green', alpha=0.7, s=25, label='Correct Predictions')\n", "ax3.scatter(test_coords[incorrect_mask, 0], test_coords[incorrect_mask, 1], \n", "           c='red', alpha=0.9, s=40, marker='x', label='Incorrect Predictions')\n", "ax3.set_title('DGCNN Prediction Accuracy Spatial Distribution')\n", "ax3.set_xlabel('X Coordinate')\n", "ax3.set_ylabel('Y Coordinate')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Z-coordinate analysis\n", "pile_true_z = test_coords[pile_mask_true, 2]\n", "pile_pred_z = test_coords[pile_mask_pred, 2]\n", "non_pile_true_z = test_coords[~pile_mask_true, 2]\n", "non_pile_pred_z = test_coords[~pile_mask_pred, 2]\n", "\n", "ax4.hist(non_pile_true_z, bins=20, alpha=0.5, label='Non-pile (True)', color='lightblue')\n", "ax4.hist(pile_true_z, bins=20, alpha=0.5, label='Pile (True)', color='red')\n", "ax4.hist(non_pile_pred_z, bins=20, alpha=0.3, label='Non-pile (Pred)', color='blue', histtype='step', linewidth=2)\n", "ax4.hist(pile_pred_z, bins=20, alpha=0.3, label='Pile (Pred)', color='orange', histtype='step', linewidth=2)\n", "ax4.set_title('Z-Coordinate Distribution')\n", "ax4.set_xlabel('Z Coordinate (Height)')\n", "ax4.set_ylabel('Frequency')\n", "ax4.legend()\n", "ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Spatial analysis summary\n", "print(f\"\\n=== SPATIAL ANALYSIS ===\")\n", "print(f\"Total test samples visualized: {len(test_coords)}\")\n", "print(f\"Correct predictions: {np.sum(correct_mask)} ({np.mean(correct_mask)*100:.1f}%)\")\n", "print(f\"Incorrect predictions: {np.sum(incorrect_mask)} ({np.mean(incorrect_mask)*100:.1f}%)\")\n", "print(f\"\\nZ-coordinate statistics:\")\n", "print(f\"  Pile mean Z: {np.mean(pile_true_z):.3f} ± {np.std(pile_true_z):.3f}\")\n", "print(f\"  Non-pile mean Z: {np.mean(non_pile_true_z):.3f} ± {np.std(non_pile_true_z):.3f}\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QNQYjTK-Inch", "outputId": "396738b7-931c-4e47-8cc0-1fd111d1d8ca"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Compare THE MODELS ===\n", "Rule-based F1:  0.9320\n", "DGCNN F1:       0.9566\n", "Improvement over rule-based: ****%\n", "DGCNN WINS against rule-based!\n", "ACHIEVED STRETCH GOAL! F1 > 0.95\n"]}], "source": ["# Compare\n", "print(\"\\n=== Compare THE MODELS ===\")\n", "print(f\"Rule-based F1:  {BASELINE_F1:.4f}\")\n", "print(f\"DGCNN F1:       {test_f1:.4f}\")\n", "\n", "rule_improvement = ((test_f1 - BASELINE_F1) / BASELINE_F1) * 100\n", "print(f\"Improvement over rule-based: {rule_improvement:+.1f}%\")\n", "\n", "if test_f1 > BASELINE_F1:\n", "    print(\"DGCNN WINS against rule-based!\")\n", "else:\n", "    print(\"Rule-based still ahead... need more training?\")\n", "\n", "if test_f1 > POINTNET_TARGET:\n", "    print(f\"ACHIEVED STRETCH GOAL! F1 > {POINTNET_TARGET}\")\n", "else:\n", "    print(f\"Getting there... Target was {POINTNET_TARGET}\")"]}, {"cell_type": "markdown", "metadata": {"id": "l20bgORpI2y3"}, "source": ["## Save Results\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "faNj_DR0I0h2", "outputId": "dd6883f1-a564-41c6-8f2c-36370fcff909"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved to dgcnn_results.json\n"]}], "source": ["# Enhanced results with comprehensive data\n", "results = {\n", "    'model': 'DGCNN_Enhanced',\n", "    'architecture': {\n", "        'input_features': 3,  # 3D coordinates only\n", "        'num_points': num_points,\n", "        'k_neighbors': k,\n", "        'edge_conv_layers': [64, 64, 128, 256],\n", "        'global_features': 1024\n", "    },\n", "    'test_metrics': {\n", "        'accuracy': float(test_accuracy),\n", "        'precision': float(test_precision),\n", "        'recall': float(test_recall),\n", "        'f1_score': float(test_f1)\n", "    },\n", "    'confusion_matrix': {\n", "        'true_negatives': int(cm[0,0]),\n", "        'false_positives': int(cm[0,1]),\n", "        'false_negatives': int(cm[1,0]),\n", "        'true_positives': int(cm[1,1])\n", "    },\n", "    'training_history': {\n", "        'train_losses': [float(x) for x in train_losses],\n", "        'val_losses': [float(x) for x in val_losses],\n", "        'train_accuracies': [float(x) for x in train_accuracies],\n", "        'val_accuracies': [float(x) for x in val_accuracies],\n", "        'num_epochs': len(train_losses),\n", "        'best_val_acc': float(best_val_acc)\n", "    },\n", "    'spatial_analysis': {\n", "        'samples_analyzed': len(test_coords),\n", "        'spatial_accuracy': float(np.mean(correct_mask)),\n", "        'pile_mean_z': float(np.mean(pile_true_z)) if len(pile_true_z) > 0 else 0,\n", "        'non_pile_mean_z': float(np.mean(non_pile_true_z)) if len(non_pile_true_z) > 0 else 0\n", "    },\n", "    'comparison': {\n", "        'rule_based_f1': float(BASELINE_F1),\n", "        'dgcnn_f1': float(test_f1),\n", "        'pointnet_target': float(POINTNET_TARGET),\n", "        'improvement_over_baseline': float(rule_improvement),\n", "        'vs_pointnet_target': 'ACHIEVED' if test_f1 > POINTNET_TARGET else 'NOT_ACHIEVED'\n", "    },\n", "    'predictions': {\n", "        'test_predictions': [int(x) for x in all_preds],\n", "        'test_ground_truth': [int(x) for x in all_targets]\n", "    }\n", "}\n", "\n", "# Save comprehensive results\n", "with open('dgcnn_enhanced_results.json', 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "# Save training history separately for easy plotting\n", "training_data = {\n", "    'epochs': list(range(1, len(train_losses) + 1)),\n", "    'train_loss': train_losses,\n", "    'val_loss': val_losses,\n", "    'train_acc': train_accuracies,\n", "    'val_acc': val_accuracies\n", "}\n", "\n", "with open('dgcnn_training_history.json', 'w') as f:\n", "    json.dump(training_data, f, indent=2)\n", "\n", "print(f\"\\nEnhanced results saved to:\")\n", "print(f\"  - dgcnn_enhanced_results.json (comprehensive metrics)\")\n", "print(f\"  - dgcnn_training_history.json (training curves data)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Comparison and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive model comparison\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"           DGCNN vs BASELINES COMPARISON\")\n", "print(\"=\"*60)\n", "\n", "# Create comparison table\n", "comparison_data = {\n", "    'Model': ['Rule-based', 'DGCNN (3D)', 'PointNet++ Target'],\n", "    'F1 Score': [BASELINE_F1, test_f1, POINTNET_TARGET],\n", "    'Input Features': ['Engineered', '3D Coords', '20 Features'],\n", "    'Architecture': ['Geometric', 'Graph CNN', 'Point CNN']\n", "}\n", "\n", "import pandas as pd\n", "df_comparison = pd.DataFrame(comparison_data)\n", "print(\"\\nPerformance Comparison:\")\n", "print(df_comparison.to_string(index=False))\n", "\n", "# Performance analysis\n", "print(f\"\\n📊 PERFORMANCE ANALYSIS:\")\n", "print(f\"   • DGCNN F1: {test_f1:.4f}\")\n", "print(f\"   • Rule-based F1: {BASELINE_F1:.4f}\")\n", "print(f\"   • Improvement: {rule_improvement:+.1f}%\")\n", "print(f\"   • Target F1: {POINTNET_TARGET:.4f}\")\n", "print(f\"   • Target Status: {'✅ ACHIEVED' if test_f1 > POINTNET_TARGET else '❌ NOT ACHIEVED'}\")\n", "\n", "# Architecture advantages\n", "print(f\"\\n🏗️ DGCNN ADVANTAGES:\")\n", "print(f\"   • Dynamic graph construction adapts to local geometry\")\n", "print(f\"   • EdgeConv captures local neighborhood relationships\")\n", "print(f\"   • Efficient with only 3D coordinates (vs 20 features)\")\n", "print(f\"   • Translation and rotation invariant\")\n", "print(f\"   • Learns geometric patterns directly from spatial data\")\n", "\n", "# Training efficiency\n", "print(f\"\\n⚡ TRAINING EFFICIENCY:\")\n", "print(f\"   • Total epochs: {len(train_losses)}\")\n", "print(f\"   • Best validation accuracy: {best_val_acc:.4f}\")\n", "print(f\"   • Final training accuracy: {train_accuracies[-1]:.4f}\")\n", "print(f\"   • Convergence: {'Good' if abs(train_accuracies[-1] - val_accuracies[-1]) < 0.1 else 'Overfitting detected'}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 407}, "id": "W4H2EYlzI-_Y", "outputId": "92fbb52c-b103-4dcb-ba66-b681d4dc2ff1"}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot training curves\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))\n", "\n", "# Loss curves\n", "ax1.plot(train_losses, label='Train Loss', color='blue', alpha=0.7)\n", "ax1.plot(val_losses, label='Val Loss', color='red', alpha=0.7)\n", "ax1.set_title('Loss Curves')\n", "ax1.set_xlabel('Epoch')\n", "ax1.set_ylabel('Loss')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Accuracy curves\n", "ax2.plot(train_accs, label='Train Acc', color='blue', alpha=0.7)\n", "ax2.plot(val_accs, label='Val Acc', color='red', alpha=0.7)\n", "ax2.axhline(y=BASELINE_F1, color='green', linestyle='--', label='Rule-based F1')\n", "ax2.set_title('Accuracy Curves')\n", "ax2.set_xlabel('Epoch')\n", "ax2.set_ylabel('Accuracy')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HrZqlyK9JDxW", "outputId": "d51716d8-d293-4b1d-dfdc-8abeb6bca313"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== FINAL VERDICT ===\n", "DGCNN is our new champion!\n", "Ready to compare with PointNet++ results!\n"]}], "source": ["print(\"\\n=== FINAL VERDICT ===\")\n", "if test_f1 > BASELINE_F1:\n", "    verdict = \"DGCNN is our new champion!\"\n", "else:\n", "    verdict = \"Rule-based still holding strong...\"\n", "\n", "print(verdict)\n", "print(\"Ready to compare with PointNet++ results!\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fO9OHAQ-K30I", "outputId": "3e95d1a6-713f-4567-bf97-4dd4cbc0aa35"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model and results moved to Google Drive.\n"]}], "source": ["import shutil\n", "from pathlib import Path\n", "\n", "# Destination path – replace with your actual drive mount path if using Colab\n", "dest_dir = Path(\"/content/drive/MyDrive/dgcnn_results/\")\n", "dest_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Move model checkpoint\n", "shutil.move(\"best_dgcnn.pth\", dest_dir / \"best_dgcnn.pth\")\n", "\n", "# Move JSON results\n", "shutil.move(\"dgcnn_results.json\", dest_dir / \"dgcnn_results.json\")\n", "\n", "print(\"Model and results moved to Google Drive.\")\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 0}