{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.004193, "end_time": "2025-08-01T10:56:30.915102", "exception": false, "start_time": "2025-08-01T10:56:30.910909", "status": "completed"}, "tags": []}, "source": ["# DGCNN Site Inference Pipeline\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. Simple exploratory pipeline to test DGCNN on multiple sites.\n", "2. Tests our trained DGCNN model on 3 different construction sites.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:30.919903Z", "iopub.status.busy": "2025-08-01T10:56:30.919724Z", "iopub.status.idle": "2025-08-01T10:56:30.925042Z", "shell.execute_reply": "2025-08-01T10:56:30.924748Z"}, "papermill": {"duration": 0.008839, "end_time": "2025-08-01T10:56:30.926046", "exception": false, "start_time": "2025-08-01T10:56:30.917207", "status": "completed"}, "tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n"]}], "source": ["# Model and processing parameters\n", "SITE_NAME = \"northan_res\"  # Will be overridden by papermill\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"  # Will be overridden\n", "\n", "RUN_NAME = f\"inference_{SITE_NAME}\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference\"\n", "\n", "MODEL_PATH = \"best_dgcnn.pth\"\n", "CONFIDENCE_THRESHOLD = 0.95\n", "BATCH_SIZE = 16\n", "GRID_SPACING = 5.0\n", "PATCH_SIZE = 3.0\n", "NUM_POINTS = 256\n", "K_NEIGHBORS = 20\n", "\n", "EXPERIMENT_NAME = \"dgcnn_inference\"\n", "\n", "# MLflow configuration\n", "\n", "print(\"DGCNN Multi-Site Inference Pipeline\")\n", "print(f\"Site Name: {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "c383f246", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:30.947855Z", "iopub.status.busy": "2025-08-01T10:56:30.947724Z", "iopub.status.idle": "2025-08-01T10:56:30.949502Z", "shell.execute_reply": "2025-08-01T10:56:30.949286Z"}, "papermill": {"duration": 0.005098, "end_time": "2025-08-01T10:56:30.950310", "exception": false, "start_time": "2025-08-01T10:56:30.945212", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference/althea_rpcs\"\n", "RUN_NAME = \"inference_althea_rpcs\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:30.954628Z", "iopub.status.busy": "2025-08-01T10:56:30.954520Z", "iopub.status.idle": "2025-08-01T10:56:34.418488Z", "shell.execute_reply": "2025-08-01T10:56:34.418219Z"}, "papermill": {"duration": 3.467301, "end_time": "2025-08-01T10:56:34.419328", "exception": false, "start_time": "2025-08-01T10:56:30.952027", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import mlflow\n", "import mlflow.pytorch\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:34.423809Z", "iopub.status.busy": "2025-08-01T10:56:34.423001Z", "iopub.status.idle": "2025-08-01T10:56:34.492295Z", "shell.execute_reply": "2025-08-01T10:56:34.491943Z"}, "papermill": {"duration": 0.072141, "end_time": "2025-08-01T10:56:34.493179", "exception": false, "start_time": "2025-08-01T10:56:34.421038", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory created: output_runs/dgcnn_inference/althea_rpcs\n", "MLflow experiment initialized\n"]}], "source": ["# Setup output directory and initialize MLflow\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"Output directory created: {OUTPUT_DIR}\")\n", "\n", "if mlflow.active_run() is not None:\n", "    print(f\"Ending previous active run: {mlflow.active_run().info.run_id}\")\n", "    mlflow.end_run()\n", "\n", "mlflow.set_experiment(EXPERIMENT_NAME)\n", "mlflow.start_run(run_name=RUN_NAME)\n", "\n", "# Log parameters\n", "mlflow.log_param(\"site_name\", SITE_NAME)\n", "mlflow.log_param(\"model_type\", \"DGCNN\")\n", "mlflow.log_param(\"patch_size\", PATCH_SIZE)\n", "mlflow.log_param(\"num_points\", NUM_POINTS)\n", "mlflow.log_param(\"k_neighbors\", K_NEIGHBORS)\n", "mlflow.log_param(\"confidence_threshold\", CONFIDENCE_THRESHOLD)\n", "mlflow.log_param(\"batch_size\", BATCH_SIZE)\n", "mlflow.log_param(\"grid_spacing\", GRID_SPACING)\n", "mlflow.log_param(\"model_path\", MODEL_PATH)\n", "\n", "print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "id": "085ade32", "metadata": {"papermill": {"duration": 0.001527, "end_time": "2025-08-01T10:56:34.496682", "exception": false, "start_time": "2025-08-01T10:56:34.495155", "status": "completed"}, "tags": []}, "source": ["## DGCNN Architecture (same as training)"]}, {"cell_type": "code", "execution_count": 5, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:34.500529Z", "iopub.status.busy": "2025-08-01T10:56:34.500393Z", "iopub.status.idle": "2025-08-01T10:56:34.504212Z", "shell.execute_reply": "2025-08-01T10:56:34.503973Z"}, "papermill": {"duration": 0.006722, "end_time": "2025-08-01T10:56:34.504936", "exception": false, "start_time": "2025-08-01T10:56:34.498214", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "    \n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    return feature"]}, {"cell_type": "code", "execution_count": 6, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:34.508621Z", "iopub.status.busy": "2025-08-01T10:56:34.508516Z", "iopub.status.idle": "2025-08-01T10:56:34.510999Z", "shell.execute_reply": "2025-08-01T10:56:34.510598Z"}, "papermill": {"duration": 0.005472, "end_time": "2025-08-01T10:56:34.512029", "exception": false, "start_time": "2025-08-01T10:56:34.506557", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x"]}, {"cell_type": "code", "execution_count": 7, "id": "b2b14a92", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:34.516241Z", "iopub.status.busy": "2025-08-01T10:56:34.516095Z", "iopub.status.idle": "2025-08-01T10:56:34.520225Z", "shell.execute_reply": "2025-08-01T10:56:34.519842Z"}, "papermill": {"duration": 0.007378, "end_time": "2025-08-01T10:56:34.521261", "exception": false, "start_time": "2025-08-01T10:56:34.513883", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class DGCNN(nn.Module):\n", "    \"\"\"Dynamic Graph CNN - exact copy from training\"\"\"\n", "    def __init__(self, num_classes=2, k=20):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        \n", "        # EdgeConv layers - getting progressively more abstract\n", "        self.conv1 = EdgeConv(3, 64, k)     # 3D coords -> 64 features\n", "        self.conv2 = EdgeConv(64, 64, k)    # 64 -> 64\n", "        self.conv3 = EdgeConv(64, 128, k)   # 64 -> 128\n", "        self.conv4 = EdgeConv(128, 256, k)  # 128 -> 256\n", "        \n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(512, 1024, kernel_size=1, bias=False),  # 64+64+128+256 = 512\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.4),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.4),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = x.transpose(2, 1)  # (B, 3, N)\n", "        \n", "        # Multi-scale EdgeConv features\n", "        x1 = self.conv1(x)   # (B, 64, N)\n", "        x2 = self.conv2(x1)  # (B, 64, N)\n", "        x3 = self.conv3(x2)  # (B, 128, N)\n", "        x4 = self.conv4(x3)  # (B, 256, N)\n", "        \n", "        # Concatenate all features - multi-scale representation!\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "        \n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "        \n", "        # Final classification\n", "        x = self.classifier(x)\n", "        \n", "        return x"]}, {"cell_type": "markdown", "id": "6e12b384", "metadata": {"papermill": {"duration": 0.001578, "end_time": "2025-08-01T10:56:34.524653", "exception": false, "start_time": "2025-08-01T10:56:34.523075", "status": "completed"}, "tags": []}, "source": ["## Load Data and DGCNN Model\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1035c7b5", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:34.528317Z", "iopub.status.busy": "2025-08-01T10:56:34.528199Z", "iopub.status.idle": "2025-08-01T10:56:34.531420Z", "shell.execute_reply": "2025-08-01T10:56:34.531070Z"}, "papermill": {"duration": 0.006044, "end_time": "2025-08-01T10:56:34.532243", "exception": false, "start_time": "2025-08-01T10:56:34.526199", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from LAS or PLY files\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    try:\n", "        if file_path.suffix.lower() == '.las':\n", "            import laspy\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded {len(points):,} points from LAS\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.ply':\n", "            import open3d as o3d\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            print(f\"Loaded {len(points):,} points from PLY\")\n", "            return points\n", "        else:\n", "            print(f\"Unsupported format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 9, "id": "0767778f", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:34.536335Z", "iopub.status.busy": "2025-08-01T10:56:34.536231Z", "iopub.status.idle": "2025-08-01T10:56:37.546537Z", "shell.execute_reply": "2025-08-01T10:56:37.546056Z"}, "papermill": {"duration": 3.013451, "end_time": "2025-08-01T10:56:37.547603", "exception": false, "start_time": "2025-08-01T10:56:34.534152", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 52,862,386 points from LAS\n", "Point cloud bounds:\n", "  X: 599595.18 to 599866.15\n", "  Y: 4334366.65 to 4334660.84\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Z: 238.63 to 259.18\n"]}], "source": ["# Load point cloud for current site\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "\n", "if point_cloud is None:\n", "    print(f\"Generating synthetic data for {SITE_NAME}\")\n", "    np.random.seed(hash(SITE_NAME) % 2**32)\n", "    point_cloud = np.random.randn(5000, 3) * 20\n", "\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}\")\n", "print(f\"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}\")\n", "print(f\"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}\")\n", "\n", "# Log point cloud metrics to MLflow\n", "mlflow.log_metric(\"point_cloud_size\", len(point_cloud))\n", "mlflow.log_metric(\"x_range\", point_cloud[:, 0].max() - point_cloud[:, 0].min())\n", "mlflow.log_metric(\"y_range\", point_cloud[:, 1].max() - point_cloud[:, 1].min())\n", "mlflow.log_metric(\"z_range\", point_cloud[:, 2].max() - point_cloud[:, 2].min())\n"]}, {"cell_type": "code", "execution_count": 10, "id": "61fee98e", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:37.552885Z", "iopub.status.busy": "2025-08-01T10:56:37.552721Z", "iopub.status.idle": "2025-08-01T10:56:37.556930Z", "shell.execute_reply": "2025-08-01T10:56:37.556654Z"}, "papermill": {"duration": 0.007646, "end_time": "2025-08-01T10:56:37.557733", "exception": false, "start_time": "2025-08-01T10:56:37.550087", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create regular grid of analysis points\"\"\"\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    padding = grid_spacing * 0.5\n", "    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)\n", "    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)\n", "    \n", "    return np.array([[x, y] for x in x_coords for y in y_coords])"]}, {"cell_type": "code", "execution_count": 11, "id": "6de0bd17", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:37.561609Z", "iopub.status.busy": "2025-08-01T10:56:37.561505Z", "iopub.status.idle": "2025-08-01T10:56:37.600017Z", "shell.execute_reply": "2025-08-01T10:56:37.599719Z"}, "papermill": {"duration": 0.041309, "end_time": "2025-08-01T10:56:37.600826", "exception": false, "start_time": "2025-08-01T10:56:37.559517", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Limiting to 1000 grid points for testing\n", "Created 1000 analysis locations\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Limit grid size for testing\n", "MAX_GRID_POINTS = 1000\n", "if len(grid_points) > MAX_GRID_POINTS:\n", "    print(f\"Limiting to {MAX_GRID_POINTS} grid points for testing\")\n", "    grid_points = grid_points[:MAX_GRID_POINTS]\n", "\n", "print(f\"Created {len(grid_points)} analysis locations\")\n", "\n", "# Log grid metrics to MLflow\n", "mlflow.log_metric(\"analysis_grid_points\", len(grid_points))"]}, {"cell_type": "code", "execution_count": 12, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:37.605150Z", "iopub.status.busy": "2025-08-01T10:56:37.605028Z", "iopub.status.idle": "2025-08-01T10:56:37.608824Z", "shell.execute_reply": "2025-08-01T10:56:37.608563Z"}, "papermill": {"duration": 0.00701, "end_time": "2025-08-01T10:56:37.609737", "exception": false, "start_time": "2025-08-01T10:56:37.602727", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_around_point(point_cloud, center_xy, radius=3.0, num_points=256):\n", "    \"\"\"Extract patch using EXACT same method as training - preserving Z variation\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    # Calculate distances to center (XY only)\n", "    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                       (point_cloud[:, 1] - center_y)**2)\n", "    \n", "    # Select points within radius\n", "    mask = distances <= radius\n", "    patch_points = point_cloud[mask]\n", "    \n", "    if len(patch_points) < 10:\n", "        return None\n", "    \n", "    # Center the patch (XY only, preserve Z)\n", "    patch_points = patch_points.copy()\n", "    patch_points[:, 0] -= center_x\n", "    patch_points[:, 1] -= center_y\n", "    # DON'T center Z - keep absolute Z values for height variation\n", "    \n", "    # Convert to float32\n", "    patch_points = np.array(patch_points, dtype=np.float32)\n", "    n = len(patch_points)\n", "    \n", "    # Sample to fixed size\n", "    if n >= num_points:\n", "        indices = np.random.choice(n, num_points, replace=False)\n", "        patch_fixed = patch_points[indices]\n", "    else:\n", "        # Pad with jittered copies\n", "        extra = np.stack([\n", "            patch_points[np.random.randint(n)] + np.random.normal(0, 0.01, 3)\n", "            for _ in range(num_points - n)\n", "        ])\n", "        patch_fixed = np.vstack([patch_points, extra])\n", "    \n", "    # Normalize XY only, preserve Z variation\n", "    xy_max = np.max(np.linalg.norm(patch_fixed[:, :2], axis=1))\n", "    if xy_max > 0:\n", "        patch_fixed[:, :2] /= xy_max\n", "    \n", "    # Normalize Z separately to preserve relative height differences\n", "    z_mean = patch_fixed[:, 2].mean()\n", "    z_std = patch_fixed[:, 2].std()\n", "    if z_std > 0:\n", "        patch_fixed[:, 2] = (patch_fixed[:, 2] - z_mean) / z_std\n", "    \n", "    return patch_fixed.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 13, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:37.614135Z", "iopub.status.busy": "2025-08-01T10:56:37.613995Z", "iopub.status.idle": "2025-08-01T10:56:37.692793Z", "shell.execute_reply": "2025-08-01T10:56:37.692548Z"}, "papermill": {"duration": 0.081806, "end_time": "2025-08-01T10:56:37.693617", "exception": false, "start_time": "2025-08-01T10:56:37.611811", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DGCNN model from best_dgcnn.pth\n", "Model has 1,276,034 parameters\n", "Testing model with dummy data...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [5.804399e-10 1.000000e+00]\n", "Model output might be problematic: pile_prob=1.0000\n"]}], "source": ["# Load trained DGCNN model with validation\n", "try:\n", "    model = DGCNN(num_classes=2, k=K_NEIGHBORS).to(device)\n", "    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "    model.eval()\n", "    print(f\"Loaded DGCNN model from {MODEL_PATH}\")\n", "    \n", "    # Log model info\n", "    param_count = sum(p.numel() for p in model.parameters())\n", "    print(f\"Model has {param_count:,} parameters\")\n", "    \n", "    # Test model with dummy data to verify it works\n", "    print(\"Testing model with dummy data...\")\n", "    dummy_input = torch.randn(1, 256, 3).to(device)\n", "    with torch.no_grad():\n", "        dummy_output = model(dummy_input)\n", "        dummy_probs = torch.softmax(dummy_output, dim=1)\n", "        print(f\"Dummy test - Output shape: {dummy_output.shape}\")\n", "        print(f\"Dummy test - Probabilities: {dummy_probs[0].cpu().numpy()}\")\n", "        \n", "        # Check if probabilities are reasonable\n", "        pile_prob = dummy_probs[0, 1].item()\n", "        if pile_prob > 0.1 and pile_prob < 0.9:\n", "            print(\"Model producing reasonable probability ranges\")\n", "        else:\n", "            print(f\"Model output might be problematic: pile_prob={pile_prob:.4f}\")\n", "    \n", "    MODEL_LOADED = True\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    print(\"Creating untrained model for testing...\")\n", "    model = DGCNN(num_classes=2, k=K_NEIGHBORS).to(device)\n", "    model.eval()\n", "    MODEL_LOADED = False"]}, {"cell_type": "markdown", "id": "6b0a249e", "metadata": {"papermill": {"duration": 0.00184, "end_time": "2025-08-01T10:56:37.697638", "exception": false, "start_time": "2025-08-01T10:56:37.695798", "status": "completed"}, "tags": []}, "source": ["## Process site with DGCNN\n"]}, {"cell_type": "code", "execution_count": 14, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:37.702026Z", "iopub.status.busy": "2025-08-01T10:56:37.701839Z", "iopub.status.idle": "2025-08-01T10:56:37.705342Z", "shell.execute_reply": "2025-08-01T10:56:37.705116Z"}, "papermill": {"duration": 0.006445, "end_time": "2025-08-01T10:56:37.706095", "exception": false, "start_time": "2025-08-01T10:56:37.699650", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_dgcnn(point_cloud, grid_points, model, device, batch_size=16):\n", "    \"\"\"Process site with DGCNN model\"\"\"\n", "    results = []\n", "    \n", "    for i in range(0, len(grid_points), batch_size):\n", "        batch_centers = grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_indices = []\n", "        \n", "        # Extract valid patches\n", "        for j, center in enumerate(batch_centers):\n", "            patch = extract_patch_around_point(point_cloud, center, radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_indices.append(i + j)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        # Run DGCNN inference\n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        # Store results\n", "        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):\n", "            center = grid_points[idx]\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    return results\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T10:56:37.710261Z", "iopub.status.busy": "2025-08-01T10:56:37.710168Z", "iopub.status.idle": "2025-08-01T11:05:06.169886Z", "shell.execute_reply": "2025-08-01T11:05:06.169229Z"}, "papermill": {"duration": 508.467668, "end_time": "2025-08-01T11:05:06.175710", "exception": false, "start_time": "2025-08-01T10:56:37.708042", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DGCNN inference on 1000 locations...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["DGCNN Results for althea_rpcs:\n", "  Total locations analyzed: 686\n", "  Pile detections: 63 (9.2%)\n", "  Average confidence: 0.151\n", "  High confidence (>0.9): 68\n"]}], "source": ["# Process site with DGCNN\n", "print(f\"Running DGCNN inference on {len(grid_points)} locations...\")\n", "\n", "results = process_site_dgcnn(point_cloud, grid_points, model, device, BATCH_SIZE)\n", "\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    avg_confidence = results_df['pile_probability'].mean()\n", "    \n", "    print(f\"DGCNN Results for {SITE_NAME}:\")\n", "    print(f\"  Total locations analyzed: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  High confidence (>0.9): {sum(results_df['pile_probability'] > 0.9)}\")\n", "    \n", "    # Log results to MLflow\n", "    mlflow.log_metric(\"total_locations\", len(results_df))\n", "    mlflow.log_metric(\"pile_detections\", pile_count)\n", "    mlflow.log_metric(\"detection_rate\", pile_count/len(results_df))\n", "    mlflow.log_metric(\"avg_confidence\", avg_confidence)\n", "    mlflow.log_metric(\"high_confidence_count\", sum(results_df['pile_probability'] > 0.9))\n", "else:\n", "    print(f\"No valid results for {SITE_NAME}\")\n", "    results_df = pd.DataFrame()\n", "    pile_count = 0\n", "    avg_confidence = 0.0\n"]}, {"cell_type": "markdown", "id": "c4f86d3f", "metadata": {"papermill": {"duration": 0.001868, "end_time": "2025-08-01T11:05:06.179527", "exception": false, "start_time": "2025-08-01T11:05:06.177659", "status": "completed"}, "tags": []}, "source": ["## Visualization\n"]}, {"cell_type": "code", "execution_count": 16, "id": "a9c7014e", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:06.184162Z", "iopub.status.busy": "2025-08-01T11:05:06.184021Z", "iopub.status.idle": "2025-08-01T11:05:06.672970Z", "shell.execute_reply": "2025-08-01T11:05:06.672661Z"}, "papermill": {"duration": 0.492995, "end_time": "2025-08-01T11:05:06.674366", "exception": false, "start_time": "2025-08-01T11:05:06.181371", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize results for current site\n", "if not results_df.empty:\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # Color by pile probability\n", "    scatter = plt.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='RdYlBu_r', s=30, alpha=0.7\n", "    )\n", "    \n", "    plt.title(f'DGCNN Pile Detection - {SITE_NAME}\\n{pile_count} piles detected')\n", "    plt.xlabel('X Coordinate')\n", "    plt.ylabel('Y Coordinate')\n", "    \n", "    # Add colorbar\n", "    cbar = plt.colorbar(scatter)\n", "    cbar.set_label('Pile Probability')\n", "    \n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.savefig(f'dgcnn_{SITE_NAME}_results.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "else:\n", "    print(f\"No results to visualize for {SITE_NAME}\")\n"]}, {"cell_type": "markdown", "id": "dfafef67", "metadata": {"papermill": {"duration": 0.002541, "end_time": "2025-08-01T11:05:06.679647", "exception": false, "start_time": "2025-08-01T11:05:06.677106", "status": "completed"}, "tags": []}, "source": ["## Export Results\n"]}, {"cell_type": "code", "execution_count": 17, "id": "f0afd0b2", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:06.685164Z", "iopub.status.busy": "2025-08-01T11:05:06.684972Z", "iopub.status.idle": "2025-08-01T11:05:06.693316Z", "shell.execute_reply": "2025-08-01T11:05:06.693066Z"}, "papermill": {"duration": 0.012043, "end_time": "2025-08-01T11:05:06.694178", "exception": false, "start_time": "2025-08-01T11:05:06.682135", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved althea_rpcs results to dgcnn_althea_rpcs_detections.csv\n", "\n", "Summary for althea_rpcs:\n", "  site_name: althea_rpcs\n", "  total_locations: 686\n", "  pile_detections: 63\n", "  detection_rate: 0.092\n", "  avg_confidence: 0.15127423405647278\n", "  high_confidence_count: 68\n", "  model_loaded: True\n"]}], "source": ["# Export results for current site\n", "if not results_df.empty:\n", "    # Save to CSV\n", "    output_file = f\"dgcnn_{SITE_NAME}_detections.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Saved {SITE_NAME} results to {output_file}\")\n", "    \n", "    # Create summary\n", "    summary = {\n", "        'site_name': SITE_NAME,\n", "        'total_locations': len(results_df),\n", "        'pile_detections': pile_count,\n", "        'detection_rate': pile_count/len(results_df),\n", "        'avg_confidence': avg_confidence,\n", "        'high_confidence_count': sum(results_df['pile_probability'] > 0.9),\n", "        'model_loaded': MODEL_LOADED\n", "    }\n", "    \n", "    print(f\"\\nSummary for {SITE_NAME}:\")\n", "    for key, value in summary.items():\n", "        if isinstance(value, float):\n", "            print(f\"  {key}: {value:.3f}\")\n", "        else:\n", "            print(f\"  {key}: {value}\")\n", "else:\n", "    print(f\"No results to export for {SITE_NAME}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "80762bff", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:06.700141Z", "iopub.status.busy": "2025-08-01T11:05:06.700041Z", "iopub.status.idle": "2025-08-01T11:05:06.842548Z", "shell.execute_reply": "2025-08-01T11:05:06.842215Z"}, "papermill": {"duration": 0.146443, "end_time": "2025-08-01T11:05:06.843461", "exception": false, "start_time": "2025-08-01T11:05:06.697018", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported all detections to KML: output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 686 points\n", "  Pile detections: 63 points\n", "  High confidence: 68 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for althea_rpcs!\n", "Output directory: output_runs/dgcnn_inference/althea_rpcs\n"]}], "source": ["# Export results to KML format\n", "from shapely.geometry import Point\n", "import geopandas as gpd\n", "\n", "if not results_df.empty:\n", "    try:\n", "        # Create GeoDataFrame from results\n", "        geometry = [Point(xy) for xy in zip(results_df['x'], results_df['y'])]\n", "        \n", "        # Create GeoDataFrame\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry)\n", "        \n", "        # Set coordinate reference system (assuming local coordinates)\n", "        # You may need to adjust this based on your actual coordinate system\n", "        gdf.crs = \"EPSG:4326\"  # WGS84 - adjust if needed\n", "        \n", "        # Add additional attributes for better KML visualization\n", "        gdf['name'] = gdf.apply(lambda row: f\"Pile_{row.name}\" if row['prediction'] == 'PILE' else f\"NonPile_{row.name}\", axis=1)\n", "        gdf['description'] = gdf.apply(lambda row: f\"Confidence: {row['pile_probability']:.3f}\\nPrediction: {row['prediction']}\", axis=1)\n", "        \n", "        # Color coding based on confidence\n", "        gdf['confidence_level'] = pd.cut(gdf['pile_probability'], \n", "                                       bins=[0, 0.5, 0.8, 0.95, 1.0], \n", "                                       labels=['Low', 'Medium', 'High', 'Very High'])\n", "        \n", "        # Export all detections to KML\n", "        kml_all_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_all_detections.kml\"\n", "        gdf.to_file(kml_all_file, driver='KML')\n", "        print(f\"Exported all detections to KML: {kml_all_file}\")\n", "        \n", "        # Export only pile detections to separate KML\n", "        pile_gdf = gdf[gdf['prediction'] == 'PILE'].copy()\n", "        if not pile_gdf.empty:\n", "            kml_piles_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_pile_detections.kml\"\n", "            pile_gdf.to_file(kml_piles_file, driver='KML')\n", "            print(f\"Exported pile detections to KML: {kml_piles_file}\")\n", "            \n", "            # Log KML files to MLflow\n", "            mlflow.log_artifact(str(kml_piles_file))\n", "        \n", "        # Export high confidence detections (>0.9) to separate KML\n", "        high_conf_gdf = gdf[gdf['pile_probability'] > 0.9].copy()\n", "        if not high_conf_gdf.empty:\n", "            kml_high_conf_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_high_confidence.kml\"\n", "            high_conf_gdf.to_file(kml_high_conf_file, driver='KML')\n", "            print(f\"Exported high confidence detections to KML: {kml_high_conf_file}\")\n", "            \n", "            # Log to MLflow\n", "            mlflow.log_artifact(str(kml_high_conf_file))\n", "        \n", "        # Log main KML to MLflow\n", "        mlflow.log_artifact(str(kml_all_file))\n", "        \n", "        print(f\"\\nKML Export Summary:\")\n", "        print(f\"  All detections: {len(gdf)} points\")\n", "        print(f\"  Pile detections: {len(pile_gdf)} points\")\n", "        print(f\"  High confidence: {len(high_conf_gdf)} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error exporting to KML: {e}\")\n", "else:\n", "    print(\"No results available for KML export\")\n", "\n", "\n", "# Close MLflow run\n", "mlflow.end_run()\n", "print(\"MLflow run completed\")\n", "\n", "print(f\"\\nDGCNN inference complete for {SITE_NAME}!\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "72e057a7", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:06.850324Z", "iopub.status.busy": "2025-08-01T11:05:06.850125Z", "iopub.status.idle": "2025-08-01T11:05:06.857509Z", "shell.execute_reply": "2025-08-01T11:05:06.857226Z"}, "papermill": {"duration": 0.011572, "end_time": "2025-08-01T11:05:06.858279", "exception": false, "start_time": "2025-08-01T11:05:06.846707", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction Distribution Analysis:\n", "Min probability: 0.0000\n", "Max probability: 1.0000\n", "Mean probability: 0.1513\n", "Std probability: 0.3197\n", "Detections > 0.5: 95 (13.8%)\n", "Detections > 0.7: 87 (12.7%)\n", "Detections > 0.8: 78 (11.4%)\n", "Detections > 0.9: 68 (9.9%)\n", "Detections > 0.95: 63 (9.2%)\n", "\n", "Top 10 predictions:\n", "           x            y  pile_probability prediction\n", "599617.67945 4.334414e+06               1.0       PILE\n", "599622.67945 4.334484e+06               1.0       PILE\n", "599627.67945 4.334444e+06               1.0       PILE\n", "599627.67945 4.334499e+06               1.0       PILE\n", "599627.67945 4.334519e+06               1.0       PILE\n", "599627.67945 4.334594e+06               1.0       PILE\n", "599632.67945 4.334399e+06               1.0       PILE\n", "599637.67945 4.334404e+06               1.0       PILE\n", "599647.67945 4.334374e+06               1.0       PILE\n", "599647.67945 4.334424e+06               1.0       PILE\n", "\n", "Model loaded successfully: True\n"]}], "source": ["# %%\n", "# Diagnostic: Check model predictions in detail\n", "if not results_df.empty:\n", "    print(\"Prediction Distribution Analysis:\")\n", "    print(f\"Min probability: {results_df['pile_probability'].min():.4f}\")\n", "    print(f\"Max probability: {results_df['pile_probability'].max():.4f}\")\n", "    print(f\"Mean probability: {results_df['pile_probability'].mean():.4f}\")\n", "    print(f\"Std probability: {results_df['pile_probability'].std():.4f}\")\n", "    \n", "    # Check distribution at different thresholds\n", "    for threshold in [0.5, 0.7, 0.8, 0.9, 0.95]:\n", "        count = sum(results_df['pile_probability'] > threshold)\n", "        print(f\"Detections > {threshold}: {count} ({count/len(results_df)*100:.1f}%)\")\n", "    \n", "    # Show top 10 predictions\n", "    print(\"\\nTop 10 predictions:\")\n", "    top_preds = results_df.nlargest(10, 'pile_probability')[['x', 'y', 'pile_probability', 'prediction']]\n", "    print(top_preds.to_string(index=False))\n", "else:\n", "    print(\"No results to analyze\")\n", "\n", "print(f\"\\nModel loaded successfully: {MODEL_LOADED}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 517.852484, "end_time": "2025-08-01T11:05:07.884227", "environment_variables": {}, "exception": null, "input_path": "02_dgcnn_inference.ipynb", "output_path": "02_dgcnn_inference_althea_rpcs_executed.ipynb", "parameters": {"OUTPUT_DIR": "output_runs/dgcnn_inference/althea_rpcs", "POINT_CLOUD_PATH": "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las", "RUN_NAME": "inference_althea_rpcs", "SITE_NAME": "althea_rpcs"}, "start_time": "2025-08-01T10:56:30.031743", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}