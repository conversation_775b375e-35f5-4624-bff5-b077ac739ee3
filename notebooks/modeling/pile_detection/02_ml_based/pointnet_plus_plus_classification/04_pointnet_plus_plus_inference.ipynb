# %% [markdown]
# # PointNet++ Inference on New Site
# Inference pipeline for applying trained PointNet++ model to new point cloud data

# %% [code]
# Parameters
NEW_SITE_NAME = "althea_rpcs"
POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
OUTPUT_DIR = f"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}"
MODEL_PATH = "best_pointnet_iter4.pth"
CONFIDENCE_THRESHOLD = 0.5
GRID_SPACING = 5.0
PATCH_SIZE = 20.0  # Will be adjusted using estimate_patch_radius
NUM_POINTS = 1024
BATCH_SIZE = 64  # Updated for batching

# %% [code]
import os
import numpy as np
from pathlib import Path
from datetime import datetime
import torch
import matplotlib.pyplot as plt
import laspy
import mlflow
from scipy.spatial import cKDTree
import pandas as pd

# %% [code]
def estimate_patch_radius(points, target_num_points=1024):
    tree = cKDTree(points[:, :2])
    dists, _ = tree.query(points[:, :2], k=2)
    avg_spacing = np.mean(dists[:, 1])
    estimated_radius = np.sqrt(target_num_points / (np.pi / (avg_spacing ** 2)))
    return estimated_radius

# %% [code]
def load_point_cloud(file_path):
    if not Path(file_path).exists():
        raise FileNotFoundError(f"Point cloud file not found: {file_path}")
    if file_path.endswith(".las"):
        las = laspy.read(file_path)
        return np.vstack([las.x, las.y, las.z]).T
    else:
        raise ValueError("Unsupported file format. Only .las is supported.")

# %% [code]
# Load point cloud
point_cloud = load_point_cloud(POINT_CLOUD_PATH)
PATCH_SIZE = estimate_patch_radius(point_cloud, NUM_POINTS)
os.makedirs(OUTPUT_DIR, exist_ok=True)

# %% [code]
def create_grid(points, spacing):
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    x_vals = np.arange(x_min, x_max, spacing)
    y_vals = np.arange(y_min, y_max, spacing)
    return np.array([[x, y] for x in x_vals for y in y_vals])

# %% [code]
def extract_patch(point_cloud, center, radius, num_points):
    distances = np.linalg.norm(point_cloud[:, :2] - center[:2], axis=1)
    mask = distances <= radius
    patch = point_cloud[mask]
    if len(patch) < 50:
        return None
    sampled = patch[np.random.choice(len(patch), num_points, replace=len(patch) < num_points)]
    sampled = sampled - np.mean(sampled, axis=0)
    sampled = sampled / np.linalg.norm(sampled, axis=1, keepdims=True)
    return sampled

# %% [code]
# Grid creation
grid_points = create_grid(point_cloud, GRID_SPACING)

# %% [code]
# Dummy model
class DummyModel(torch.nn.Module):
    def forward(self, x):
        return torch.randn((x.shape[0], 2))

model = DummyModel()
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device).eval()

# %% [code]
def run_inference_batched(model, point_cloud, grid_points, batch_size=64):
    results = []
    patches = []
    centers = []

    for center in grid_points:
        patch = extract_patch(point_cloud, center, PATCH_SIZE, NUM_POINTS)
        if patch is not None:
            patches.append(patch)
            centers.append(center)

    if not patches:
        return []

    all_patches = torch.FloatTensor(patches).to(device)
    model.eval()
    with torch.no_grad():
        for i in range(0, len(all_patches), batch_size):
            batch = all_patches[i:i+batch_size]
            logits = model(batch)
            probs = torch.softmax(logits, dim=1)[:, 1].cpu().numpy()

            for j, prob in enumerate(probs):
                x, y = centers[i + j]
                results.append({"x": x, "y": y, "prob": prob, "label": "PILE" if prob > CONFIDENCE_THRESHOLD else "NON-PILE"})

    return results

# %% [code]
import time
start = time.time()
results = run_inference_batched(model, point_cloud, grid_points, BATCH_SIZE)
print(f"Inference completed in {time.time() - start:.2f} seconds")

# %% [code]
results_df = pd.DataFrame(results)
results_df.to_csv(Path(OUTPUT_DIR) / f"{NEW_SITE_NAME}_pile_detections.csv", index=False)
print("Results saved.")

# %% [code]
pile_count = (results_df['label'] == 'PILE').sum()
total = len(results_df)
print(f"Detected {pile_count} piles out of {total} locations ({pile_count/total:.2%})")
