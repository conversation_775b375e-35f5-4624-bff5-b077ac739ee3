# Parameters cell for Papermill execution
NEW_SITE_NAME = "nevados"  # Update with actual site name
MODEL_PATH = "best_pointnet_plus_plus.pth"  # Path to trained model
POINT_CLOUD_PATH = "../../../../../data/raw/nevados/pointcloud/nevados.las"

DWG_PATH = ""  # Path to DWG file (optional)
OUTPUT_DIR = "output_runs/pointnet_plus_plus_inference"  # Output directory

# Model parameters (must match training)
CONFIDENCE_THRESHOLD = 0.95  # Confidence threshold for pile detection
BATCH_SIZE = 16  # Batch size for inference

GRID_SPACING = 5.0 # Grid spacing for analysis points
PATCH_SIZE = 3.0  # meters radius for patch extraction
NUM_POINTS = 128

# MLflow configuration
EXPERIMENT_NAME = "pointnet_plus_plus_inference"
RUN_NAME = f"inference_{NEW_SITE_NAME}"

# Display configuration
print(f"New Site Analysis Configuration:")
print(f"Site Name: {NEW_SITE_NAME}")
print(f"Patch Size: {PATCH_SIZE}m radius")
print(f"Points per Patch: {NUM_POINTS}")
print(f"Model Path: {MODEL_PATH}")
print(f"Point Cloud Path: {POINT_CLOUD_PATH}")
print(f"Output Directory: {OUTPUT_DIR}")

import os
import json
import pickle
import torch
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
import torch.nn as nn

# Optional libraries
import matplotlib.pyplot as plt
import laspy
import open3d as o3d
import mlflow
import mlflow.pytorch

warnings.filterwarnings('ignore')


# Setup output directory and initialize MLflow
os.makedirs(OUTPUT_DIR, exist_ok=True)
print(f"Output directory created: {OUTPUT_DIR}")

if mlflow.active_run() is not None:
    print(f"Ending previous active run: {mlflow.active_run().info.run_id}")
    mlflow.end_run()

mlflow.set_experiment(EXPERIMENT_NAME)
mlflow.start_run(run_name=RUN_NAME)

# Log parameters
mlflow.log_param("site_name", NEW_SITE_NAME)
mlflow.log_param("patch_size", PATCH_SIZE)
mlflow.log_param("num_points", NUM_POINTS)
mlflow.log_param("confidence_threshold", CONFIDENCE_THRESHOLD)
mlflow.log_param("batch_size", BATCH_SIZE)
mlflow.log_param("grid_spacing", GRID_SPACING)

print("MLflow experiment initialized")

# PointNet++ utility functions
def square_distance(src, dst):
    """Calculate squared distance between two point sets"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def farthest_point_sample(xyz, npoint):
    """Farthest point sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """Query ball point grouping"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

# PointNet++ Set Abstraction Layer
class PointNetSetAbstraction(nn.Module):
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        """Forward pass of PointNet++ Set Abstraction layer"""
        B, N, C = xyz.shape
        
        if self.group_all:
            new_xyz = torch.zeros(B, 1, C).to(xyz.device)
            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)
            
            if points is not None:
                grouped_points = points[torch.arange(B)[:, None, None], idx]
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm
        
        new_points = new_points.permute(0, 3, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = torch.relu(bn(conv(new_points)))
        
        new_points = torch.max(new_points, 2)[0]
        new_points = new_points.permute(0, 2, 1)
        
        return new_xyz, new_points

# Validate model and data paths
def validate_paths():
    """Validate that required files exist"""
    issues = []
    
    # Check model path
    if not Path(MODEL_PATH).exists():
        issues.append(f"Model file not found: {MODEL_PATH}")
    
    # Check point cloud path if specified
    if POINT_CLOUD_PATH and not Path(POINT_CLOUD_PATH).exists():
        issues.append(f"Point cloud file not found: {POINT_CLOUD_PATH}")
    
    # Check DWG path if specified
    if DWG_PATH and not Path(DWG_PATH).exists():
        issues.append(f"DWG file not found: {DWG_PATH}")
    
    return issues

# Run validation
validation_issues = validate_paths()
if validation_issues:
    print("Validation Issues Found:")
    for issue in validation_issues:
        print(f"  - {issue}")
    print("\nNote: Some features may not work without proper file paths.")
else:
    print("All file paths validated successfully.")

# Define complete PointNet++ model
class PointNetPlusPlus(nn.Module):
    def __init__(self, num_classes=2):
        super(PointNetPlusPlus, self).__init__()
        
        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)
        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)
        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)
        
        self.fc1 = nn.Linear(1024, 512)
        self.bn1 = nn.BatchNorm1d(512)
        self.drop1 = nn.Dropout(0.4)
        self.fc2 = nn.Linear(512, 256)
        self.bn2 = nn.BatchNorm1d(256)
        self.drop2 = nn.Dropout(0.4)
        self.fc3 = nn.Linear(256, num_classes)
    
    def forward(self, xyz):
        if xyz.shape[1] == 3:
            xyz = xyz.transpose(1, 2).contiguous()
        
        B, _, _ = xyz.shape
        
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        x = l3_points.view(B, 1024)
        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))
        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))
        x = self.fc3(x)
        
        return x


# Load trained model with error handling
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
try:
    if Path(MODEL_PATH).exists():
        model = PointNetPlusPlus(num_classes=2).to(device)
        model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
        model.eval()
        
        param_count = sum(p.numel() for p in model.parameters())
        print(f"Loaded trained PointNet++ model from {MODEL_PATH}")
        print(f"Model has {param_count:,} parameters")
        
        mlflow.log_param("model_parameters", param_count)
        mlflow.log_param("device", str(device))
        
        MODEL_LOADED = True
    else:
        print(f"Model file not found: {MODEL_PATH}")
        print("Creating untrained model for architecture testing...")
        model = PointNetPlusPlus(num_classes=2).to(device)
        model.eval()
        MODEL_LOADED = False
        
except Exception as e:
    print(f"Error loading model: {e}")
    print("Creating untrained model for architecture testing...")
    model = PointNetPlusPlus(num_classes=2).to(device)
    model.eval()
    MODEL_LOADED = False

from pathlib import Path
import numpy as np

def load_point_cloud(file_path):
    """
    Load point cloud from supported formats (.las, .ply)
    Returns numpy array of shape (N, 3) with X, Y, Z coordinates
    """
    file_path = Path(file_path)

    if not file_path.exists():
        print(f"File not found: {file_path}")
        return None

    try:
        suffix = file_path.suffix.lower()

        if suffix == '.las':
            try:
                import laspy
            except ImportError:
                print("laspy not available. Install with: pip install laspy")
                return None
            las_file = laspy.read(file_path)
            points = np.vstack([las_file.x, las_file.y, las_file.z]).T
            print(f"Loaded LAS file with {len(points):,} points")
            return points

        elif suffix == '.ply':
            try:
                import open3d as o3d
            except ImportError:
                print("open3d not available. Install with: pip install open3d")
                return None
            pcd = o3d.io.read_point_cloud(str(file_path))
            points = np.asarray(pcd.points)
            print(f"Loaded PLY file with {len(points):,} points")
            return points

        else:
            print(f"Unsupported file format: {suffix}")
            return None

    except Exception as e:
        print(f"Error loading point cloud: {e}")
        return None

# Load new site point cloud with enhanced logging
if POINT_CLOUD_PATH and Path(POINT_CLOUD_PATH).exists():
    point_cloud = load_point_cloud(POINT_CLOUD_PATH)
    if point_cloud is not None:
        print(f"Point cloud bounds:")
        print(f"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}")
        print(f"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}")
        print(f"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}")
        
        # Log to MLflow
        mlflow.log_metric("point_cloud_size", len(point_cloud))
        mlflow.log_metric("x_range", point_cloud[:, 0].max() - point_cloud[:, 0].min())
        mlflow.log_metric("y_range", point_cloud[:, 1].max() - point_cloud[:, 1].min())
        mlflow.log_metric("z_range", point_cloud[:, 2].max() - point_cloud[:, 2].min())
        
        POINT_CLOUD_LOADED = True
    else:
        print("Failed to load point cloud - using synthetic data")
        POINT_CLOUD_LOADED = False
else:
    print("Point cloud path not specified or file not found. Using synthetic data for testing.")
    POINT_CLOUD_LOADED = False

# Generate synthetic point cloud for testing if needed
if not POINT_CLOUD_LOADED:
    print("Generating synthetic point cloud for testing...")
    np.random.seed(42)
    point_cloud = np.random.randn(10000, 3) * 10  # Smaller for testing
    print(f"Generated synthetic point cloud with {len(point_cloud):,} points")

# Define grid-based patch extraction for site-wide analysis
def create_analysis_grid(point_cloud, grid_spacing=5.0):
    """
    Create a regular grid of analysis points across the site
    """
    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()
    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()
    
    # Add padding to ensure coverage
    padding = grid_spacing * 0.5
    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)
    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)
    
    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])

    return grid_points

# Define patch extraction around grid points
def extract_patch_around_point(point_cloud, center_xy, radius=5.0, num_points=256):
    """
    Extract a circular patch of points around a center location
    """
    center_x, center_y = center_xy
    
    # Calculate distances to center
    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + 
                       (point_cloud[:, 1] - center_y)**2)
    
    # Select points within radius
    mask = distances <= radius
    patch_points = point_cloud[mask]
    
    if len(patch_points) < 10:
        return None  # Too few points for analysis
    
    # Center the patch
    patch_points[:, 0] -= center_x
    patch_points[:, 1] -= center_y
    
    # Sample or pad to fixed number of points
    if len(patch_points) >= num_points:
        indices = np.random.choice(len(patch_points), num_points, replace=False)
        patch_points = patch_points[indices]
    else:
        # Pad with noisy copies
        n_needed = num_points - len(patch_points)
        noise_scale = 0.01
        for _ in range(n_needed):
            base_idx = np.random.randint(len(patch_points))
            base_point = patch_points[base_idx].copy()
            base_point += np.random.normal(0, noise_scale, 3)
            patch_points = np.vstack([patch_points, base_point])
    
    # Normalize
    max_dist = np.max(np.linalg.norm(patch_points, axis=1))
    if max_dist > 0:
        patch_points = patch_points / max_dist
    
    return patch_points.astype(np.float32)

def filter_valid_grid_points(point_cloud, grid_points, patch_size, min_points):
    valid_grid = []
    for pt in grid_points:
        patch = extract_patch_around_point(point_cloud, pt, radius=patch_size, num_points=min_points)
        if patch is not None:
            valid_grid.append(pt)
    return np.array(valid_grid)

# Analyse Density of Point Cloud
total_points = len(point_cloud)
xyz_min = point_cloud[:, :3].min(axis=0)
xyz_max = point_cloud[:, :3].max(axis=0)
bounds = xyz_max - xyz_min
volume = bounds[0] * bounds[1]  # area in 2D

density_2d = total_points / volume  # points per m²
print(f"Point cloud density: {density_2d:.2f} points/m²")

# Analyse Spacing of Point Cloud
import scipy.spatial

kdtree = scipy.spatial.cKDTree(point_cloud[:, :3])
dists, _ = kdtree.query(point_cloud[:, :3], k=2)  # k=2 includes self and nearest neighbor
avg_spacing = np.mean(dists[:, 1])  # skip distance to self
print(f"Average spacing between points: {avg_spacing:.3f} m")

# Create analysis grid
grid_points = create_analysis_grid(point_cloud, grid_spacing=GRID_SPACING)
print(f"Created analysis grid with {len(grid_points)} analysis points")
print(f"Grid spacing: {GRID_SPACING}m")

mlflow.log_metric("analysis_grid_points", len(grid_points))

# Filter invalid ones
valid_grid_points = filter_valid_grid_points(
    point_cloud, grid_points, patch_size=PATCH_SIZE, min_points=NUM_POINTS
)
print(f"Filtered to {len(valid_grid_points)} valid analysis points")

# Extract patches around grid points
sample_center = valid_grid_points[len(valid_grid_points)//2]
#sample_center = grid_points[len(grid_points)//2]  # Middle of grid
sample_patch = extract_patch_around_point(point_cloud, sample_center, 
                                        radius=PATCH_SIZE, num_points=NUM_POINTS)

if sample_patch is not None:
    print(f"Successfully extracted sample patch with shape: {sample_patch.shape}")
    print(f"Sample patch statistics:")
    print(f"  X range: {sample_patch[:, 0].min():.3f} to {sample_patch[:, 0].max():.3f}")
    print(f"  Y range: {sample_patch[:, 1].min():.3f} to {sample_patch[:, 1].max():.3f}")
    print(f"  Z range: {sample_patch[:, 2].min():.3f} to {sample_patch[:, 2].max():.3f}")
else:
    print("Failed to extract sample patch - insufficient points in area")


# Run inference on sample patch
if sample_patch is not None:
    with torch.no_grad():
        # Convert to tensor and add batch dimension
        patch_tensor = torch.FloatTensor(sample_patch).unsqueeze(0).to(device)
        
        # Run inference
        output = model(patch_tensor)
        probabilities = torch.softmax(output, dim=1)
        
        pile_probability = probabilities[0, 1].cpu().item()
        prediction = "PILE" if pile_probability > CONFIDENCE_THRESHOLD else "NON-PILE"
        
        print(f"Sample patch inference:")
        print(f"  Pile probability: {pile_probability:.4f}")
        print(f"  Prediction: {prediction}")
        print(f"  Confidence threshold: {CONFIDENCE_THRESHOLD}")

# Batch processing function for site-wide analysis
def process_site_batch(point_cloud, grid_points, model, device, 
                      batch_size=32, radius=5.0, num_points=256):
    """
    Process multiple grid points in batches for efficiency
    """
    results = []
    
    for i in range(0, len(grid_points), batch_size):
        batch_centers = grid_points[i:i+batch_size]
        batch_patches = []
        valid_indices = []
        
        # Extract patches for batch
        for j, center in enumerate(batch_centers):
            patch = extract_patch_around_point(point_cloud, center, radius, num_points)
            if patch is not None:
                batch_patches.append(patch)
                valid_indices.append(i + j)
        
        if len(batch_patches) == 0:
            continue
        
        # Convert to tensor
        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)
        
        # Run inference
        with torch.no_grad():
            outputs = model(batch_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            pile_probs = probabilities[:, 1].cpu().numpy()
        
        # Store results
        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):
            center = grid_points[idx]
            results.append({
                'grid_index': idx,
                'x': center[0],
                'y': center[1],
                'pile_probability': prob,
                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'
            })
    
    return results


# Run site-wide analysis on subset for testing
print("Running site-wide analysis on sample grid...")
SAMPLE_GRID_SIZE = 2000 # bring it down if its too slow

print(f"Grid points: {len(grid_points)}")
# Use subset for testing (first 100 points)
if len(grid_points) > SAMPLE_GRID_SIZE:
    print(f"Using first {SAMPLE_GRID_SIZE} points for testing")
    test_grid = grid_points[:SAMPLE_GRID_SIZE]
else:
    test_grid = grid_points

results = process_site_batch(point_cloud, test_grid, model, device, 
                           batch_size=16, radius=PATCH_SIZE, num_points=NUM_POINTS)

print(f"Processed {len(results)} analysis points")

# %%
# Analyze results
if results:
    results_df = pd.DataFrame(results)
    
    pile_detections = results_df[results_df['prediction'] == 'PILE']
    non_pile_detections = results_df[results_df['prediction'] == 'NON-PILE']
    
    print(f"Analysis Results Summary:")
    print(f"  Total analysis points: {len(results_df)}")
    print(f"  Pile detections: {len(pile_detections)} ({len(pile_detections)/len(results_df)*100:.1f}%)")
    print(f"  Non-pile areas: {len(non_pile_detections)} ({len(non_pile_detections)/len(results_df)*100:.1f}%)")
    print(f"  Average pile probability: {results_df['pile_probability'].mean():.4f}")
    print(f"  High confidence piles (>0.9): {sum(results_df['pile_probability'] > 0.9)}")

print(results_df.head())

# Point cloud bounds
pcd_xyz =point_cloud[:, :3]
print("Point Cloud Bounds (X, Y):", pcd_xyz[:, :2].min(axis=0), pcd_xyz[:, :2].max(axis=0))

from scipy.spatial import cKDTree
import open3d as o3d
import numpy as np

# Build KD-tree on XY of original point cloud
pcd_xyz = point_cloud[:, :3]
pcd_tree = cKDTree(pcd_xyz[:, :2])  # only XY for matching

# Pile X,Y from detection DataFrame
pile_xy = results_df[['x', 'y']].values

# Find nearest neighbor indices in point cloud
_, idx = pcd_tree.query(pile_xy, k=1)

# Get correct Z-values from original point cloud
z_vals = pcd_xyz[idx, 2]  # use matched index to extract Z

# Stack to get full XYZ for pile detections
pile_with_z = np.hstack((pile_xy, z_vals.reshape(-1, 1)))

# Create red pile point cloud
pile_colors = np.zeros((pile_with_z.shape[0], 3))
pile_colors[:, 0] = 1.0  # Red

pile_pcd = o3d.geometry.PointCloud()
pile_pcd.points = o3d.utility.Vector3dVector(pile_with_z)
pile_pcd.colors = o3d.utility.Vector3dVector(pile_colors)

pile_pcd.paint_uniform_color([1, 0, 0])  # Red
pile_pcd.estimate_normals()
pile_pcd.normalize_normals()

# Convert original point cloud (NumPy) to Open3D
orig_pcd = o3d.geometry.PointCloud()
orig_pcd.points = o3d.utility.Vector3dVector(pcd_xyz)
orig_pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Grey

# Overlay and show
o3d.visualization.draw_geometries([orig_pcd, pile_pcd])


print("Pile X Range:", pile_xy[:, 0].min(), pile_xy[:, 0].max())
print("Pile Y Range:", pile_xy[:, 1].min(), pile_xy[:, 1].max())
print("PointCloud X Range:", pcd_xyz[:, 0].min(), pcd_xyz[:, 0].max())
print("PointCloud Y Range:", pcd_xyz[:, 1].min(), pcd_xyz[:, 1].max())


#  2D visualization for pile detection results
if 'results' in locals() and results:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Plot 1: Probability heatmap
    sc1 = ax1.scatter(
        results_df['x'], results_df['y'],
        c=results_df['pile_probability'],
        cmap='viridis', s=25, alpha=0.8
    )
    ax1.set_title('Pile Probability Heatmap')
    ax1.set_xlabel('X Coordinate')
    ax1.set_ylabel('Y Coordinate')
    plt.colorbar(sc1, ax=ax1, label='Probability')

    # Plot 2: Confidence-based classification
    ax2.set_title('Pile Classification')
    ax2.set_xlabel('X Coordinate')
    ax2.set_ylabel('Y Coordinate')

    ax2.scatter(
        pile_detections['x'], pile_detections['y'],
        color='darkgreen', label='Pile (> threshold)',
        s=30, alpha=0.8
    )

    if not non_pile_detections.empty:
        ax2.scatter(
            non_pile_detections['x'], non_pile_detections['y'],
            color='gray', label='Non-Pile',
            s=15, alpha=0.4
        )

    ax2.legend(loc='upper right')
    plt.tight_layout()

    # Save the figure
    plot_path = Path(OUTPUT_DIR) / f"{NEW_SITE_NAME}_pile_visualization.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Saved pile visualization to: {plot_path}")

    plt.show()
    mlflow.log_artifact(str(plot_path))

else:
    print("No results available for visualization.")


import matplotlib.pyplot as plt

# Just plot pile detections (results_df is already filtered to valid detections)
plt.figure(figsize=(10, 8))
scatter = plt.scatter(
    results_df['x'], results_df['y'], 
    c=results_df['pile_probability'], cmap='Greens', s=50, edgecolor='k', alpha=0.8
)

# Title & labels
plt.title("Detected Pile Locations on Site", fontsize=16)
plt.xlabel("Site X Coordinate", fontsize=12)
plt.ylabel("Site Y Coordinate", fontsize=12)

# Optional: show confidence colorbar
cbar = plt.colorbar(scatter)
cbar.set_label("Model Confidence (Pile Probability)", fontsize=12)

# Grid and legend (optional)
plt.grid(True, linestyle='--', alpha=0.3)
plt.tight_layout()

# Save and show
plt.savefig("simplified_pile_map.png", dpi=300)
plt.show()


# Export results to file with proper paths
if 'results' in locals() and results:
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = Path(OUTPUT_DIR) / f"{NEW_SITE_NAME}_pile_detections_{timestamp}.csv"
    
    results_df.to_csv(output_file, index=False)
    print(f"Results exported to: {output_file}")
    
    # Create summary statistics
    summary = {
        'site_name': NEW_SITE_NAME,
        'analysis_timestamp': timestamp,
        'total_analysis_points': len(results_df),
        'pile_detections': len(pile_detections),
        'detection_rate': len(pile_detections) / len(results_df),
        'average_pile_probability': float(results_df['pile_probability'].mean()),
        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),
        'model_path': MODEL_PATH,
        'patch_size_meters': PATCH_SIZE,
        'confidence_threshold': CONFIDENCE_THRESHOLD,
        'model_loaded': MODEL_LOADED,
        'point_cloud_loaded': POINT_CLOUD_LOADED
    }
    
    summary_file = Path(OUTPUT_DIR) / f"{NEW_SITE_NAME}_analysis_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Summary statistics saved to: {summary_file}")
    
    # Log to MLflow
    mlflow.log_metric("total_analysis_points", len(results_df))
    mlflow.log_metric("pile_detections", len(pile_detections))
    mlflow.log_metric("detection_rate", len(pile_detections) / len(results_df))
    mlflow.log_metric("average_pile_probability", results_df['pile_probability'].mean())
    mlflow.log_metric("high_confidence_detections", sum(results_df['pile_probability'] > 0.9))
        
    mlflow.log_artifact(str(output_file))
    mlflow.log_artifact(str(summary_file))
else:
    print("No results to export")

def load_dwg_pile_locations(dwg_path):
    """
    Placeholder function to load pile locations from DWG file
    This would need to be implemented based on specific DWG format
    """
    # This is a placeholder - actual implementation would depend on DWG format
    # Could use libraries like ezdxf for DXF files or specific CAD APIs
    
    print("DWG loading not implemented - this is a placeholder")
    print("Actual implementation would parse DWG layers for pile locations")
    
    # Return example format that would be expected
    return np.array([])  # Array of (x, y) coordinates


# Comparison with DWG data (if available)
if DWG_PATH and Path(DWG_PATH).exists():
    dwg_piles = load_dwg_pile_locations(DWG_PATH)
    
    if len(dwg_piles) > 0:
        print(f"Loaded {len(dwg_piles)} pile locations from DWG")
        
        # Find PointNet++ detections near DWG locations
        matches = []
        for dwg_pile in dwg_piles:
            distances = np.sqrt((pile_detections['x'] - dwg_pile[0])**2 + 
                              (pile_detections['y'] - dwg_pile[1])**2)
            min_distance = distances.min() if len(distances) > 0 else float('inf')
            
            if min_distance < PATCH_SIZE:  # Within patch radius
                matches.append(min_distance)
        
        print(f"DWG vs PointNet++ Comparison:")
        print(f"  DWG piles matched: {len(matches)}/{len(dwg_piles)} ({len(matches)/len(dwg_piles)*100:.1f}%)")
        print(f"  Average match distance: {np.mean(matches):.2f}m")
    else:
        print("No pile locations found in DWG file")
else:
    print("DWG file not available for comparison")

# Analysis completion summary and cleanup
print("\n" + "="*50)
print("SITE ANALYSIS COMPLETE")
print("="*50)
print(f"Site: {NEW_SITE_NAME}")
print(f"Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"Model loaded: {MODEL_LOADED}")
print(f"Point cloud loaded: {POINT_CLOUD_LOADED}")

if 'results' in locals() and results:
    print(f"Key findings:")
    print(f"  - Analyzed {len(results_df)} locations")
    print(f"  - Found {len(pile_detections)} potential pile locations")
    print(f"  - Detection confidence: {results_df['pile_probability'].mean():.3f} average")
    print(f"  - Results exported to output directory")
else:
    print("No analysis results generated")

# Close MLflow run
mlflow.end_run()
print("MLflow run completed")

print(f"Output directory: {OUTPUT_DIR}")
