{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.00691, "end_time": "2025-07-28T12:10:42.702067", "exception": false, "start_time": "2025-07-28T12:10:42.695157", "status": "completed"}, "tags": []}, "source": ["# PointNet++ Model Application to New Site\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. <PERSON><PERSON> trained PointNet++ model\n", "2. Process new site point cloud data\n", "3. Generate predictions across the site\n", "4. Compare with DWG-based pile locations (if available)\n", "5. Validate and export results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "41c2968b", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:42.739408Z", "iopub.status.busy": "2025-07-28T12:10:42.739250Z", "iopub.status.idle": "2025-07-28T12:10:43.630983Z", "shell.execute_reply": "2025-07-28T12:10:43.630510Z"}, "papermill": {"duration": 0.926384, "end_time": "2025-07-28T12:10:43.632384", "exception": false, "start_time": "2025-07-28T12:10:42.706000", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: mlflow in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (3.1.1)\r\n", "Requirement already satisfied: mlflow-skinny==3.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.1.1)\r\n", "Requirement already satisfied: Flask<4 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (3.1.1)\r\n", "Requirement already satisfied: alembic!=1.10.0,<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.16.2)\r\n", "Requirement already satisfied: docker<8,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (7.1.0)\r\n", "Requirement already satisfied: graphene<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.4.3)\r\n", "Requirement already satisfied: gunicorn<24 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (23.0.0)\r\n", "Requirement already satisfied: matplotlib<4 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (3.10.3)\r\n", "Requirement already satisfied: numpy<3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.26.4)\r\n", "Requirement already satisfied: pandas<3 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (2.3.0)\r\n", "Requirement already satisfied: pyarrow<21,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (20.0.0)\r\n", "Requirement already satisfied: scikit-learn<2 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (1.7.0)\r\n", "Requirement already satisfied: scipy<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.10.1)\r\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (2.0.41)\r\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: cachetools<7,>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (5.5.2)\r\n", "Requirement already satisfied: click<9,>=7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.2.1)\r\n", "Requirement already satisfied: cloudpickle<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.1)\r\n", "Requirement already satisfied: databricks-sdk<1,>=0.20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.57.0)\r\n", "Requirement already satisfied: fastapi<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.115.14)\r\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.44)\r\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.7.0)\r\n", "Requirement already satisfied: opentelemetry-api<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\r\n", "Requirement already satisfied: opentelemetry-sdk<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\r\n", "Requirement already satisfied: packaging<26 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (25.0)\r\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.20.3)\r\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.11.7)\r\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (6.0.2)\r\n", "Requirement already satisfied: requests<3,>=2.17.3 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.32.4)\r\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.5.3)\r\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (4.14.0)\r\n", "Requirement already satisfied: uvicorn<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.34.3)\r\n", "Requirement already satisfied: <PERSON><PERSON> in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from alembic!=1.10.0,<2->mlflow) (1.3.10)\r\n", "Requirement already satisfied: google-auth~=2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (2.40.3)\r\n", "Requirement already satisfied: urllib3>=1.26.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\r\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from fastapi<1->mlflow-skinny==3.1.1->mlflow) (0.46.2)\r\n", "Requirement already satisfied: blinker>=1.9.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (1.9.0)\r\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (2.2.0)\r\n", "Requirement already satisfied: jinja2>=3.1.2 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.1.6)\r\n", "Requirement already satisfied: markupsafe>=2.1.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.0.2)\r\n", "Requirement already satisfied: werkzeug>=3.1.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.1.3)\r\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (4.0.12)\r\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (5.0.2)\r\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.4.2)\r\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (4.9.1)\r\n", "Requirement already satisfied: graphql-core<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.6)\r\n", "Requirement already satisfied: graphql-relay<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.0)\r\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from graphene<4->mlflow) (2.9.0.post0)\r\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/.local/lib/python3.11/site-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.1.1->mlflow) (3.23.0)\r\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (1.3.2)\r\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (0.12.1)\r\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (4.58.4)\r\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (1.4.8)\r\n", "Requirement already satisfied: pillow>=8 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (11.3.0)\r\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (3.2.3)\r\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.55b1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.1.1->mlflow) (0.55b1)\r\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas<3->mlflow) (2025.2)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas<3->mlflow) (2025.2)\r\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.7.0)\r\n", "Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (2.33.2)\r\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.4.1)\r\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\r\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.4.2)\r\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.10)\r\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (2025.6.15)\r\n", "Requirement already satisfied: pyasn1>=0.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rsa<5,>=3.1.4->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.6.1)\r\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from scikit-learn<2->mlflow) (1.5.1)\r\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from scikit-learn<2->mlflow) (3.6.0)\r\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (4.9.0)\r\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (1.3.1)\r\n", "Requirement already satisfied: h11>=0.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from uvicorn<1->mlflow-skinny==3.1.1->mlflow) (0.16.0)\r\n"]}], "source": ["!python -m pip install mlflow"]}, {"cell_type": "code", "execution_count": 2, "id": "3c38e158", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:43.640128Z", "iopub.status.busy": "2025-07-28T12:10:43.639952Z", "iopub.status.idle": "2025-07-28T12:10:43.902780Z", "shell.execute_reply": "2025-07-28T12:10:43.902187Z"}, "papermill": {"duration": 0.268213, "end_time": "2025-07-28T12:10:43.904242", "exception": false, "start_time": "2025-07-28T12:10:43.636029", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 266624\r\n", "-rw-r--r--@ 1 <USER>  <GROUP>   130M Sep 20  2024 Croped_pointcloud_FLy4_Giorgio.las\r\n"]}], "source": ["!ls -lh ../../../../../data/raw/piani_di_giorgio/pointcloud"]}, {"cell_type": "code", "execution_count": 3, "id": "39959d82", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:43.910846Z", "iopub.status.busy": "2025-07-28T12:10:43.910703Z", "iopub.status.idle": "2025-07-28T12:10:44.156181Z", "shell.execute_reply": "2025-07-28T12:10:44.155712Z"}, "papermill": {"duration": 0.250221, "end_time": "2025-07-28T12:10:44.157405", "exception": false, "start_time": "2025-07-28T12:10:43.907184", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ls: ../pointnet_plus_plus/: No such file or directory\r\n"]}], "source": ["!ls -h ../pointnet_plus_plus/"]}, {"cell_type": "code", "execution_count": 4, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:44.163839Z", "iopub.status.busy": "2025-07-28T12:10:44.163692Z", "iopub.status.idle": "2025-07-28T12:10:44.166545Z", "shell.execute_reply": "2025-07-28T12:10:44.166219Z"}, "papermill": {"duration": 0.007029, "end_time": "2025-07-28T12:10:44.167381", "exception": false, "start_time": "2025-07-28T12:10:44.160352", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters cell for Papermill execution\n", "NEW_SITE_NAME = \"nevados\"  # Update with actual site name\n", "MODEL_PATH = \"best_pointnet_plus_plus.pth\"  # Path to trained model\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nevados/pointcloud/nevados.las\"\n", "\n", "DWG_PATH = \"\"  # Path to DWG file (optional)\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference\"  # Output directory\n", "\n", "# Model parameters (must match training)\n", "CONFIDENCE_THRESHOLD = 0.95  # Confidence threshold for pile detection\n", "BATCH_SIZE = 16  # Batch size for inference\n", "\n", "GRID_SPACING = 5.0 # Grid spacing for analysis points\n", "PATCH_SIZE = 3.0  # meters radius for patch extraction\n", "NUM_POINTS = 128\n", "\n", "# MLflow configuration\n", "EXPERIMENT_NAME = \"pointnet_plus_plus_inference\"\n", "RUN_NAME = f\"inference_{NEW_SITE_NAME}\""]}, {"cell_type": "code", "execution_count": 5, "id": "761e570a", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:44.173160Z", "iopub.status.busy": "2025-07-28T12:10:44.172959Z", "iopub.status.idle": "2025-07-28T12:10:44.174804Z", "shell.execute_reply": "2025-07-28T12:10:44.174563Z"}, "papermill": {"duration": 0.005606, "end_time": "2025-07-28T12:10:44.175635", "exception": false, "start_time": "2025-07-28T12:10:44.170029", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "NEW_SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference/althea_rpcs\"\n", "RUN_NAME = \"inference_althea_rpcs\"\n"]}, {"cell_type": "code", "execution_count": 6, "id": "f22a587b", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:44.181162Z", "iopub.status.busy": "2025-07-28T12:10:44.181073Z", "iopub.status.idle": "2025-07-28T12:10:44.183051Z", "shell.execute_reply": "2025-07-28T12:10:44.182818Z"}, "papermill": {"duration": 0.005539, "end_time": "2025-07-28T12:10:44.183822", "exception": false, "start_time": "2025-07-28T12:10:44.178283", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New Site Analysis Configuration:\n", "Site Name: althea_rpcs\n", "Patch Size: 3.0m radius\n", "Points per Patch: 128\n", "Model Path: best_pointnet_plus_plus.pth\n", "Point Cloud Path: ../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Output Directory: output_runs/pointnet_plus_plus_inference/althea_rpcs\n"]}], "source": ["# Display configuration\n", "print(f\"New Site Analysis Configuration:\")\n", "print(f\"Site Name: {NEW_SITE_NAME}\")\n", "print(f\"Patch Size: {PATCH_SIZE}m radius\")\n", "print(f\"Points per Patch: {NUM_POINTS}\")\n", "print(f\"Model Path: {MODEL_PATH}\")\n", "print(f\"Point Cloud Path: {POINT_CLOUD_PATH}\")\n", "print(f\"Output Directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:44.189064Z", "iopub.status.busy": "2025-07-28T12:10:44.188978Z", "iopub.status.idle": "2025-07-28T12:10:48.381125Z", "shell.execute_reply": "2025-07-28T12:10:48.380805Z"}, "papermill": {"duration": 4.195966, "end_time": "2025-07-28T12:10:48.382312", "exception": false, "start_time": "2025-07-28T12:10:44.186346", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import os\n", "import json\n", "import pickle\n", "import torch\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "import torch.nn as nn\n", "\n", "# Optional libraries\n", "import matplotlib.pyplot as plt\n", "import laspy\n", "import open3d as o3d\n", "import mlflow\n", "import mlflow.pytorch\n", "\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 8, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.387641Z", "iopub.status.busy": "2025-07-28T12:10:48.387411Z", "iopub.status.idle": "2025-07-28T12:10:48.465309Z", "shell.execute_reply": "2025-07-28T12:10:48.464994Z"}, "papermill": {"duration": 0.081476, "end_time": "2025-07-28T12:10:48.466192", "exception": false, "start_time": "2025-07-28T12:10:48.384716", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025/07/28 17:40:48 INFO mlflow.tracking.fluent: Experiment with name 'pointnet_plus_plus_inference' does not exist. Creating a new experiment.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Output directory created: output_runs/pointnet_plus_plus_inference/althea_rpcs\n", "MLflow experiment initialized\n"]}], "source": ["# Setup output directory and initialize MLflow\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"Output directory created: {OUTPUT_DIR}\")\n", "\n", "if mlflow.active_run() is not None:\n", "    print(f\"Ending previous active run: {mlflow.active_run().info.run_id}\")\n", "    mlflow.end_run()\n", "\n", "mlflow.set_experiment(EXPERIMENT_NAME)\n", "mlflow.start_run(run_name=RUN_NAME)\n", "\n", "# Log parameters\n", "mlflow.log_param(\"site_name\", NEW_SITE_NAME)\n", "mlflow.log_param(\"patch_size\", PATCH_SIZE)\n", "mlflow.log_param(\"num_points\", NUM_POINTS)\n", "mlflow.log_param(\"confidence_threshold\", CONFIDENCE_THRESHOLD)\n", "mlflow.log_param(\"batch_size\", BATCH_SIZE)\n", "mlflow.log_param(\"grid_spacing\", GRID_SPACING)\n", "\n", "print(\"MLflow experiment initialized\")"]}, {"cell_type": "code", "execution_count": 9, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.471390Z", "iopub.status.busy": "2025-07-28T12:10:48.471217Z", "iopub.status.idle": "2025-07-28T12:10:48.476366Z", "shell.execute_reply": "2025-07-28T12:10:48.476059Z"}, "papermill": {"duration": 0.008831, "end_time": "2025-07-28T12:10:48.477354", "exception": false, "start_time": "2025-07-28T12:10:48.468523", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# PointNet++ utility functions\n", "def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between two point sets\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Query ball point grouping\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "code", "execution_count": 10, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.482664Z", "iopub.status.busy": "2025-07-28T12:10:48.482495Z", "iopub.status.idle": "2025-07-28T12:10:48.486851Z", "shell.execute_reply": "2025-07-28T12:10:48.486621Z"}, "papermill": {"duration": 0.007963, "end_time": "2025-07-28T12:10:48.487663", "exception": false, "start_time": "2025-07-28T12:10:48.479700", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# PointNet++ Set Abstraction Layer\n", "class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        \"\"\"Forward pass of PointNet++ Set Abstraction layer\"\"\"\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "            \n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "        \n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "        \n", "        return new_xyz, new_points"]}, {"cell_type": "code", "execution_count": 11, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.492754Z", "iopub.status.busy": "2025-07-28T12:10:48.492657Z", "iopub.status.idle": "2025-07-28T12:10:48.495917Z", "shell.execute_reply": "2025-07-28T12:10:48.495656Z"}, "papermill": {"duration": 0.006612, "end_time": "2025-07-28T12:10:48.496689", "exception": false, "start_time": "2025-07-28T12:10:48.490077", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All file paths validated successfully.\n"]}], "source": ["# Validate model and data paths\n", "def validate_paths():\n", "    \"\"\"Validate that required files exist\"\"\"\n", "    issues = []\n", "    \n", "    # Check model path\n", "    if not Path(MODEL_PATH).exists():\n", "        issues.append(f\"Model file not found: {MODEL_PATH}\")\n", "    \n", "    # Check point cloud path if specified\n", "    if POINT_CLOUD_PATH and not Path(POINT_CLOUD_PATH).exists():\n", "        issues.append(f\"Point cloud file not found: {POINT_CLOUD_PATH}\")\n", "    \n", "    # Check DWG path if specified\n", "    if DWG_PATH and not Path(DWG_PATH).exists():\n", "        issues.append(f\"DWG file not found: {DWG_PATH}\")\n", "    \n", "    return issues\n", "\n", "# Run validation\n", "validation_issues = validate_paths()\n", "if validation_issues:\n", "    print(\"Validation Issues Found:\")\n", "    for issue in validation_issues:\n", "        print(f\"  - {issue}\")\n", "    print(\"\\nNote: Some features may not work without proper file paths.\")\n", "else:\n", "    print(\"All file paths validated successfully.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.501542Z", "iopub.status.busy": "2025-07-28T12:10:48.501454Z", "iopub.status.idle": "2025-07-28T12:10:48.504938Z", "shell.execute_reply": "2025-07-28T12:10:48.504702Z"}, "papermill": {"duration": 0.006788, "end_time": "2025-07-28T12:10:48.505732", "exception": false, "start_time": "2025-07-28T12:10:48.498944", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Define complete PointNet++ model\n", "class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(PointNetPlusPlus, self).__init__()\n", "        \n", "        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "        \n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(0.4)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(0.4)\n", "        self.fc3 = nn.Linear(256, num_classes)\n", "    \n", "    def forward(self, xyz):\n", "        if xyz.shape[1] == 3:\n", "            xyz = xyz.transpose(1, 2).contiguous()\n", "        \n", "        B, _, _ = xyz.shape\n", "        \n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "        \n", "        x = l3_points.view(B, 1024)\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "        \n", "        return x\n"]}, {"cell_type": "code", "execution_count": 13, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.510536Z", "iopub.status.busy": "2025-07-28T12:10:48.510448Z", "iopub.status.idle": "2025-07-28T12:10:48.540066Z", "shell.execute_reply": "2025-07-28T12:10:48.539835Z"}, "papermill": {"duration": 0.032846, "end_time": "2025-07-28T12:10:48.540827", "exception": false, "start_time": "2025-07-28T12:10:48.507981", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n", "Loaded trained PointNet++ model from best_pointnet_plus_plus.pth\n", "Model has 1,465,154 parameters\n"]}], "source": ["# Load trained model with error handling\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "try:\n", "    if Path(MODEL_PATH).exists():\n", "        model = PointNetPlusPlus(num_classes=2).to(device)\n", "        model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "        model.eval()\n", "        \n", "        param_count = sum(p.numel() for p in model.parameters())\n", "        print(f\"Loaded trained PointNet++ model from {MODEL_PATH}\")\n", "        print(f\"Model has {param_count:,} parameters\")\n", "        \n", "        mlflow.log_param(\"model_parameters\", param_count)\n", "        mlflow.log_param(\"device\", str(device))\n", "        \n", "        MODEL_LOADED = True\n", "    else:\n", "        print(f\"Model file not found: {MODEL_PATH}\")\n", "        print(\"Creating untrained model for architecture testing...\")\n", "        model = PointNetPlusPlus(num_classes=2).to(device)\n", "        model.eval()\n", "        MODEL_LOADED = False\n", "        \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    print(\"Creating untrained model for architecture testing...\")\n", "    model = PointNetPlusPlus(num_classes=2).to(device)\n", "    model.eval()\n", "    MODEL_LOADED = False"]}, {"cell_type": "code", "execution_count": 14, "id": "2d36b96c", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.546001Z", "iopub.status.busy": "2025-07-28T12:10:48.545886Z", "iopub.status.idle": "2025-07-28T12:10:48.549098Z", "shell.execute_reply": "2025-07-28T12:10:48.548876Z"}, "papermill": {"duration": 0.006638, "end_time": "2025-07-28T12:10:48.549853", "exception": false, "start_time": "2025-07-28T12:10:48.543215", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from pathlib import Path\n", "import numpy as np\n", "\n", "def load_point_cloud(file_path):\n", "    \"\"\"\n", "    Load point cloud from supported formats (.las, .ply)\n", "    Returns numpy array of shape (N, 3) with X, Y, Z coordinates\n", "    \"\"\"\n", "    file_path = Path(file_path)\n", "\n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "\n", "    try:\n", "        suffix = file_path.suffix.lower()\n", "\n", "        if suffix == '.las':\n", "            try:\n", "                import laspy\n", "            except ImportError:\n", "                print(\"laspy not available. Install with: pip install laspy\")\n", "                return None\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded LAS file with {len(points):,} points\")\n", "            return points\n", "\n", "        elif suffix == '.ply':\n", "            try:\n", "                import open3d as o3d\n", "            except ImportError:\n", "                print(\"open3d not available. Install with: pip install open3d\")\n", "                return None\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            print(f\"Loaded PLY file with {len(points):,} points\")\n", "            return points\n", "\n", "        else:\n", "            print(f\"Unsupported file format: {suffix}\")\n", "            return None\n", "\n", "    except Exception as e:\n", "        print(f\"Error loading point cloud: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 15, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:48.555019Z", "iopub.status.busy": "2025-07-28T12:10:48.554917Z", "iopub.status.idle": "2025-07-28T12:10:51.142554Z", "shell.execute_reply": "2025-07-28T12:10:51.142069Z"}, "papermill": {"duration": 2.591504, "end_time": "2025-07-28T12:10:51.143719", "exception": false, "start_time": "2025-07-28T12:10:48.552215", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded LAS file with 52,862,386 points\n", "Point cloud bounds:\n", "  X: 599595.18 to 599866.15\n", "  Y: 4334366.65 to 4334660.84\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Z: 238.63 to 259.18\n"]}], "source": ["# Load new site point cloud with enhanced logging\n", "if POINT_CLOUD_PATH and Path(POINT_CLOUD_PATH).exists():\n", "    point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "    if point_cloud is not None:\n", "        print(f\"Point cloud bounds:\")\n", "        print(f\"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}\")\n", "        print(f\"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}\")\n", "        print(f\"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}\")\n", "        \n", "        # Log to MLflow\n", "        mlflow.log_metric(\"point_cloud_size\", len(point_cloud))\n", "        mlflow.log_metric(\"x_range\", point_cloud[:, 0].max() - point_cloud[:, 0].min())\n", "        mlflow.log_metric(\"y_range\", point_cloud[:, 1].max() - point_cloud[:, 1].min())\n", "        mlflow.log_metric(\"z_range\", point_cloud[:, 2].max() - point_cloud[:, 2].min())\n", "        \n", "        POINT_CLOUD_LOADED = True\n", "    else:\n", "        print(\"Failed to load point cloud - using synthetic data\")\n", "        POINT_CLOUD_LOADED = False\n", "else:\n", "    print(\"Point cloud path not specified or file not found. Using synthetic data for testing.\")\n", "    POINT_CLOUD_LOADED = False\n", "\n", "# Generate synthetic point cloud for testing if needed\n", "if not POINT_CLOUD_LOADED:\n", "    print(\"Generating synthetic point cloud for testing...\")\n", "    np.random.seed(42)\n", "    point_cloud = np.random.randn(10000, 3) * 10  # Smaller for testing\n", "    print(f\"Generated synthetic point cloud with {len(point_cloud):,} points\")"]}, {"cell_type": "code", "execution_count": 16, "id": "89ce4683", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:51.150644Z", "iopub.status.busy": "2025-07-28T12:10:51.150512Z", "iopub.status.idle": "2025-07-28T12:10:51.153744Z", "shell.execute_reply": "2025-07-28T12:10:51.153397Z"}, "papermill": {"duration": 0.007419, "end_time": "2025-07-28T12:10:51.154622", "exception": false, "start_time": "2025-07-28T12:10:51.147203", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Define grid-based patch extraction for site-wide analysis\n", "def create_analysis_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"\n", "    Create a regular grid of analysis points across the site\n", "    \"\"\"\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    # Add padding to ensure coverage\n", "    padding = grid_spacing * 0.5\n", "    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)\n", "    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "\n", "    return grid_points"]}, {"cell_type": "code", "execution_count": 17, "id": "1d9a4e23", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:51.160197Z", "iopub.status.busy": "2025-07-28T12:10:51.160100Z", "iopub.status.idle": "2025-07-28T12:10:51.163726Z", "shell.execute_reply": "2025-07-28T12:10:51.163437Z"}, "papermill": {"duration": 0.007246, "end_time": "2025-07-28T12:10:51.164526", "exception": false, "start_time": "2025-07-28T12:10:51.157280", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Define patch extraction around grid points\n", "def extract_patch_around_point(point_cloud, center_xy, radius=5.0, num_points=256):\n", "    \"\"\"\n", "    Extract a circular patch of points around a center location\n", "    \"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    # Calculate distances to center\n", "    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                       (point_cloud[:, 1] - center_y)**2)\n", "    \n", "    # Select points within radius\n", "    mask = distances <= radius\n", "    patch_points = point_cloud[mask]\n", "    \n", "    if len(patch_points) < 10:\n", "        return None  # Too few points for analysis\n", "    \n", "    # Center the patch\n", "    patch_points[:, 0] -= center_x\n", "    patch_points[:, 1] -= center_y\n", "    \n", "    # Sample or pad to fixed number of points\n", "    if len(patch_points) >= num_points:\n", "        indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "        patch_points = patch_points[indices]\n", "    else:\n", "        # Pad with noisy copies\n", "        n_needed = num_points - len(patch_points)\n", "        noise_scale = 0.01\n", "        for _ in range(n_needed):\n", "            base_idx = np.random.randint(len(patch_points))\n", "            base_point = patch_points[base_idx].copy()\n", "            base_point += np.random.normal(0, noise_scale, 3)\n", "            patch_points = np.vstack([patch_points, base_point])\n", "    \n", "    # Normalize\n", "    max_dist = np.max(np.linalg.norm(patch_points, axis=1))\n", "    if max_dist > 0:\n", "        patch_points = patch_points / max_dist\n", "    \n", "    return patch_points.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 18, "id": "d784765f", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:51.169854Z", "iopub.status.busy": "2025-07-28T12:10:51.169757Z", "iopub.status.idle": "2025-07-28T12:10:51.171862Z", "shell.execute_reply": "2025-07-28T12:10:51.171571Z"}, "papermill": {"duration": 0.005607, "end_time": "2025-07-28T12:10:51.172604", "exception": false, "start_time": "2025-07-28T12:10:51.166997", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def filter_valid_grid_points(point_cloud, grid_points, patch_size, min_points):\n", "    valid_grid = []\n", "    for pt in grid_points:\n", "        patch = extract_patch_around_point(point_cloud, pt, radius=patch_size, num_points=min_points)\n", "        if patch is not None:\n", "            valid_grid.append(pt)\n", "    return np.array(valid_grid)"]}, {"cell_type": "code", "execution_count": 19, "id": "93fee97c", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:10:51.177910Z", "iopub.status.busy": "2025-07-28T12:10:51.177805Z", "iopub.status.idle": "2025-07-28T12:11:56.600889Z", "shell.execute_reply": "2025-07-28T12:11:56.600057Z"}, "papermill": {"duration": 65.431635, "end_time": "2025-07-28T12:11:56.606689", "exception": false, "start_time": "2025-07-28T12:10:51.175054", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud density: 663.12 points/m²\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Average spacing between points: 0.016 m\n"]}], "source": ["# Analyse Density of Point Cloud\n", "total_points = len(point_cloud)\n", "xyz_min = point_cloud[:, :3].min(axis=0)\n", "xyz_max = point_cloud[:, :3].max(axis=0)\n", "bounds = xyz_max - xyz_min\n", "volume = bounds[0] * bounds[1]  # area in 2D\n", "\n", "density_2d = total_points / volume  # points per m²\n", "print(f\"Point cloud density: {density_2d:.2f} points/m²\")\n", "\n", "# Analyse Spacing of Point Cloud\n", "import scipy.spatial\n", "\n", "kdtree = scipy.spatial.cKDTree(point_cloud[:, :3])\n", "dists, _ = kdtree.query(point_cloud[:, :3], k=2)  # k=2 includes self and nearest neighbor\n", "avg_spacing = np.mean(dists[:, 1])  # skip distance to self\n", "print(f\"Average spacing between points: {avg_spacing:.3f} m\")"]}, {"cell_type": "code", "execution_count": 20, "id": "4fcf24bd", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:11:56.613284Z", "iopub.status.busy": "2025-07-28T12:11:56.613137Z", "iopub.status.idle": "2025-07-28T12:29:02.412100Z", "shell.execute_reply": "2025-07-28T12:29:02.411465Z"}, "papermill": {"duration": 1025.803647, "end_time": "2025-07-28T12:29:02.413234", "exception": false, "start_time": "2025-07-28T12:11:56.609587", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created analysis grid with 3360 analysis points\n", "Grid spacing: 5.0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered to 2779 valid analysis points\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Successfully extracted sample patch with shape: (128, 3)\n", "Sample patch statistics:\n", "  X range: -0.011 to 0.012\n", "  Y range: -0.011 to 0.006\n", "  Z range: 1.000 to 1.000\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, grid_spacing=GRID_SPACING)\n", "print(f\"Created analysis grid with {len(grid_points)} analysis points\")\n", "print(f\"Grid spacing: {GRID_SPACING}m\")\n", "\n", "mlflow.log_metric(\"analysis_grid_points\", len(grid_points))\n", "\n", "# Filter invalid ones\n", "valid_grid_points = filter_valid_grid_points(\n", "    point_cloud, grid_points, patch_size=PATCH_SIZE, min_points=NUM_POINTS\n", ")\n", "print(f\"Filtered to {len(valid_grid_points)} valid analysis points\")\n", "\n", "# Extract patches around grid points\n", "sample_center = valid_grid_points[len(valid_grid_points)//2]\n", "#sample_center = grid_points[len(grid_points)//2]  # Middle of grid\n", "sample_patch = extract_patch_around_point(point_cloud, sample_center, \n", "                                        radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "if sample_patch is not None:\n", "    print(f\"Successfully extracted sample patch with shape: {sample_patch.shape}\")\n", "    print(f\"Sample patch statistics:\")\n", "    print(f\"  X range: {sample_patch[:, 0].min():.3f} to {sample_patch[:, 0].max():.3f}\")\n", "    print(f\"  Y range: {sample_patch[:, 1].min():.3f} to {sample_patch[:, 1].max():.3f}\")\n", "    print(f\"  Z range: {sample_patch[:, 2].min():.3f} to {sample_patch[:, 2].max():.3f}\")\n", "else:\n", "    print(\"Failed to extract sample patch - insufficient points in area\")\n"]}, {"cell_type": "code", "execution_count": 21, "id": "207b67ad", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:29:02.419725Z", "iopub.status.busy": "2025-07-28T12:29:02.419578Z", "iopub.status.idle": "2025-07-28T12:29:02.498285Z", "shell.execute_reply": "2025-07-28T12:29:02.498033Z"}, "papermill": {"duration": 0.082984, "end_time": "2025-07-28T12:29:02.499130", "exception": false, "start_time": "2025-07-28T12:29:02.416146", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample patch inference:\n", "  Pile probability: 0.9999\n", "  Prediction: PILE\n", "  Confidence threshold: 0.95\n"]}], "source": ["# Run inference on sample patch\n", "if sample_patch is not None:\n", "    with torch.no_grad():\n", "        # Convert to tensor and add batch dimension\n", "        patch_tensor = torch.FloatTensor(sample_patch).unsqueeze(0).to(device)\n", "        \n", "        # Run inference\n", "        output = model(patch_tensor)\n", "        probabilities = torch.softmax(output, dim=1)\n", "        \n", "        pile_probability = probabilities[0, 1].cpu().item()\n", "        prediction = \"PILE\" if pile_probability > CONFIDENCE_THRESHOLD else \"NON-PILE\"\n", "        \n", "        print(f\"Sample patch inference:\")\n", "        print(f\"  Pile probability: {pile_probability:.4f}\")\n", "        print(f\"  Prediction: {prediction}\")\n", "        print(f\"  Confidence threshold: {CONFIDENCE_THRESHOLD}\")"]}, {"cell_type": "code", "execution_count": 22, "id": "a3695722", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:29:02.505043Z", "iopub.status.busy": "2025-07-28T12:29:02.504938Z", "iopub.status.idle": "2025-07-28T12:29:02.508643Z", "shell.execute_reply": "2025-07-28T12:29:02.508390Z"}, "papermill": {"duration": 0.007528, "end_time": "2025-07-28T12:29:02.509473", "exception": false, "start_time": "2025-07-28T12:29:02.501945", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Batch processing function for site-wide analysis\n", "def process_site_batch(point_cloud, grid_points, model, device, \n", "                      batch_size=32, radius=5.0, num_points=256):\n", "    \"\"\"\n", "    Process multiple grid points in batches for efficiency\n", "    \"\"\"\n", "    results = []\n", "    \n", "    for i in range(0, len(grid_points), batch_size):\n", "        batch_centers = grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_indices = []\n", "        \n", "        # Extract patches for batch\n", "        for j, center in enumerate(batch_centers):\n", "            patch = extract_patch_around_point(point_cloud, center, radius, num_points)\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_indices.append(i + j)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        # Convert to tensor\n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        # Run inference\n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        # Store results\n", "        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):\n", "            center = grid_points[idx]\n", "            results.append({\n", "                'grid_index': idx,\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    return results\n"]}, {"cell_type": "code", "execution_count": 23, "id": "0c3fdb08", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:29:02.515845Z", "iopub.status.busy": "2025-07-28T12:29:02.515669Z", "iopub.status.idle": "2025-07-28T12:39:53.538545Z", "shell.execute_reply": "2025-07-28T12:39:53.537908Z"}, "papermill": {"duration": 651.032084, "end_time": "2025-07-28T12:39:53.544500", "exception": false, "start_time": "2025-07-28T12:29:02.512416", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running site-wide analysis on sample grid...\n", "Grid points: 3360\n", "Using first 2000 points for testing\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1519 analysis points\n", "Analysis Results Summary:\n", "  Total analysis points: 1519\n", "  Pile detections: 1519 (100.0%)\n", "  Non-pile areas: 0 (0.0%)\n", "  Average pile probability: 0.9998\n", "  High confidence piles (>0.9): 1519\n"]}], "source": ["# Run site-wide analysis on subset for testing\n", "print(\"Running site-wide analysis on sample grid...\")\n", "SAMPLE_GRID_SIZE = 2000 # bring it down if its too slow\n", "\n", "print(f\"Grid points: {len(grid_points)}\")\n", "# Use subset for testing (first 100 points)\n", "if len(grid_points) > SAMPLE_GRID_SIZE:\n", "    print(f\"Using first {SAMPLE_GRID_SIZE} points for testing\")\n", "    test_grid = grid_points[:SAMPLE_GRID_SIZE]\n", "else:\n", "    test_grid = grid_points\n", "\n", "results = process_site_batch(point_cloud, test_grid, model, device, \n", "                           batch_size=16, radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "print(f\"Processed {len(results)} analysis points\")\n", "\n", "# %%\n", "# Analyze results\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    non_pile_detections = results_df[results_df['prediction'] == 'NON-PILE']\n", "    \n", "    print(f\"Analysis Results Summary:\")\n", "    print(f\"  Total analysis points: {len(results_df)}\")\n", "    print(f\"  Pile detections: {len(pile_detections)} ({len(pile_detections)/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Non-pile areas: {len(non_pile_detections)} ({len(non_pile_detections)/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average pile probability: {results_df['pile_probability'].mean():.4f}\")\n", "    print(f\"  High confidence piles (>0.9): {sum(results_df['pile_probability'] > 0.9)}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "2c0d34eb", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:39:53.550850Z", "iopub.status.busy": "2025-07-28T12:39:53.550693Z", "iopub.status.idle": "2025-07-28T12:39:53.557421Z", "shell.execute_reply": "2025-07-28T12:39:53.557163Z"}, "papermill": {"duration": 0.010987, "end_time": "2025-07-28T12:39:53.558236", "exception": false, "start_time": "2025-07-28T12:39:53.547249", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   grid_index             x             y  pile_probability prediction\n", "0          10  599592.67945  4.334414e+06          0.999790       PILE\n", "1          13  599592.67945  4.334429e+06          0.999792       PILE\n", "2          14  599592.67945  4.334434e+06          0.999792       PILE\n", "3          16  599592.67945  4.334444e+06          0.999791       PILE\n", "4          19  599592.67945  4.334459e+06          0.999798       PILE\n"]}], "source": ["print(results_df.head())"]}, {"cell_type": "code", "execution_count": 25, "id": "f42268bd", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:39:53.564376Z", "iopub.status.busy": "2025-07-28T12:39:53.564278Z", "iopub.status.idle": "2025-07-28T12:39:53.596847Z", "shell.execute_reply": "2025-07-28T12:39:53.596542Z"}, "papermill": {"duration": 0.036432, "end_time": "2025-07-28T12:39:53.597641", "exception": false, "start_time": "2025-07-28T12:39:53.561209", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point Cloud Bounds (X, Y): [ 599595.17945018 4334366.64733374] [ 599866.15345018 4334660.83633374]\n"]}], "source": ["# Point cloud bounds\n", "pcd_xyz =point_cloud[:, :3]\n", "print(\"Point Cloud Bounds (X, Y):\", pcd_xyz[:, :2].min(axis=0), pcd_xyz[:, :2].max(axis=0))"]}, {"cell_type": "code", "execution_count": 26, "id": "e02d10c1", "metadata": {"execution": {"iopub.execute_input": "2025-07-28T12:39:53.603965Z", "iopub.status.busy": "2025-07-28T12:39:53.603849Z"}, "papermill": {"duration": 7390.146475, "end_time": "2025-07-28T14:43:03.746949", "exception": false, "start_time": "2025-07-28T12:39:53.600474", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from scipy.spatial import cKDTree\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Build KD-tree on XY of original point cloud\n", "pcd_xyz = point_cloud[:, :3]\n", "pcd_tree = cKDTree(pcd_xyz[:, :2])  # only XY for matching\n", "\n", "# Pile X,Y from detection DataFrame\n", "pile_xy = results_df[['x', 'y']].values\n", "\n", "# Find nearest neighbor indices in point cloud\n", "_, idx = pcd_tree.query(pile_xy, k=1)\n", "\n", "# Get correct Z-values from original point cloud\n", "z_vals = pcd_xyz[idx, 2]  # use matched index to extract Z\n", "\n", "# Stack to get full XYZ for pile detections\n", "pile_with_z = np.hstack((pile_xy, z_vals.reshape(-1, 1)))\n", "\n", "# Create red pile point cloud\n", "pile_colors = np.zeros((pile_with_z.shape[0], 3))\n", "pile_colors[:, 0] = 1.0  # Red\n", "\n", "pile_pcd = o3d.geometry.PointCloud()\n", "pile_pcd.points = o3d.utility.Vector3dVector(pile_with_z)\n", "pile_pcd.colors = o3d.utility.Vector3dVector(pile_colors)\n", "\n", "pile_pcd.paint_uniform_color([1, 0, 0])  # Red\n", "pile_pcd.estimate_normals()\n", "pile_pcd.normalize_normals()\n", "\n", "# Convert original point cloud (NumPy) to Open3D\n", "orig_pcd = o3d.geometry.PointCloud()\n", "orig_pcd.points = o3d.utility.Vector3dVector(pcd_xyz)\n", "orig_pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Grey\n", "\n", "# Overlay and show\n", "o3d.visualization.draw_geometries([orig_pcd, pile_pcd])\n"]}, {"cell_type": "code", "execution_count": null, "id": "92bbbae1", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["print(\"Pile X Range:\", pile_xy[:, 0].min(), pile_xy[:, 0].max())\n", "print(\"Pile Y Range:\", pile_xy[:, 1].min(), pile_xy[:, 1].max())\n", "print(\"PointCloud X Range:\", pcd_xyz[:, 0].min(), pcd_xyz[:, 0].max())\n", "print(\"PointCloud Y Range:\", pcd_xyz[:, 1].min(), pcd_xyz[:, 1].max())\n"]}, {"cell_type": "code", "execution_count": null, "id": "c42a903d", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["#  2D visualization for pile detection results\n", "if 'results' in locals() and results:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "    # Plot 1: Probability heatmap\n", "    sc1 = ax1.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='viridis', s=25, alpha=0.8\n", "    )\n", "    ax1.set_title('Pile Probability Heatmap')\n", "    ax1.set_xlabel('X Coordinate')\n", "    ax1.set_ylabel('Y Coordinate')\n", "    plt.colorbar(sc1, ax=ax1, label='Probability')\n", "\n", "    # Plot 2: Confidence-based classification\n", "    ax2.set_title('Pile Classification')\n", "    ax2.set_xlabel('X Coordinate')\n", "    ax2.set_ylabel('Y Coordinate')\n", "\n", "    ax2.scatter(\n", "        pile_detections['x'], pile_detections['y'],\n", "        color='darkgreen', label='Pile (> threshold)',\n", "        s=30, alpha=0.8\n", "    )\n", "\n", "    if not non_pile_detections.empty:\n", "        ax2.scatter(\n", "            non_pile_detections['x'], non_pile_detections['y'],\n", "            color='gray', label='Non-Pile',\n", "            s=15, alpha=0.4\n", "        )\n", "\n", "    ax2.legend(loc='upper right')\n", "    plt.tight_layout()\n", "\n", "    # Save the figure\n", "    plot_path = Path(OUTPUT_DIR) / f\"{NEW_SITE_NAME}_pile_visualization.png\"\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"Saved pile visualization to: {plot_path}\")\n", "\n", "    plt.show()\n", "    mlflow.log_artifact(str(plot_path))\n", "\n", "else:\n", "    print(\"No results available for visualization.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "55494275", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Just plot pile detections (results_df is already filtered to valid detections)\n", "plt.figure(figsize=(10, 8))\n", "scatter = plt.scatter(\n", "    results_df['x'], results_df['y'], \n", "    c=results_df['pile_probability'], cmap='Greens', s=50, edgecolor='k', alpha=0.8\n", ")\n", "\n", "# Title & labels\n", "plt.title(\"Detected Pile Locations on Site\", fontsize=16)\n", "plt.xlabel(\"Site X Coordinate\", fontsize=12)\n", "plt.ylabel(\"Site Y Coordinate\", fontsize=12)\n", "\n", "# Optional: show confidence colorbar\n", "cbar = plt.colorbar(scatter)\n", "cbar.set_label(\"Model Confidence (Pile Probability)\", fontsize=12)\n", "\n", "# Grid and legend (optional)\n", "plt.grid(True, linestyle='--', alpha=0.3)\n", "plt.tight_layout()\n", "\n", "# Save and show\n", "plt.savefig(\"simplified_pile_map.png\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "01c4b32e", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Export results to file with proper paths\n", "if 'results' in locals() and results:\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = Path(OUTPUT_DIR) / f\"{NEW_SITE_NAME}_pile_detections_{timestamp}.csv\"\n", "    \n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Results exported to: {output_file}\")\n", "    \n", "    # Create summary statistics\n", "    summary = {\n", "        'site_name': NEW_SITE_NAME,\n", "        'analysis_timestamp': timestamp,\n", "        'total_analysis_points': len(results_df),\n", "        'pile_detections': len(pile_detections),\n", "        'detection_rate': len(pile_detections) / len(results_df),\n", "        'average_pile_probability': float(results_df['pile_probability'].mean()),\n", "        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),\n", "        'model_path': MODEL_PATH,\n", "        'patch_size_meters': PATCH_SIZE,\n", "        'confidence_threshold': CONFIDENCE_THRESHOLD,\n", "        'model_loaded': MODEL_LOADED,\n", "        'point_cloud_loaded': POINT_CLOUD_LOADED\n", "    }\n", "    \n", "    summary_file = Path(OUTPUT_DIR) / f\"{NEW_SITE_NAME}_analysis_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    print(f\"Summary statistics saved to: {summary_file}\")\n", "    \n", "    # Log to MLflow\n", "    mlflow.log_metric(\"total_analysis_points\", len(results_df))\n", "    mlflow.log_metric(\"pile_detections\", len(pile_detections))\n", "    mlflow.log_metric(\"detection_rate\", len(pile_detections) / len(results_df))\n", "    mlflow.log_metric(\"average_pile_probability\", results_df['pile_probability'].mean())\n", "    mlflow.log_metric(\"high_confidence_detections\", sum(results_df['pile_probability'] > 0.9))\n", "        \n", "    mlflow.log_artifact(str(output_file))\n", "    mlflow.log_artifact(str(summary_file))\n", "else:\n", "    print(\"No results to export\")"]}, {"cell_type": "code", "execution_count": null, "id": "bb1e671e", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_dwg_pile_locations(dwg_path):\n", "    \"\"\"\n", "    Placeholder function to load pile locations from DWG file\n", "    This would need to be implemented based on specific DWG format\n", "    \"\"\"\n", "    # This is a placeholder - actual implementation would depend on DWG format\n", "    # Could use libraries like ezdxf for DXF files or specific CAD APIs\n", "    \n", "    print(\"DWG loading not implemented - this is a placeholder\")\n", "    print(\"Actual implementation would parse DWG layers for pile locations\")\n", "    \n", "    # Return example format that would be expected\n", "    return np.array([])  # Array of (x, y) coordinates\n"]}, {"cell_type": "code", "execution_count": null, "id": "e52b8dbc", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Comparison with DWG data (if available)\n", "if DWG_PATH and Path(DWG_PATH).exists():\n", "    dwg_piles = load_dwg_pile_locations(DWG_PATH)\n", "    \n", "    if len(dwg_piles) > 0:\n", "        print(f\"Loaded {len(dwg_piles)} pile locations from DWG\")\n", "        \n", "        # Find PointNet++ detections near DWG locations\n", "        matches = []\n", "        for dwg_pile in dwg_piles:\n", "            distances = np.sqrt((pile_detections['x'] - dwg_pile[0])**2 + \n", "                              (pile_detections['y'] - dwg_pile[1])**2)\n", "            min_distance = distances.min() if len(distances) > 0 else float('inf')\n", "            \n", "            if min_distance < PATCH_SIZE:  # Within patch radius\n", "                matches.append(min_distance)\n", "        \n", "        print(f\"DWG vs PointNet++ Comparison:\")\n", "        print(f\"  DWG piles matched: {len(matches)}/{len(dwg_piles)} ({len(matches)/len(dwg_piles)*100:.1f}%)\")\n", "        print(f\"  Average match distance: {np.mean(matches):.2f}m\")\n", "    else:\n", "        print(\"No pile locations found in DWG file\")\n", "else:\n", "    print(\"DWG file not available for comparison\")"]}, {"cell_type": "code", "execution_count": null, "id": "07e88f8c", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Analysis completion summary and cleanup\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"SITE ANALYSIS COMPLETE\")\n", "print(\"=\"*50)\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"Model loaded: {MODEL_LOADED}\")\n", "print(f\"Point cloud loaded: {POINT_CLOUD_LOADED}\")\n", "\n", "if 'results' in locals() and results:\n", "    print(f\"Key findings:\")\n", "    print(f\"  - Analyzed {len(results_df)} locations\")\n", "    print(f\"  - Found {len(pile_detections)} potential pile locations\")\n", "    print(f\"  - Detection confidence: {results_df['pile_probability'].mean():.3f} average\")\n", "    print(f\"  - Results exported to output directory\")\n", "else:\n", "    print(\"No analysis results generated\")\n", "\n", "# Close MLflow run\n", "mlflow.end_run()\n", "print(\"MLflow run completed\")\n", "\n", "print(f\"Output directory: {OUTPUT_DIR}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 9144.889851, "end_time": "2025-07-28T14:43:06.709422", "environment_variables": {}, "exception": null, "input_path": "02_pointnet_plus_plus_inference.ipynb", "output_path": "02_pointnet_plus_plus_inference_althea_rpcs_executed.ipynb", "parameters": {"NEW_SITE_NAME": "althea_rpcs", "OUTPUT_DIR": "output_runs/pointnet_plus_plus_inference/althea_rpcs", "POINT_CLOUD_PATH": "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las", "RUN_NAME": "inference_althea_rpcs"}, "start_time": "2025-07-28T12:10:41.819571", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}