{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.012717, "end_time": "2025-08-06T15:00:28.972199", "exception": false, "start_time": "2025-08-06T15:00:28.959482", "status": "completed"}, "tags": []}, "source": ["# PointNet++ Model Application to New Site\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. <PERSON><PERSON> trained PointNet++ model\n", "2. Process new site point cloud data\n", "3. Generate predictions across the site\n", "4. Compare with DWG-based pile locations (if available)\n", "5. Validate and export results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:28.980731Z", "iopub.status.busy": "2025-08-06T15:00:28.980450Z", "iopub.status.idle": "2025-08-06T15:00:28.987687Z", "shell.execute_reply": "2025-08-06T15:00:28.987302Z"}, "papermill": {"duration": 0.012843, "end_time": "2025-08-06T15:00:28.989034", "exception": false, "start_time": "2025-08-06T15:00:28.976191", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters cell for Papermill execution\n", "NEW_SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = f\"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}\"\n", "\n", "MODEL_PATH = \"best_pointnet_iter4.pth\"  # Path to trained model\n", "\n", "DWG_PATH = \"\"  # Path to DWG file (optional)\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference\"  # Output directory\n", "\n", "# Model parameters (must match training)\n", "CONFIDENCE_THRESHOLD = 0.5  # Confidence threshold for pile detection\n", "\n", "GRID_SPACING = 25.0 # Grid spacing for analysis points\n", "PATCH_SIZE = 20.0  # meters radius for patch extraction\n", "NUM_POINTS = 1024 # KEEP 1024 to match training - CRITICAL for accuracy\n", "BATCH_SIZE = 8  # Batch size for inference\n", "\n", "# Enhanced analysis parameters\n", "EXTENDED_ANALYSIS = True\n", "NEGATIVE_SAMPLE_DISTANCE = 8.0  # Distance for synthetic negatives\n", "FULL_SITE_ANALYSIS = True  # Run on all points, not subset\n", "\n", "# MLflow configuration\n", "EXPERIMENT_NAME = \"pointnet_plus_plus_inference\"\n", "RUN_NAME = f\"inference_{NEW_SITE_NAME}\""]}, {"cell_type": "code", "execution_count": 2, "id": "2077fad6", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:29.021860Z", "iopub.status.busy": "2025-08-06T15:00:29.021701Z", "iopub.status.idle": "2025-08-06T15:00:29.023669Z", "shell.execute_reply": "2025-08-06T15:00:29.023424Z"}, "papermill": {"duration": 0.006123, "end_time": "2025-08-06T15:00:29.024622", "exception": false, "start_time": "2025-08-06T15:00:29.018499", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "NEW_SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference/althea_rpcs\"\n", "RUN_NAME = \"inference_althea_rpcs\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f22a587b", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:29.029244Z", "iopub.status.busy": "2025-08-06T15:00:29.029101Z", "iopub.status.idle": "2025-08-06T15:00:29.031610Z", "shell.execute_reply": "2025-08-06T15:00:29.031385Z"}, "papermill": {"duration": 0.005777, "end_time": "2025-08-06T15:00:29.032457", "exception": false, "start_time": "2025-08-06T15:00:29.026680", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New Site Analysis Configuration:\n", "Site Name: althea_rpcs\n", "Patch Size: 20.0m radius\n", "Points per Patch: 1024\n", "Model Path: best_pointnet_iter4.pth\n", "Point Cloud Path: ../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Output Directory: output_runs/pointnet_plus_plus_inference/althea_rpcs\n", "Extended Analysis: True\n", "Full Site Analysis: True\n"]}], "source": ["# Display configuration\n", "print(f\"New Site Analysis Configuration:\")\n", "print(f\"Site Name: {NEW_SITE_NAME}\")\n", "print(f\"Patch Size: {PATCH_SIZE}m radius\")\n", "print(f\"Points per Patch: {NUM_POINTS}\")\n", "print(f\"Model Path: {MODEL_PATH}\")\n", "print(f\"Point Cloud Path: {POINT_CLOUD_PATH}\")\n", "print(f\"Output Directory: {OUTPUT_DIR}\")\n", "print(f\"Extended Analysis: {EXTENDED_ANALYSIS}\")\n", "print(f\"Full Site Analysis: {FULL_SITE_ANALYSIS}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:29.036775Z", "iopub.status.busy": "2025-08-06T15:00:29.036657Z", "iopub.status.idle": "2025-08-06T15:00:33.355263Z", "shell.execute_reply": "2025-08-06T15:00:33.354972Z"}, "papermill": {"duration": 4.321897, "end_time": "2025-08-06T15:00:33.356300", "exception": false, "start_time": "2025-08-06T15:00:29.034403", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import os\n", "import json\n", "import pickle\n", "import torch\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "import torch.nn as nn\n", "import matplotlib.pyplot as plt\n", "import laspy\n", "import open3d as o3d\n", "import mlflow\n", "import mlflow.pytorch\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import pdist\n", "import seaborn as sns\n", "\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 5, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.360625Z", "iopub.status.busy": "2025-08-06T15:00:33.360448Z", "iopub.status.idle": "2025-08-06T15:00:33.363330Z", "shell.execute_reply": "2025-08-06T15:00:33.363079Z"}, "papermill": {"duration": 0.005794, "end_time": "2025-08-06T15:00:33.364122", "exception": false, "start_time": "2025-08-06T15:00:33.358328", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def setup_environment():\n", "    \"\"\"Setup output directory and MLflow tracking\"\"\"\n", "    os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "    \n", "    if mlflow.active_run() is not None:\n", "        mlflow.end_run()\n", "    \n", "    mlflow.set_experiment(EXPERIMENT_NAME)\n", "    mlflow.start_run(run_name=RUN_NAME)\n", "    \n", "    # Log parameters\n", "    params = {\n", "        \"site_name\": NEW_SITE_NAME,\n", "        \"patch_size\": PATCH_SIZE,\n", "        \"num_points\": NUM_POINTS,\n", "        \"confidence_threshold\": CONFIDENCE_THRESHOLD,\n", "        \"batch_size\": BATCH_SIZE,\n", "        \"grid_spacing\": GRID_SPACING\n", "    }\n", "    for key, value in params.items():\n", "        mlflow.log_param(key, value)"]}, {"cell_type": "markdown", "id": "a0315f2c", "metadata": {"papermill": {"duration": 0.001783, "end_time": "2025-08-06T15:00:33.367650", "exception": false, "start_time": "2025-08-06T15:00:33.365867", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Utility Functions "]}, {"cell_type": "code", "execution_count": 6, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.371608Z", "iopub.status.busy": "2025-08-06T15:00:33.371496Z", "iopub.status.idle": "2025-08-06T15:00:33.376460Z", "shell.execute_reply": "2025-08-06T15:00:33.376185Z"}, "papermill": {"duration": 0.008017, "end_time": "2025-08-06T15:00:33.377258", "exception": false, "start_time": "2025-08-06T15:00:33.369241", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# PointNet++ utility functions\n", "def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between two point sets\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Query ball point grouping\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "markdown", "id": "8cbfede7", "metadata": {"papermill": {"duration": 0.001555, "end_time": "2025-08-06T15:00:33.380590", "exception": false, "start_time": "2025-08-06T15:00:33.379035", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Set Abstraction Layer\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.384698Z", "iopub.status.busy": "2025-08-06T15:00:33.384506Z", "iopub.status.idle": "2025-08-06T15:00:33.389018Z", "shell.execute_reply": "2025-08-06T15:00:33.388751Z"}, "papermill": {"duration": 0.007517, "end_time": "2025-08-06T15:00:33.389920", "exception": false, "start_time": "2025-08-06T15:00:33.382403", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        \"\"\"Forward pass of PointNet++ Set Abstraction layer\"\"\"\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "            \n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "        \n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "        \n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "id": "d5232c15", "metadata": {"papermill": {"duration": 0.001686, "end_time": "2025-08-06T15:00:33.393383", "exception": false, "start_time": "2025-08-06T15:00:33.391697", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Model Architecture\n"]}, {"cell_type": "code", "execution_count": 8, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.397515Z", "iopub.status.busy": "2025-08-06T15:00:33.397297Z", "iopub.status.idle": "2025-08-06T15:00:33.401737Z", "shell.execute_reply": "2025-08-06T15:00:33.401483Z"}, "papermill": {"duration": 0.007337, "end_time": "2025-08-06T15:00:33.402578", "exception": false, "start_time": "2025-08-06T15:00:33.395241", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):  # ✅ CRITICAL: in_channels=20\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set Abstraction Layers (SAME AS TRAINING)\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        # Classification head (SAME AS TRAINING)\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(dropout)\n", "\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(dropout)\n", "\n", "        self.fc3 = nn.Linear(256, 64)\n", "        self.bn3 = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "        self.drop3 = nn.Dropout(dropout * 0.6)\n", "\n", "        self.fc4 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        # Input shape: (B, N, C), C = in_channels (20 features)\n", "        if len(xyz.shape) == 4:\n", "            xyz = xyz.squeeze(1)\n", "\n", "        B, N, C = xyz.shape\n", "\n", "        # Split input into xyz coords and features (SAME AS TRAINING)\n", "        coords = xyz[:, :, :3]       # (B, N, 3)\n", "        features = xyz[:, :, 3:]     # (B, N, C-3) = (B, N, 17)\n", "\n", "        # Pass through SA layers\n", "        l1_xyz, l1_points = self.sa1(coords, features)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        global_feat = l3_points.view(B, -1)\n", "\n", "        # Classification head\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))\n", "        x = self.fc4(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "id": "2f5e408b", "metadata": {"papermill": {"duration": 0.00154, "end_time": "2025-08-06T15:00:33.405888", "exception": false, "start_time": "2025-08-06T15:00:33.404348", "status": "completed"}, "tags": []}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 9, "id": "3838119b", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.410379Z", "iopub.status.busy": "2025-08-06T15:00:33.410252Z", "iopub.status.idle": "2025-08-06T15:00:33.414094Z", "shell.execute_reply": "2025-08-06T15:00:33.413821Z"}, "papermill": {"duration": 0.007186, "end_time": "2025-08-06T15:00:33.414932", "exception": false, "start_time": "2025-08-06T15:00:33.407746", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from supported formats (.las, .ply, .pcd)\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    suffix = file_path.suffix.lower()\n", "    \n", "    try:\n", "        if suffix == '.las':\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded LAS file with {len(points):,} points\")\n", "                \n", "        elif suffix in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            \n", "            if len(points) == 0:\n", "                print(f\"Warning: {suffix.upper()} file contains no points\")\n", "                return None\n", "                \n", "            print(f\"Loaded {suffix.upper()} file with {len(points):,} points\")\n", "                \n", "        else:\n", "            print(f\"Unsupported file format: {suffix}. Supported formats: .las, .ply, .pcd\")\n", "            return None\n", "        \n", "        if len(points) == 0:\n", "            print(\"Warning: Point cloud contains no points\")\n", "            return None\n", "            \n", "        print(f\"Point cloud bounds:\")\n", "        print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "        print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "        print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "        \n", "        return points\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading point cloud: {e}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 10, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.419078Z", "iopub.status.busy": "2025-08-06T15:00:33.418929Z", "iopub.status.idle": "2025-08-06T15:00:33.422144Z", "shell.execute_reply": "2025-08-06T15:00:33.421808Z"}, "papermill": {"duration": 0.006314, "end_time": "2025-08-06T15:00:33.423135", "exception": false, "start_time": "2025-08-06T15:00:33.416821", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    \"\"\"Load trained PointNet++ model\"\"\"\n", "    try:\n", "        model = PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device)\n", "        \n", "        if Path(model_path).exists():\n", "            checkpoint = torch.load(model_path, map_location=device)\n", "            \n", "            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:\n", "                model.load_state_dict(checkpoint['model_state_dict'])\n", "                print(f\"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}\")\n", "            else:\n", "                model.load_state_dict(checkpoint)\n", "            \n", "            model.eval()\n", "            param_count = sum(p.numel() for p in model.parameters())\n", "            print(f\"Loaded trained PointNet++ model with {param_count:,} parameters\")\n", "            return model, True\n", "        else:\n", "            print(f\"Model file not found: {model_path}\")\n", "            return model, False\n", "            \n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device), False"]}, {"cell_type": "code", "execution_count": 11, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.427211Z", "iopub.status.busy": "2025-08-06T15:00:33.427047Z", "iopub.status.idle": "2025-08-06T15:00:33.429956Z", "shell.execute_reply": "2025-08-06T15:00:33.429678Z"}, "papermill": {"duration": 0.005739, "end_time": "2025-08-06T15:00:33.430763", "exception": false, "start_time": "2025-08-06T15:00:33.425024", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=15.0, buffer_factor=1.5):\n", "    \"\"\"Create analysis grid with spatial bounds optimization\"\"\"\n", "    print(f\"Point cloud has {len(point_cloud):,} points\")\n", "    \n", "    # Get bounds efficiently\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    print(f\"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})\")\n", "    \n", "    buffer = grid_spacing * buffer_factor\n", "    x_coords = np.arange(x_min - buffer, x_max + buffer, grid_spacing)\n", "    y_coords = np.arange(y_min - buffer, y_max + buffer, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "    print(f\"Created analysis grid with {len(grid_points)} points\")\n", "    \n", "    return grid_points\n"]}, {"cell_type": "code", "execution_count": 12, "id": "2d36b96c", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.434716Z", "iopub.status.busy": "2025-08-06T15:00:33.434586Z", "iopub.status.idle": "2025-08-06T15:00:33.440904Z", "shell.execute_reply": "2025-08-06T15:00:33.440635Z"}, "papermill": {"duration": 0.009395, "end_time": "2025-08-06T15:00:33.441854", "exception": false, "start_time": "2025-08-06T15:00:33.432459", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_features(point_cloud, center_xy, radius=20.0, num_points=1024, kdtree=None):\n", "    \"\"\"Extract patch features EXACTLY matching training preprocessing\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    if kdtree is not None:\n", "        indices = kdtree.query_ball_point([center_x, center_y], radius)\n", "        if len(indices) < 50:\n", "            return None\n", "        patch_xyz = point_cloud[indices]\n", "    else:\n", "        distances_2d = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                              (point_cloud[:, 1] - center_y)**2)\n", "        mask = distances_2d <= radius\n", "        patch_xyz = point_cloud[mask]\n", "        \n", "        if len(patch_xyz) < 50:\n", "            return None\n", "    \n", "    # MATCH training preprocessing exactly - use same feature engineering as training data creation\n", "    x, y, z = patch_xyz[:, 0], patch_xyz[:, 1], patch_xyz[:, 2]\n", "    \n", "    # Calculate patch statistics\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_mean, z_std = z.mean(), z.std() + 1e-6\n", "    z_min, z_max = z.min(), z.max()\n", "    \n", "    # Distance calculations\n", "    dist_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)\n", "    \n", "    # RECREATE EXACT TRAINING FEATURES - these come from the original data creation process\n", "    features_list = []\n", "    for i, point in enumerate(patch_xyz):\n", "        px, py, pz = point\n", "        \n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    \n", "    # EXACT SAME SAMPLING LOGIC AS TRAINING\n", "    if len(patch_features) >= num_points:\n", "        # Distance-weighted sampling like training\n", "        distances = patch_features[:, 4]  # distance_norm column\n", "        probabilities = 1 / (distances + 0.1)\n", "        probabilities /= probabilities.sum()\n", "        \n", "        sampled_indices = np.random.choice(len(patch_features), num_points, \n", "                                         replace=False, p=probabilities)\n", "        sampled = patch_features[sampled_indices]\n", "    else:\n", "        # Upsample with weighted selection like training\n", "        upsampled = patch_features.copy()\n", "        needed = num_points - len(patch_features)\n", "        \n", "        for _ in range(needed):\n", "            distances = patch_features[:, 4]\n", "            weights = 1 / (distances + 0.1)\n", "            weights /= weights.sum()\n", "            source_idx = np.random.choice(len(patch_features), p=weights)\n", "            \n", "            new_point = patch_features[source_idx].copy()\n", "            new_point[:3] += np.random.normal(0, 0.02, 3)  # Add noise to spatial like training\n", "            upsampled = np.vstack([upsampled, new_point])\n", "        \n", "        sampled = upsampled[:num_points]\n", "    \n", "    # EXACT SAME NORMALIZATION AS TRAINING\n", "    # Training shows: sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1\n", "    spatial_coords = sampled[:, :3]\n", "    spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if spatial_extent > 0:\n", "        sampled[:, :3] /= spatial_extent\n", "    \n", "    return sampled\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.446185Z", "iopub.status.busy": "2025-08-06T15:00:33.446045Z", "iopub.status.idle": "2025-08-06T15:00:33.451109Z", "shell.execute_reply": "2025-08-06T15:00:33.450783Z"}, "papermill": {"duration": 0.008115, "end_time": "2025-08-06T15:00:33.452004", "exception": false, "start_time": "2025-08-06T15:00:33.443889", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_inference(point_cloud, grid_points, model, device, \n", "                          batch_size=16, radius=20.0, num_points=1024):\n", "    \"\"\"CPU-optimized inference maintaining 1024 points for accuracy\"\"\"\n", "    print(f\"Building spatial index for {len(point_cloud):,} points...\")\n", "    kdtree = cKDTree(point_cloud[:, :2])\n", "    \n", "    print(\"Pre-filtering valid grid points...\")\n", "    valid_grid_points = []\n", "    for i, center in enumerate(grid_points):\n", "        if i % 500 == 0:\n", "            print(f\"  Pre-filtering progress: {i}/{len(grid_points)}\")\n", "        \n", "        indices = kdtree.query_ball_point([center[0], center[1]], radius)\n", "        if len(indices) >= 50:  # Keep higher threshold for 1024 points\n", "            valid_grid_points.append(center)\n", "    \n", "    valid_grid_points = np.array(valid_grid_points)\n", "    print(f\"Filtered to {len(valid_grid_points)} valid grid points (from {len(grid_points)})\")\n", "    \n", "    if len(valid_grid_points) == 0:\n", "        return pd.DataFrame()\n", "    \n", "    results = []\n", "    total_batches = len(valid_grid_points) // batch_size + (1 if len(valid_grid_points) % batch_size != 0 else 0)\n", "    \n", "    print(f\"Processing {len(valid_grid_points)} points with 1024 points/patch (CPU optimized)...\")\n", "    print(f\"This maintains training accuracy but will be slower than 512 points\")\n", "    \n", "    model.eval()\n", "    if device.type == 'cpu':\n", "        torch.set_num_threads(4)\n", "    \n", "    for i in range(0, len(valid_grid_points), batch_size):\n", "        batch_idx = i // batch_size + 1\n", "        if batch_idx % 3 == 1:  # More frequent updates since batches are smaller\n", "            print(f\"  Batch {batch_idx}/{total_batches} ({i}/{len(valid_grid_points)} points)\")\n", "        \n", "        batch_centers = valid_grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_centers = []\n", "        \n", "        for center in batch_centers:\n", "            patch = extract_patch_features(point_cloud, center, radius, num_points, kdtree)\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_centers.append(center)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        for center, prob in zip(valid_centers, pile_probs):\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    print(f\"CPU processing complete: {len(results)} successful predictions\")\n", "    return pd.DataFrame(results)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "89ce4683", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.456704Z", "iopub.status.busy": "2025-08-06T15:00:33.456592Z", "iopub.status.idle": "2025-08-06T15:00:33.460129Z", "shell.execute_reply": "2025-08-06T15:00:33.459873Z"}, "papermill": {"duration": 0.006637, "end_time": "2025-08-06T15:00:33.460853", "exception": false, "start_time": "2025-08-06T15:00:33.454216", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def visualize_results(results_df, output_dir, site_name):\n", "    \"\"\"Create visualization of pile detection results\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to visualize\")\n", "        return\n", "    \n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Probability heatmap\n", "    sc1 = ax1.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='viridis', s=25, alpha=0.8\n", "    )\n", "    ax1.set_title('Pile Probability Heatmap')\n", "    ax1.set_xlabel('X Coordinate')\n", "    ax1.set_ylabel('Y Coordinate')\n", "    plt.colorbar(sc1, ax=ax1, label='Probability')\n", "    \n", "    # Plot 2: Pile classifications\n", "    ax2.scatter(\n", "        pile_detections['x'], pile_detections['y'],\n", "        color='darkgreen', label='Pile Detections',\n", "        s=30, alpha=0.8\n", "    )\n", "    \n", "    non_pile = results_df[results_df['prediction'] == 'NON-PILE']\n", "    if not non_pile.empty:\n", "        ax2.scatter(\n", "            non_pile['x'], non_pile['y'],\n", "            color='gray', label='Non-Pile',\n", "            s=15, alpha=0.4\n", "        )\n", "    \n", "    ax2.set_title('Pile Classification Results')\n", "    ax2.set_xlabel('X Coordinate')\n", "    ax2.set_ylabel('Y Coordinate')\n", "    ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    \n", "    plot_path = Path(output_dir) / f\"{site_name}_pile_visualization.png\"\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"Saved visualization to: {plot_path}\")\n", "    \n", "    plt.show()\n", "    return str(plot_path)"]}, {"cell_type": "code", "execution_count": 15, "id": "1d9a4e23", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.465402Z", "iopub.status.busy": "2025-08-06T15:00:33.465303Z", "iopub.status.idle": "2025-08-06T15:00:33.468858Z", "shell.execute_reply": "2025-08-06T15:00:33.468502Z"}, "papermill": {"duration": 0.006742, "end_time": "2025-08-06T15:00:33.469775", "exception": false, "start_time": "2025-08-06T15:00:33.463033", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def export_results(results_df, output_dir, site_name, model_path, patch_size, confidence_threshold):\n", "    \"\"\"Export results and summary statistics\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to export\")\n", "        return\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # Export main results\n", "    output_file = Path(output_dir) / f\"{site_name}_pile_detections_{timestamp}.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Results exported to: {output_file}\")\n", "    \n", "    # Create summary statistics\n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    summary = {\n", "        'site_name': site_name,\n", "        'analysis_timestamp': timestamp,\n", "        'total_analysis_points': len(results_df),\n", "        'pile_detections': len(pile_detections),\n", "        'detection_rate': len(pile_detections) / len(results_df),\n", "        'average_pile_probability': float(results_df['pile_probability'].mean()),\n", "        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),\n", "        'model_path': model_path,\n", "        'patch_size_meters': patch_size,\n", "        'confidence_threshold': confidence_threshold\n", "    }\n", "    \n", "    summary_file = Path(output_dir) / f\"{site_name}_analysis_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    print(f\"Summary statistics saved to: {summary_file}\")\n", "    \n", "    # Log to MLflow\n", "    for key, value in summary.items():\n", "        if isinstance(value, (int, float)):\n", "            mlflow.log_metric(key, value)\n", "    \n", "    mlflow.log_artifact(str(output_file))\n", "    mlflow.log_artifact(str(summary_file))\n", "    \n", "    return summary\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "id": "4a52cac4", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.473901Z", "iopub.status.busy": "2025-08-06T15:00:33.473687Z", "iopub.status.idle": "2025-08-06T15:00:33.555109Z", "shell.execute_reply": "2025-08-06T15:00:33.554686Z"}, "papermill": {"duration": 0.084271, "end_time": "2025-08-06T15:00:33.555954", "exception": false, "start_time": "2025-08-06T15:00:33.471683", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting PointNet++ Pile Detection Pipeline\n", "Site: althea_rpcs\n", "Configuration: 20.0m patches, 1024 points each\n", "Using device: cpu\n"]}], "source": ["print(\"Starting PointNet++ Pile Detection Pipeline\")\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Configuration: {PATCH_SIZE}m patches, {NUM_POINTS} points each\")\n", "    \n", "# Setup\n", "setup_environment()\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "6bb565de", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:33.560824Z", "iopub.status.busy": "2025-08-06T15:00:33.560690Z", "iopub.status.idle": "2025-08-06T15:00:35.784783Z", "shell.execute_reply": "2025-08-06T15:00:35.784091Z"}, "papermill": {"duration": 2.228569, "end_time": "2025-08-06T15:00:35.786634", "exception": false, "start_time": "2025-08-06T15:00:33.558065", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded checkpoint from epoch 97\n", "Loaded trained PointNet++ model with 1,482,434 parameters\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded LAS file with 52,862,386 points\n", "Point cloud bounds:\n", "  X: 599595.18 to 599866.15\n", "  Y: 4334366.65 to 4334660.84\n", "  Z: 238.63 to 259.18\n"]}], "source": ["# Load model\n", "model, model_loaded = load_model(MODEL_PATH, device)\n", "    \n", "# Load point cloud\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "if point_cloud is None:\n", "    print(\"Failed to load point cloud. Exiting.\")\n", "    mlflow.end_run()    \n"]}, {"cell_type": "code", "execution_count": 18, "id": "703fc072", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:35.794090Z", "iopub.status.busy": "2025-08-06T15:00:35.793922Z", "iopub.status.idle": "2025-08-06T15:00:35.826237Z", "shell.execute_reply": "2025-08-06T15:00:35.825851Z"}, "papermill": {"duration": 0.036455, "end_time": "2025-08-06T15:00:35.827066", "exception": false, "start_time": "2025-08-06T15:00:35.790611", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud has 52,862,386 points\n", "Point cloud bounds: X(599595.2, 599866.2), Y(4334366.6, 4334660.8)\n", "Created analysis grid with 210 points\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)"]}, {"cell_type": "code", "execution_count": 19, "id": "98181738", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:00:35.831440Z", "iopub.status.busy": "2025-08-06T15:00:35.831323Z", "iopub.status.idle": "2025-08-06T15:10:24.182021Z", "shell.execute_reply": "2025-08-06T15:10:24.181597Z"}, "papermill": {"duration": 588.354056, "end_time": "2025-08-06T15:10:24.183137", "exception": false, "start_time": "2025-08-06T15:00:35.829081", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Building spatial index for 52,862,386 points...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Pre-filtering valid grid points...\n", "  Pre-filtering progress: 0/210\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered to 153 valid grid points (from 210)\n", "Processing 153 points with 1024 points/patch (CPU optimized)...\n", "This maintains training accuracy but will be slower than 512 points\n", "  Batch 1/20 (0/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 4/20 (24/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 7/20 (48/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON>ch 10/20 (72/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON>ch 13/20 (96/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 16/20 (120/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 19/20 (144/153 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CPU processing complete: 153 successful predictions\n"]}], "source": ["# Run inference\n", "results_df = process_site_inference(\n", "    point_cloud, grid_points, model, device,\n", "    batch_size=BATCH_SIZE, radius=PATCH_SIZE, num_points=NUM_POINTS\n", ")\n"]}, {"cell_type": "code", "execution_count": 20, "id": "8e8f2c88", "metadata": {"execution": {"iopub.execute_input": "2025-08-06T15:10:24.189939Z", "iopub.status.busy": "2025-08-06T15:10:24.189809Z", "iopub.status.idle": "2025-08-06T15:10:24.685212Z", "shell.execute_reply": "2025-08-06T15:10:24.684913Z"}, "papermill": {"duration": 0.499386, "end_time": "2025-08-06T15:10:24.686152", "exception": false, "start_time": "2025-08-06T15:10:24.186766", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analysis Results:\n", "  Total analysis points: 153\n", "  Pile detections: 153 (100.0%)\n", "  Average confidence: 1.0000\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved visualization to: output_runs/pointnet_plus_plus_inference/althea_rpcs/althea_rpcs_pile_visualization.png\n"]}, {"data": {"image/png": "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******************************++ArZvHmzT6xxcXHm4bNvRYWZ//zVV1+Vs88+u3b7pEmTTKf3m2++GfB9ysrKZNeuXWbKlptvvlneeecdWblyZcD9+/XrZ6Zv0c78Dz/8UEaMGCF79uzxGY3etm1bue6668yiowgtR/scKykvkZmLZpoFrXQ+Tr2dWEdD6cVcYlyihAq3xOGmWIjDPm6JxS1xuCkWt8ThpliIwz4lDsdyqG1MOtEtuegFAAChzYb2hLcMF14d/E705/9xxSHHqlOv9O/fXx5++GHzXKdo0bnJdYS5do7/FJ3bvFu3bnL++efL3Xff7XefoqIic8w77rhDfve735myZWZmyosvvijjx483+6xdu9aMbtc50QcOHPiz44azbDjHAAAAEJ5tzOijWioAAACEnSlTppiR58cff7zpTJ8xY4YUFxebKVrURRddJC1btqydDubLL7+ULVu2SN++fc1P7RjXjvebbrqp9pi///*****************************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", "text/plain": ["<Figure size 1500x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Results exported to: output_runs/pointnet_plus_plus_inference/althea_rpcs/althea_rpcs_pile_detections_20250806_204024.csv\n", "Summary statistics saved to: output_runs/pointnet_plus_plus_inference/althea_rpcs/althea_rpcs_analysis_summary_20250806_204024.json\n", "\n", "Pipeline complete. Results saved to: output_runs/pointnet_plus_plus_inference/althea_rpcs\n"]}], "source": ["if not results_df.empty:\n", "    # Print summary\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    print(f\"\\nAnalysis Results:\")\n", "    print(f\"  Total analysis points: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {results_df['pile_probability'].mean():.4f}\")\n", "        \n", "    # Visualize and export\n", "    plot_path = visualize_results(results_df, OUTPUT_DIR, NEW_SITE_NAME)\n", "    \n", "    if plot_path:\n", "        mlflow.log_artifact(plot_path)\n", "\n", "    export_results(results_df, OUTPUT_DIR, NEW_SITE_NAME, \n", "                      MOD<PERSON>_PATH, PATCH_SIZE, CONFIDENCE_THRESHOLD)\n", "    \n", "    # Cleanup\n", "    mlflow.end_run()\n", "    print(f\"\\nPipeline complete. Results saved to: {OUTPUT_DIR}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 597.713794, "end_time": "2025-08-06T15:10:25.715461", "environment_variables": {}, "exception": null, "input_path": "04_pointnet_plus_plus_inference.ipynb", "output_path": "04_pointnet_plus_plus_inference_althea_rpcs_executed.ipynb", "parameters": {"NEW_SITE_NAME": "althea_rpcs", "OUTPUT_DIR": "output_runs/pointnet_plus_plus_inference/althea_rpcs", "POINT_CLOUD_PATH": "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las", "RUN_NAME": "inference_althea_rpcs"}, "start_time": "2025-08-06T15:00:28.001667", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}