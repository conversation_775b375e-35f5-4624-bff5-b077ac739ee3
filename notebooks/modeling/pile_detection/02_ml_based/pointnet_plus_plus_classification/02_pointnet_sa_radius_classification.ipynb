from google.colab import drive
drive.mount('/content/drive')

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torch.cuda.amp import GradScaler, autocast
import numpy as np
import pandas as pd
import pickle
import os
from pathlib import Path
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
import matplotlib.pyplot as plt
import json
import warnings
import time
warnings.filterwarnings('ignore')

GDRIVE_BASE = "/content/drive/MyDrive"
PROJECT_FOLDER = "pointnet_pile_detection"

project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER
data_path = project_path / "pointnet_data"  # Same data as Iteration 1
results_path = project_path / "results_iter2"
models_path = project_path / "models_iter2"

ifc_path = project_path / "GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
kml_path = project_path / "pile.kml"
test_pkl_path = data_path / "test_pointnet.pkl"
harmonized_pile_dataset = project_path/"harmonized_pile_dataset_final.csv"

# Create directories
for path in [results_path, models_path]:
    path.mkdir(parents=True, exist_ok=True)

# Device configuration
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Training configuration
# config = {
#     # Base parameters (optimized from Iteration 1)
#     'batch_size': 16 if device.type == 'cuda' else 8,
#     'num_epochs': 100,
#     'learning_rate': 0.001,
#     'num_points': 512,
#     'patience': 20,
#     'device': device,

#     # Parameters
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-4,
#     'dropout': 0.5,
#     'gradient_clip': 1.0,
#     'augmentation': True,
#     'use_amp': device.type == 'cuda'
# }

# config = {
#     # Base parameters (optimized from Iteration 1)
#     'batch_size': 8 if device.type == 'cuda' else 4,  # Smaller for stability
#     'num_epochs': 100,
#     'learning_rate': 0.0005,  # Keep same
#     'num_points': 1024,  # Keep same for now
#     'patience': 30,  # Increased patience
#     'device': device,

#     # Parameters
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-4,
#     'dropout': 0.3,  # Reduced dropout
#     'gradient_clip': 1.0,
#     'augmentation': True,  # Keep but reduce in preprocessing
#     'use_amp': device.type == 'cuda'
# }

# config = {
#     'batch_size': 4 if device.type == 'cuda' else 2,  # Reduce batch size
#     'num_epochs': 100,
#     'learning_rate': 0.0005,
#     'num_points': 2048,  # DOUBLE the points - this is the key!
#     'patience': 30,
#     'device': device,
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-4,
#     'dropout': 0.3,
#     'gradient_clip': 1.0,
#     'augmentation': True,
#     'use_amp': device.type == 'cuda'
# }

# config = {
#     'batch_size': 4 if device.type == 'cuda' else 2,  # Good for 2048 points
#     'num_epochs': 100,
#     'learning_rate': 0.0005,
#     'num_points': 2048,  # DOUBLE the points - this is the key!
#     'patience': 30,
#     'device': device,
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-4,
#     'dropout': 0.3,
#     'gradient_clip': 1.0,
#     'augmentation': False,  # DISABLE augmentation to start clean
#     'use_amp': device.type == 'cuda'
# }

# config = {
#     'batch_size': 4,
#     'num_epochs': 100,
#     'learning_rate': 0.01,  # MUCH higher LR for raw coordinates
#     'num_points': 2048,
#     'patience': 30,
#     'device': device,
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-5,  # Much less regularization
#     'dropout': 0.2,  # Less dropout
#     'gradient_clip': 1.0,
#     'augmentation': False,
#     'use_amp': device.type == 'cuda'
# }

# config = {
#     'batch_size': 2,  # Very small batches
#     'num_epochs': 100,
#     'learning_rate': 0.00001,  # Much lower LR for raw coordinates
#     'num_points': 512,  # Reduce points if needed
#     'patience': 30,
#     'device': device,
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-6,  # Very light regularization
#     'dropout': 0.2,
#     'gradient_clip': 0.1,  # Light clipping for large values
#     'augmentation': False,
#     'use_amp': False,  # Keep disabled
# }

# config = {
#     'batch_size': 16,  # Very small for raw coordinates
#     'num_epochs': 50,  # Fewer epochs for faster testing
#     'learning_rate': 0.0001,  # Lower LR for raw coordinates
#     'num_points': 128,  # Reduce points for speed
#     'patience': 10,
#     'device': device,
#     'lr_scheduler': 'cosine',
#     'weight_decay': 1e-4,
#     'dropout': 0.3,
#     'gradient_clip': 1.0,
#     'augmentation': False,
#     'use_amp': False,  # Disable for raw coordinates
# }

# config = {
#     'batch_size': 8,        # Smaller batches for stability
#     'num_epochs': 100,      # More epochs
#     'learning_rate': 0.0001, # MUCH lower learning rate
#     'num_points': 256,      # More points (but manageable)
#     'patience': 25,         # More patience
#     'device': device,
#     'lr_scheduler': 'step', # Simpler scheduler
#     'weight_decay': 1e-3,   # More regularization
#     'dropout': 0.5,         # More dropout
#     'gradient_clip': 0.5,   # Lighter clipping
#     'augmentation': False,  # Disable for now
#     'use_amp': False,
# }

config = {
    'batch_size': 8,
    'num_epochs': 100,
    'learning_rate': 0.0005,
    'num_points': 1024,
    'patience': 20,
    'device': device,
    'lr_scheduler': 'step',
    'weight_decay': 0.001,
    'dropout': 0.3,
    'gradient_clip': 0.5,
    'augmentation': False,
    'use_amp': False,
}

print("Configuration:")
for key, value in config.items():
    print(f"  {key}: {value}")

def load_pile_data(data_path):
    """Load pile detection data from pickle files - Preserves 20 features for PointNet"""
    datasets = {}
    file_mapping = {
        'train': 'train_pointnet.pkl',
        'val': 'val_pointnet.pkl',
        'test': 'test_pointnet.pkl'
    }

    for split, filename in file_mapping.items():
        filepath = data_path / filename

        if not filepath.exists():
            print(f"ERROR: {filepath} not found!")
            return None

        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        patches = data['points']
        labels = data['labels']

        # ✅ DO NOT TRUNCATE TO 3D
        # if patches.shape[2] > 3:
        #     patches = patches[:, :, :3]

        patches = patches.astype(np.float32)
        labels = np.array(labels)

        print(f"{split}: {patches.shape}, labels: {len(labels)}")

        datasets[split] = {
            'patches': patches,
            'labels': labels,
            'metadata': data.get('metadata', [])
        }

    return datasets

datasets = load_pile_data(data_path)
print(datasets['train']['patches'].shape)  # Should be (N, 1024, 20)


def augment_point_cloud(points, augment_prob=0.7):
    """NEW: Data augmentation for point clouds"""
    if np.random.random() > augment_prob:
        return points

    # Random rotation around Z-axis
    angle = np.random.uniform(0, 2 * np.pi)
    cos_angle, sin_angle = np.cos(angle), np.sin(angle)
    rotation_matrix = np.array([
        [cos_angle, -sin_angle, 0],
        [sin_angle, cos_angle, 0],
        [0, 0, 1]
    ])
    points = points @ rotation_matrix.T

    # Random scaling
    scale = np.random.uniform(0.9, 1.1)
    points = points * scale

    # Random jittering
    noise = np.random.normal(0, 0.01, points.shape)
    points = points + noise

    return points

def preprocess_patches_1(patches, labels, num_points):
    """Preprocessing with augmentation"""
    processed_patches = []
    processed_labels = []

    print(f"Preprocessing of {len(patches)} patches...")

    for i, (patch, label) in enumerate(zip(patches, labels)):
        # Remove zero-padded points
        valid_mask = np.abs(patch).sum(axis=1) > 1e-6
        valid_points = patch[valid_mask]

        if len(valid_points) < 10:
            continue

        # Sample to fixed number of points
        if len(valid_points) >= num_points:
            indices = np.random.choice(len(valid_points), num_points, replace=False)
            sampled = valid_points[indices]
        else:
            indices = np.random.choice(len(valid_points), num_points, replace=True)
            sampled = valid_points[indices]
            noise = np.random.normal(0, 0.01, sampled.shape)
            sampled = sampled + noise

        # Apply augmentation
        if config['augmentation']:
            sampled = augment_point_cloud(sampled, augment_prob=0.7)

        # Center and normalize
        centroid = np.mean(sampled, axis=0)
        sampled = sampled - centroid

        max_dist = np.max(np.linalg.norm(sampled, axis=1))
        if max_dist > 1e-6:
            sampled = sampled / max_dist

        processed_patches.append(sampled)
        processed_labels.append(label)

    print(f"Preprocessing complete: {len(processed_patches)} patches")
    return np.array(processed_patches), np.array(processed_labels)

def preprocess_patches_2(patches, labels, num_points):
    """Enhanced preprocessing inspired by 90%+ classical ML results"""
    processed_patches = []
    processed_labels = []

    print(f"Enhanced preprocessing of {len(patches)} patches...")

    for i, (patch, label) in enumerate(zip(patches, labels)):
        # Remove zero-padded points
        valid_mask = np.abs(patch).sum(axis=1) > 1e-6
        valid_points = patch[valid_mask]

        if len(valid_points) < 20:  # Higher threshold (was 10)
            continue

        # More aggressive outlier removal (classical ML technique)
        distances = np.linalg.norm(valid_points, axis=1)
        q90, q10 = np.percentile(distances, [90, 10])  # Remove extreme outliers
        outlier_mask = (distances >= q10) & (distances <= q90)
        clean_patch = valid_points[outlier_mask]

        if len(clean_patch) < 15:
            clean_patch = valid_points  # Fallback

        # Smart sampling with pile-aware weighting
        center_distances = np.linalg.norm(clean_patch[:, :2], axis=1)

        # For piles: bias toward center, for non-piles: uniform sampling
        if label == 1:  # Pile - bias toward center points
            weights = 1.0 / (center_distances + 0.1)
            weights = weights / weights.sum()
        else:  # Non-pile - uniform sampling
            weights = None

        # Sample to fixed number of points
        if len(clean_patch) >= num_points:
            if weights is not None:
                indices = np.random.choice(len(clean_patch), num_points,
                                         replace=False, p=weights)
            else:
                indices = np.random.choice(len(clean_patch), num_points, replace=False)
            sampled = clean_patch[indices]
        else:
            if weights is not None:
                indices = np.random.choice(len(clean_patch), num_points,
                                         replace=True, p=weights)
            else:
                indices = np.random.choice(len(clean_patch), num_points, replace=True)
            sampled = clean_patch[indices]
            noise = np.random.normal(0, 0.005, sampled.shape)  # Less noise
            sampled = sampled + noise

        # Apply minimal augmentation (much less aggressive)
        if config['augmentation'] and np.random.random() < 0.3:  # Reduced from 0.7 to 0.3
            sampled = augment_point_cloud(sampled, augment_prob=0.3)

        # Robust centering and scaling
        centroid = np.median(sampled, axis=0)  # More robust than mean
        sampled = sampled - centroid

        # Use 90th percentile for more stable scaling
        distances_norm = np.linalg.norm(sampled, axis=1)
        scale = np.percentile(distances_norm, 90)  # More robust than max
        if scale > 1e-6:
            sampled = sampled / scale

        processed_patches.append(sampled)
        processed_labels.append(label)

    print(f"Enhanced preprocessing complete: {len(processed_patches)} patches")
    return np.array(processed_patches), np.array(processed_labels)

def preprocess_patches_3(patches, labels, num_points):
    """Minimal preprocessing to preserve spatial relationships like Classical ML"""
    processed_patches = []
    processed_labels = []

    print(f"Minimal preprocessing of {len(patches)} patches...")

    for i, (patch, label) in enumerate(zip(patches, labels)):
        # Remove zero-padded points
        valid_mask = np.abs(patch).sum(axis=1) > 1e-6
        valid_points = patch[valid_mask]

        if len(valid_points) < 20:
            continue

        # REMOVE the aggressive outlier removal - it might be removing important structure
        # distances = np.linalg.norm(valid_points, axis=1)
        # q90, q10 = np.percentile(distances, [90, 10])
        # outlier_mask = (distances >= q10) & (distances <= q90)
        # clean_patch = valid_points[outlier_mask]

        # Use all valid points (like Classical ML used all features)
        clean_patch = valid_points

        # Remove the pile-aware weighting - it might be creating bias
        # center_distances = np.linalg.norm(clean_patch[:, :2], axis=1)
        # if label == 1:  # Pile - bias toward center points
        #     weights = 1.0 / (center_distances + 0.1)
        #     weights = weights / weights.sum()
        # else:  # Non-pile - uniform sampling
        #     weights = None

        # Simple uniform sampling (like Classical ML)
        if len(clean_patch) >= num_points:
            indices = np.random.choice(len(clean_patch), num_points, replace=False)
            sampled = clean_patch[indices]
        else:
            indices = np.random.choice(len(clean_patch), num_points, replace=True)
            sampled = clean_patch[indices]
            noise = np.random.normal(0, 0.002, sampled.shape)  # Minimal noise
            sampled = sampled + noise

        # NO augmentation to start clean
        # if config['augmentation'] and np.random.random() < 0.3:
        #     sampled = augment_point_cloud(sampled, augment_prob=0.3)

        # MINIMAL normalization - preserve relative scales
        centroid = np.mean(sampled, axis=0)  # Simple mean
        sampled = sampled - centroid

        # Light scaling to preserve spatial relationships
        scale = np.std(np.linalg.norm(sampled, axis=1))  # Use std, not percentile
        if scale > 1e-6:
            sampled = sampled / (scale * 2)  # Less aggressive scaling

        processed_patches.append(sampled)
        processed_labels.append(label)

    print(f"Minimal preprocessing complete: {len(processed_patches)} patches")
    return np.array(processed_patches), np.array(processed_labels)

def preprocess_patches_4(patches, labels, num_points):
    """Keep absolute coordinates - the key difference!"""
    processed_patches = []
    processed_labels = []

    print(f"Absolute coordinate preprocessing of {len(patches)} patches...")

    for patch, label in zip(patches, labels):
        # Only remove zero-padding
        valid_mask = np.abs(patch).sum(axis=1) > 1e-6
        valid_points = patch[valid_mask]

        if len(valid_points) < 20:
            continue

        # Sample points
        if len(valid_points) >= num_points:
            indices = np.random.choice(len(valid_points), num_points, replace=False)
            sampled = valid_points[indices]
        else:
            indices = np.random.choice(len(valid_points), num_points, replace=True)
            sampled = valid_points[indices]

        # NO CENTERING - keep absolute coordinates!
        # Light global scaling to prevent GPU issues
        scaled = sampled / 50.0  # Scale by a fixed factor

        processed_patches.append(scaled)
        processed_labels.append(label)

    print(f"Absolute coordinate preprocessing complete: {len(processed_patches)} patches")
    return np.array(processed_patches), np.array(processed_labels)


def preprocess_patches_5(patches, labels, num_points):
    """Light scaling - preserves height differences"""
    processed_patches = []
    processed_labels = []

    for patch, label in zip(patches, labels):
        valid_mask = np.abs(patch).sum(axis=1) > 1e-6
        valid_points = patch[valid_mask]

        if len(valid_points) < 20:
            continue

        # Sample points
        if len(valid_points) >= num_points:
            indices = np.random.choice(len(valid_points), num_points, replace=False)
            sampled = valid_points[indices]
        else:
            indices = np.random.choice(len(valid_points), num_points, replace=True)
            sampled = valid_points[indices]

        # ONLY light global scaling - NO CENTERING!
        scaled = sampled / 30.0  # Keeps piles ~2.0 height, non-piles ~1.6

        processed_patches.append(scaled)
        processed_labels.append(label)

    return np.array(processed_patches), np.array(processed_labels)


def preprocess_patches_6(patches, labels, num_points):
    """Preserve height differences - the key discriminative feature"""
    processed_patches = []
    processed_labels = []

    print(f"Height-preserving preprocessing of {len(patches)} patches...")

    for patch, label in zip(patches, labels):
        # Remove zero-padded points
        valid_mask = np.abs(patch).sum(axis=1) > 1e-6
        valid_points = patch[valid_mask]

        if len(valid_points) < 20:
            continue

        # Sample points
        if len(valid_points) >= num_points:
            indices = np.random.choice(len(valid_points), num_points, replace=False)
            sampled = valid_points[indices]
        else:
            indices = np.random.choice(len(valid_points), num_points, replace=True)
            sampled = valid_points[indices]
            # Minimal noise for upsampled points
            noise = np.random.normal(0, 0.01, sampled.shape)
            sampled = sampled + noise

        # KEY CHANGE: Only center X,Y coordinates, preserve Z (height)
        xy_centroid = np.mean(sampled[:, :2], axis=0)
        sampled[:, :2] = sampled[:, :2] - xy_centroid

        # Light global scaling (preserves height differences)
        sampled = sampled / 20.0  # Much lighter than current /30.0

        processed_patches.append(sampled)
        processed_labels.append(label)

    print(f"Height-preserving preprocessing complete: {len(processed_patches)} patches")
    return np.array(processed_patches), np.array(processed_labels)

def preprocess_patches_7(patches, labels, num_points):

    processed_patches = []
    processed_labels = []

    for patch, label in zip(patches, labels):
        patch = np.array(patch, dtype=np.float32)

        # Simple sampling
        if len(patch) >= num_points:
            sampled = patch[np.random.choice(len(patch), num_points, replace=False)]
        else:
            # Upsample with light noise
            extra = np.stack([
                patch[np.random.randint(len(patch))] + np.random.normal(0, 0.01, 3)
                for _ in range(num_points - len(patch))
            ])
            sampled = np.vstack([patch, extra])

        # ✅ ONLY normalize by max distance - preserves relationships
        sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1

        processed_patches.append(sampled)
        processed_labels.append(label)

    return np.array(processed_patches), np.array(processed_labels)


import numpy as np

def preprocess_patches(patches, labels, num_points=1024, num_features=20):
    """
    Preprocess point cloud patches for PointNet training.

    Parameters:
    - patches: List or array of shape (N, variable_points, num_features)
    - labels: Corresponding labels for each patch
    - num_points: Target number of points per patch
    - num_features: Expected number of features per point (default: 20)

    Returns:
    - processed_patches: Array of shape (N, num_points, num_features)
    - processed_labels: Array of shape (N,)
    """
    processed_patches = []
    processed_labels = []

    for patch, label in zip(patches, labels):
        patch = np.array(patch, dtype=np.float32)

        if patch.shape[1] != num_features:
            raise ValueError(f"Expected {num_features} features, got {patch.shape[1]}")

        if len(patch) >= num_points:
            distances = patch[:, 4]  # distance_norm
            probabilities = 1 / (distances + 0.1)
            probabilities /= probabilities.sum()

            sampled_indices = np.random.choice(len(patch), num_points, replace=False, p=probabilities)
            sampled = patch[sampled_indices]
        else:
            upsampled = patch.copy()
            needed = num_points - len(patch)

            for _ in range(needed):
                distances = patch[:, 4]
                weights = 1 / (distances + 0.1)
                weights /= weights.sum()
                source_idx = np.random.choice(len(patch), p=weights)

                new_point = patch[source_idx].copy()
                new_point[:3] += np.random.normal(0, 0.02, 3)
                upsampled = np.vstack([upsampled, new_point])

            sampled = upsampled[:num_points]

        spatial_coords = sampled[:, :3]
        spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))
        if spatial_extent > 0:
            sampled[:, :3] /= spatial_extent

        processed_patches.append(sampled)
        processed_labels.append(label)

    return np.array(processed_patches), np.array(processed_labels)


def preprocess_patches_simple_fix(patches, labels, num_points=1024):
    """
    Simpler fallback for preprocessing without full sampling logic.
    """
    processed_patches = []
    processed_labels = []

    for patch, label in zip(patches, labels):
        patch = np.array(patch, dtype=np.float32)
        n_points, n_features = patch.shape

        if n_points >= num_points:
            indices = np.random.choice(n_points, num_points, replace=False)
            sampled = patch[indices]
        else:
            extra_indices = np.random.choice(n_points, num_points - n_points, replace=True)
            extra = patch[extra_indices].copy()
            if n_features >= 3:
                extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))
            sampled = np.vstack([patch, extra])

        if sampled.shape[1] >= 3:
            spatial_coords = sampled[:, :3]
            max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))
            if max_dist > 0:
                sampled[:, :3] /= max_dist

        processed_patches.append(sampled)
        processed_labels.append(label)

    return np.array(processed_patches), np.array(processed_labels)


def verify_preprocessing_consistency(patches_sample):
    """
    Verifies the consistency of processed patches.
    """
    patches_array = np.array(patches_sample)

    print("Preprocessing Verification:")
    print(f"   Shape: {patches_array.shape}")
    print("   Expected: (N, 1024, 20)")

    if len(patches_array.shape) == 3 and patches_array.shape[2] == 20:
        print("   Status: PASS - Matches expected input format.")
        sample_patch = patches_array[0]
        print("   Feature Ranges:")
        print(f"     Spatial (0–2): {sample_patch[:, :3].min():.3f} to {sample_patch[:, :3].max():.3f}")
        print(f"     Height Norm (3): {sample_patch[:, 3].min():.3f} to {sample_patch[:, 3].max():.3f}")
        print(f"     Distance Norm (4): {sample_patch[:, 4].min():.3f} to {sample_patch[:, 4].max():.3f}")
        print(f"     Density (5): {sample_patch[:, 5].min():.3f} to {sample_patch[:, 5].max():.3f}")
        return True
    else:
        print("   Status: FAIL - Feature count mismatch.")
        return False


def square_distance(src, dst):
    """Calculate squared distance between points"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def farthest_point_sample(xyz, npoint):
    """Farthest point sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)

    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]

    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """NEW: Ball query for radius-based local neighborhoods"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

class PointNetSetAbstraction(nn.Module):
    """NEW: Standard PointNet++ Set Abstraction Layer"""
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all

        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel

    def forward(self, xyz, points):
        B, N, C = xyz.shape

        if self.group_all:
            new_xyz = torch.zeros(B, 1, C).to(xyz.device)
            new_points = points.view(B, 1, N, -1)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)

            if points is not None:
                grouped_points = points[torch.arange(B)[:, None, None], idx]
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm

        new_points = new_points.permute(0, 3, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = torch.relu(bn(conv(new_points)))

        new_points = torch.max(new_points, 2)[0]
        new_points = new_points.permute(0, 2, 1)

        return new_xyz, new_points


class PointNetPlusPlus(nn.Module):
    # Modidied original Pointnet plus which is designed for only 3
    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):
        super(PointNetPlusPlus, self).__init__()

        # Set Abstraction Layers
        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)
        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)
        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)

        # Classification head
        self.fc1 = nn.Linear(1024, 512)
        self.bn1 = nn.BatchNorm1d(512)
        self.drop1 = nn.Dropout(dropout)

        self.fc2 = nn.Linear(512, 256)
        self.bn2 = nn.BatchNorm1d(256)
        self.drop2 = nn.Dropout(dropout)

        self.fc3 = nn.Linear(256, 64)
        self.bn3 = nn.BatchNorm1d(64)
        self.drop3 = nn.Dropout(dropout * 0.6)

        self.fc4 = nn.Linear(64, num_classes)

    def forward(self, xyz):
        # Input shape: (B, N, C), C = in_channels
        if len(xyz.shape) == 4:
            xyz = xyz.squeeze(1)  # Remove extra dim if present

        B, N, C = xyz.shape

        # Split input into xyz coords and features
        coords = xyz[:, :, :3]       # (B, N, 3)
        features = xyz[:, :, 3:]     # (B, N, C-3) or None if C == 3

        # Pass through SA layers
        l1_xyz, l1_points = self.sa1(coords, features)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)

        global_feat = l3_points.view(B, -1)

        # Classification head
        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))
        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))
        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))
        x = self.fc4(x)

        return x


class PileDataset(Dataset):
    def __init__(self, points, labels):
        self.points = torch.FloatTensor(points)
        self.labels = torch.LongTensor(labels)

    def __len__(self):
        return len(self.points)

    def __getitem__(self, idx):
        return self.points[idx], self.labels[idx]

def setup_training(model, train_labels):
    """Balanced training setup like Classical ML"""

    # Check data distribution
    pile_count = np.sum(train_labels)
    total_count = len(train_labels)
    pile_ratio = pile_count / total_count
    print(f"Data distribution: {total_count-pile_count} non-piles, {pile_count} piles")
    print(f"Pile ratio: {pile_ratio:.3f}")

    # Light class weighting (not aggressive)
    if pile_ratio > 0.6:  # You have 66.9% piles
        # Weight the minority class slightly more
        pos_weight = 1.2  # Light weighting
        neg_weight = 0.8
        class_weights = torch.FloatTensor([neg_weight, pos_weight]).to(device)
        criterion = nn.CrossEntropyLoss(weight=class_weights)
    else:
        criterion = nn.CrossEntropyLoss()

    print(f"Using class weights: {class_weights if 'class_weights' in locals() else None}")

    # More conservative optimizer
    optimizer = optim.Adam(  # Switch to Adam
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )

    # Step scheduler instead of cosine
    scheduler = optim.lr_scheduler.StepLR(
        optimizer, step_size=20, gamma=0.5
    )

    return criterion, optimizer, scheduler

def train_epoch(model, loader, criterion, optimizer, device, scaler=None):
    """Training with gradient clipping"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    for batch_idx, (data, target) in enumerate(loader):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()

        if scaler is not None:
            with autocast():
                output = model(data)
                loss = criterion(output, target)
            scaler.scale(loss).backward()
            # NEW: Gradient clipping
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])
            scaler.step(optimizer)
            scaler.update()
        else:
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            # NEW: Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])
            optimizer.step()

        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)

    return total_loss / len(loader), correct / total


def validate_epoch(model, loader, criterion, device):
    model.eval()
    total_loss = 0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in loader:
            data, target = data.to(device), target.to(device)

            if config['use_amp']:
                with autocast():
                    output = model(data)
                    loss = criterion(output, target)
            else:
                output = model(data)
                loss = criterion(output, target)

            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)

    return total_loss / len(loader), correct / total

print("POINTNET++ PILE DETECTION - ITERATION 2")
print("="*50)
print("- Proper Set Abstraction layers with ball query")
print("- Data augmentation (rotation, scaling, jittering)")
print("- Learning rate scheduling (Cosine Annealing)")
print("- Gradient clipping for stability")
print("- Optimizer (AdamW) with weight decay")
print("- Deeper classification head (4 layers)")
print("="*50)

def run_training(model, train_loader, val_loader, criterion, optimizer, scheduler, config, scaler, models_path):
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    learning_rates = []

    best_val_loss = float('inf')  # Track validation LOSS, not just accuracy
    best_val_acc = 0
    patience_counter = 0
    start_time = time.time()

    for epoch in range(config['num_epochs']):
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device, scaler)
        val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)

        current_lr = optimizer.param_groups[0]['lr']
        learning_rates.append(current_lr)

        if scheduler:
            scheduler.step()

        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_accs.append(train_acc)
        val_accs.append(val_acc)

        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{config['num_epochs']}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}, Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}, LR={current_lr:.6f}")

        # IMPROVED EARLY STOPPING: Use validation loss + accuracy
        improved = False
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            improved = True
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            improved = True

        if improved:
            patience_counter = 0
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
                'epoch': epoch,
                'val_acc': val_acc,
                'val_loss': val_loss,
                'config': config
            }, models_path / 'best_pointnet_iter4.pth')
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                print(f"Early stopping at epoch {epoch+1} (no improvement for {config['patience']} epochs)")
                break

    training_time = time.time() - start_time
    print(f"Training completed in {training_time:.1f}s")
    print(f"Best validation accuracy: {best_val_acc:.4f}")
    print(f"Best validation loss: {best_val_loss:.4f}")

    history = {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'train_accs': train_accs,
        'val_accs': val_accs,
        'learning_rates': learning_rates
    }

    return history, best_val_acc, training_time


# Load data
datasets = load_pile_data(data_path)
if not datasets:
    print("ERROR: No data found!")

# Preprocess data
train_patches, train_labels = preprocess_patches(
    datasets['train']['patches'], datasets['train']['labels'], config['num_points'],num_features=20
)
val_patches, val_labels = preprocess_patches(
    datasets['val']['patches'], datasets['val']['labels'], config['num_points'],num_features=20
)
test_patches, test_labels = preprocess_patches(
    datasets['test']['patches'], datasets['test']['labels'], config['num_points'],num_features=20
)

# === DATA INTEGRITY VERIFICATION ===
print("\n=== VERIFYING DATA INTEGRITY ===")
original_max = datasets['train']['patches'].max()
original_min = datasets['train']['patches'].min()
processed_max = train_patches.max()
processed_min = train_patches.min()

print(f"Original data range: [{original_min:.2f}, {original_max:.2f}]")
print(f"Processed data range: [{processed_min:.2f}, {processed_max:.2f}]")

if original_max > 50:  # Should be ~60 if uncorrupted
    print("Original data intact - no corruption detected")
else:
    print("WARNING: Original data may be corrupted!")

if processed_max < 2:  # Good preprocessing should be 0-1 range
    print("Processed data in good range")
else:
    print("Processed data range looks unusual")


# Create datasets and loaders
train_dataset = PileDataset(train_patches, train_labels)
val_dataset = PileDataset(val_patches, val_labels)
test_dataset = PileDataset(test_patches, test_labels)

train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, drop_last=True)
val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False)

# === PREPROCESSING FUNCTION DEBUG ===
print("\n=== PREPROCESSING FUNCTION DEBUG ===")

# Test the function directly on one sample
test_patch = datasets['train']['patches'][0].copy()  # Get original patch (SAFE COPY)
test_label = datasets['train']['labels'][0]

print(f"Original patch: range=[{test_patch.min():.2f}, {test_patch.max():.2f}], std={np.std(test_patch):.3f}")

# Apply your preprocessing function to this single patch
valid_mask = np.abs(test_patch).sum(axis=1) > 1e-6
valid_points = test_patch[valid_mask]
print(f"Valid points: {len(valid_points)}, range=[{valid_points.min():.2f}, {valid_points.max():.2f}], std={np.std(valid_points):.3f}")

# Sample points
indices = np.random.choice(len(valid_points), min(config['num_points'], len(valid_points)), replace=False)
sampled = valid_points[indices]
print(f"After sampling: range=[{sampled.min():.2f}, {sampled.max():.2f}], std={np.std(sampled):.3f}")

# Apply the SAME normalization as actual preprocessing
normalized = sampled / np.max(np.linalg.norm(sampled, axis=1))
print(f"After normalization (actual preprocessing): range=[{normalized.min():.2f}, {normalized.max():.2f}], std={np.std(normalized):.3f}")


# === SAMPLE DATA ANALYSIS ===
print("\n=== SAMPLE DATA ANALYSIS ===")

# Check actual data differences
pile_samples = train_patches[train_labels == 1][:5]
non_pile_samples = train_patches[train_labels == 0][:5]

print("After preprocessing:")
print(f"Pile samples height range: {pile_samples[:,:,2].min():.3f} to {pile_samples[:,:,2].max():.3f}")
print(f"Non-pile samples height range: {non_pile_samples[:,:,2].min():.3f} to {non_pile_samples[:,:,2].max():.3f}")
print(f"Height difference preserved: {abs(pile_samples[:,:,2].mean() - non_pile_samples[:,:,2].mean()):.3f}")

print(f"Pile samples stats:")
for i, sample in enumerate(pile_samples):
    print(f"  Sample {i}: mean={np.mean(sample):.6f}, std={np.std(sample):.6f}")

print(f"Non-pile samples stats:")
for i, sample in enumerate(non_pile_samples):
    print(f"  Sample {i}: mean={np.mean(sample):.6f}, std={np.std(sample):.6f}")

# Check if samples look identical
print(f"\nData variance analysis:")
print(f"Pile samples variance: {np.var(pile_samples):.6f}")
print(f"Non-pile samples variance: {np.var(non_pile_samples):.6f}")

# Check for identical preprocessing
print(f"Pile sample range: [{pile_samples.min():.6f}, {pile_samples.max():.6f}]")
print(f"Non-pile sample range: [{non_pile_samples.min():.6f}, {non_pile_samples.max():.6f}]")

# === ORIGINAL DATA ANALYSIS (BEFORE PREPROCESSING) ===
print("\n=== ORIGINAL DATA ANALYSIS (BEFORE PREPROCESSING) ===")

# Get original patches directly from datasets
train_original = datasets['train']['patches'].copy()
train_labels_orig = datasets['train']['labels'].copy()

# Check original pile vs non-pile samples
pile_indices = np.where(np.array(train_labels_orig) == 1)[0][:5]
non_pile_indices = np.where(np.array(train_labels_orig) == 0)[0][:5]

print("Original PILE samples (before preprocessing):")
for i, idx in enumerate(pile_indices):
    sample = train_original[idx]
    valid_mask = np.abs(sample).sum(axis=1) > 1e-6
    valid_points = sample[valid_mask]
    print(f"  Pile {i}: {len(valid_points)} points, range=[{valid_points.min():.2f}, {valid_points.max():.2f}], std={np.std(valid_points):.3f}")

print("Original NON-PILE samples (before preprocessing):")
for i, idx in enumerate(non_pile_indices):
    sample = train_original[idx]
    valid_mask = np.abs(sample).sum(axis=1) > 1e-6
    valid_points = sample[valid_mask]
    print(f"  Non-pile {i}: {len(valid_points)} points, range=[{valid_points.min():.2f}, {valid_points.max():.2f}], std={np.std(valid_points):.3f}")

# Initialize model with more regularization
# model = PointNetPlusPlus(num_classes=2).to(device)
model = PointNetPlusPlus(num_classes=2, in_channels=20).to(device)

# New stable training setup
criterion, optimizer, scheduler = setup_training(model, train_labels)
scaler = None  # Disable AMP for stability

# Model info
param_count = sum(p.numel() for p in model.parameters())
print(f"Model parameters: {param_count:,}")

# Inspect feature-wise stats across all patches
all_data = datasets['train']['patches'].reshape(-1, 20)
for i in range(20):
    f_min, f_max = np.min(all_data[:, i]), np.max(all_data[:, i])
    print(f"Feature {i}: range [{f_min:.3f}, {f_max:.3f}]")


history, best_val_acc, training_time = run_training(
    model, train_loader, val_loader, criterion, optimizer, scheduler,
    config, scaler, models_path
)

def evaluate_model(model, test_loader, device):
    model.eval()
    all_preds = []
    all_targets = []

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            all_preds.extend(output.argmax(dim=1).cpu().numpy())
            all_targets.extend(target.cpu().numpy())

    return all_preds, all_targets

all_preds, all_targets = evaluate_model(model, test_loader, device)

def compute_metrics(all_preds, all_targets):
    test_accuracy = accuracy_score(all_targets, all_preds)
    print(f"True Test Accuracy: {test_accuracy:.3f}")

    test_f1 = f1_score(all_targets, all_preds)
    test_precision = precision_score(all_targets, all_preds)
    test_recall = recall_score(all_targets, all_preds)
    cm = confusion_matrix(all_targets, all_preds)

    print("\nTEST METRICS:")
    print(f"Accuracy: {test_accuracy:.4f}")
    print(f"F1-Score: {test_f1:.4f}")
    print(f"Precision: {test_precision:.4f}")
    print(f"Recall: {test_recall:.4f}")
    print("Confusion Matrix:\n", cm)

    return {
        'accuracy': test_accuracy,
        'f1_score': test_f1,
        'precision': test_precision,
        'recall': test_recall,
        'confusion_matrix': cm.tolist()
    }

# Plot training and validation loss
plt.figure(figsize=(10, 4))
plt.subplot(1, 2, 1)

plt.plot(history['train_losses'], label='Train Loss')
plt.plot(history['val_losses'], label='Val Loss')
plt.title("Loss")
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.plot(history['train_accs'], label='Train Acc')
plt.plot(history['val_accs'], label='Val Acc')
plt.title("Accuracy")
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# Confusion matrix
from sklearn.metrics import confusion_matrix
import seaborn as sns

metrics_dict = compute_metrics(all_preds, all_targets)

cm = confusion_matrix(all_targets, all_preds)
print("Confusion Matrix:")
print(cm)

plt.figure(figsize=(6, 5))
sns.heatmap(cm, annot=True, fmt="d", cmap="Blues", xticklabels=["Non-Pile", "Pile"], yticklabels=["Non-Pile", "Pile"])
plt.xlabel("Predicted")
plt.ylabel("Actual")
plt.title("Confusion Matrix")
plt.tight_layout()
plt.show()


import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import pickle
from sklearn.metrics import accuracy_score

def create_pointnet_spatial_plot_correct_way(all_preds, all_targets, test_pkl_path, results_path, metrics):
    """Create PointNet++ spatial plot using the same method as classical ML models."""

    # Load reference data
    ifc_df = pd.read_csv(ifc_path)
    ifc_coords = ifc_df[['X', 'Y']].values

    gdf_kml = gpd.read_file(kml_path, driver='KML')
    gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)
    gdf_kml['geometry'] = gdf_kml.geometry.centroid
    kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)

    # Load the original pile dataset
    pile_df = pd.read_csv(harmonized_pile_dataset)

    # Load test metadata
    with open(test_pkl_path, 'rb') as f:
        test_data = pickle.load(f)
    test_metadata = test_data.get('metadata', [])

    # Reconstruct coordinates using pile IDs
    pred_coords = []
    valid_indices = []

    for i, meta in enumerate(test_metadata[:len(all_preds)]):
        pile_id = meta.get('pile_id') if isinstance(meta, dict) else None

        if pile_id and pile_id in pile_df['pile_id'].values:
            pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]
            pred_coords.append([pile_row['x'], pile_row['y']])
            valid_indices.append(i)

    if len(pred_coords) == 0:
        print("ERROR: No pile IDs found in metadata!")
        return

    pred_coords = np.array(pred_coords)

    # Filter predictions to match valid coordinates
    all_preds_filtered = [all_preds[i] for i in valid_indices]
    all_targets_filtered = [all_targets[i] for i in valid_indices]

    # Calculate accuracy for SPATIAL subset (for display purposes)
    correct_mask = (np.array(all_targets_filtered) == np.array(all_preds_filtered))
    error_mask = ~correct_mask
    spatial_accuracy = np.mean(correct_mask)

    # Use the TRUE accuracy from full test set for title
    true_accuracy = metrics['accuracy']

    print(f"Reconstructed {len(pred_coords)} coordinates from pile IDs")
    print(f"Spatial subset accuracy: {spatial_accuracy:.3f} ({len(pred_coords)} samples)")
    print(f"Full test set accuracy: {true_accuracy:.3f} ({len(all_preds)} samples)")
    print(f"Coordinate range: X[{pred_coords[:, 0].min():.0f}, {pred_coords[:, 0].max():.0f}], Y[{pred_coords[:, 1].min():.0f}, {pred_coords[:, 1].max():.0f}]")

    # Create plot exactly like classical ML models
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))

    # Plot reference data
    ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1],
              s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')
    ax.scatter(kml_coords[:, 0], kml_coords[:, 1],
              s=8, alpha=0.7, color='green', marker='s', label='KML Ground Truth')

    # Plot predictions
    if np.sum(correct_mask) > 0:
        ax.scatter(pred_coords[correct_mask, 0], pred_coords[correct_mask, 1],
                  c='blue', s=30, alpha=0.8, marker='o',
                  label=f'Correct ({np.sum(correct_mask)})')

    if np.sum(error_mask) > 0:
        ax.scatter(pred_coords[error_mask, 0], pred_coords[error_mask, 1],
                  c='red', s=50, alpha=0.9, marker='x', linewidth=2,
                  label=f'Errors ({np.sum(error_mask)})')

    # Use TRUE accuracy in title, but show spatial stats in legend
    ax.set_title(f'PointNet++\nAccuracy: {true_accuracy:.3f}')
    ax.set_xlabel('X (UTM)')
    ax.set_ylabel('Y (UTM)')
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal', adjustable='box')

    # Add text box with full stats
    stats_text = f'Full Test Set:\n{len(all_preds)} samples\n{true_accuracy:.1%} accuracy\n\nSpatial Subset:\n{len(pred_coords)} samples\n{spatial_accuracy:.1%} accuracy'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(results_path / 'pointnet_spatial_final_corrected.png', dpi=150, bbox_inches='tight')
    plt.show()

    return {
        'spatial_accuracy': spatial_accuracy,
        'spatial_samples': len(pred_coords),
        'true_accuracy': true_accuracy,
        'total_samples': len(all_preds)
    }

spatial_results = create_pointnet_spatial_plot_correct_way(
    all_preds, all_targets, test_pkl_path, results_path, metrics_dict
)

def save_results(results_path, metrics, history, config, best_val_acc, training_time, preds, targets):
    results = {
        'iteration': 1,
        'model': 'Basic PointNet++',
        'architecture': 'Simplified layers with k-NN grouping',
        'training_time': training_time,
        'best_val_acc': best_val_acc,
        'test_metrics': metrics,
        'config': config,
        'training_history': history,
        'predictions': preds,
        'ground_truth': targets
    }

    with open(results_path / 'pointnet_sa_radius_classification.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"Results saved to: {results_path / 'pointnet_sa_radius_classification.json'}")

save_results(results_path, metrics_dict, history, config, best_val_acc, training_time, all_preds, all_targets)


results_path_1 = Path("/content/drive/MyDrive/pointnet_pile_detection/results_iter1")
results_path_2 = Path("/content/drive/MyDrive/pointnet_pile_detection/results_iter2")

def load_iteration_results():
    iter1_path = results_path_1 / "pointnet_knn_classification.json"
    iter2_path = results_path_2 / "pointnet_sa_radius_classification.json"

    if iter1_path.exists() and iter2_path.exists():
        with open(iter1_path, 'r') as f:
            iter1 = json.load(f)
            if isinstance(iter1, str):
                iter1 = json.loads(iter1)

        with open(iter2_path, 'r') as f:
            iter2 = json.load(f)
            if isinstance(iter2, str):
                iter2 = json.loads(iter2)

        return iter1, iter2
    else:
        print("Iteration result files not found.")
        return None, None

def plot_comparison(iter1, iter2):
    import matplotlib.pyplot as plt
    import numpy as np

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

    # Training Accuracy
    ax1.plot(iter1['training_history']['train_accs'], label='Iteration 1 (k-NN)', alpha=0.7)
    ax1.plot(iter2['training_history']['train_accs'], label='Iteration 2 (Set Abstraction)', alpha=0.7)
    ax1.set_title('Training Accuracy')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Validation Accuracy
    ax2.plot(iter1['training_history']['val_accs'], label='Iteration 1 (k-NN)', alpha=0.7)
    ax2.plot(iter2['training_history']['val_accs'], label='Iteration 2 (Set Abstraction)', alpha=0.7)
    ax2.set_title('Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Learning Rate Schedule (Iteration 2 only)
    if 'learning_rates' in iter2['training_history']:
        ax3.plot(iter2['training_history']['learning_rates'])
        ax3.set_title('Learning Rate Schedule (Iteration 2)')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.grid(True, alpha=0.3)
    else:
        ax3.axis("off")

    # Test Metrics Comparison
    metrics = ['accuracy', 'f1_score', 'precision', 'recall']
    iter1_values = [iter1['test_metrics'][m] for m in metrics]
    iter2_values = [iter2['test_metrics'][m] for m in metrics]

    x = np.arange(len(metrics))
    width = 0.35

    ax4.bar(x - width/2, iter1_values, width, label='Iteration 1', alpha=0.7)
    ax4.bar(x + width/2, iter2_values, width, label='Iteration 2', alpha=0.7)
    ax4.set_title('Test Metrics Comparison')
    ax4.set_ylabel('Score')
    ax4.set_xticks(x)
    ax4.set_xticklabels([m.replace('_', ' ').title() for m in metrics])
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # Save and Show
    plt.tight_layout()
    plt.savefig(results_path / 'comparison_plot.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"Comparison plot saved to: {results_path / 'comparison_plot.png'}")

iter1, iter2 = load_iteration_results()

if iter1 and iter2:
    plot_comparison(iter1, iter2)