import os
import sys
import papermill as pm
from pathlib import Path
from datetime import datetime
import pandas as pd

print("Imports completed successfully")

# Site configurations for inference
SITE_CONFIGS = [
    {
        "site_name": "althea_rpcs",
        "point_cloud_path": "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las",
        "description": "Althea RPCS site with Point_Cloud.las"
    },
    {
        "site_name": "nortan_res",
        "point_cloud_path": "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las",
        "description": "Nortan RES site with Block_11_2m.las"
    }
]

# Base parameters for all runs
BASE_PARAMETERS = {
    "MODEL_PATH": "best_pointnet_plus_plus.pth",
    "DWG_PATH": "",
    "CONFIDENCE_THRESHOLD": 0.95,
    "BATCH_SIZE": 16,
    "GRID_SPACING": 5.0,
    "PATCH_SIZE": 3.0,
    "NUM_POINTS": 128,
    "EXPERIMENT_NAME": "pointnet_plus_plus_inference"
}

print(f"Configured {len(SITE_CONFIGS)} sites for inference:")
for i, config in enumerate(SITE_CONFIGS, 1):
    print(f"  {i}. {config['site_name']}: {config['description']}")

def validate_paths():
    """Validate that required files exist before execution"""
    issues = []
    
    # Check if base notebook exists
    base_notebook = "04_pointnet_plus_plus_inference.ipynb"
    if not Path(base_notebook).exists():
        issues.append(f"Base notebook not found: {base_notebook}")
    
    # Check if model exists
    model_path = BASE_PARAMETERS["MODEL_PATH"]
    if not Path(model_path).exists():
        issues.append(f"Model file not found: {model_path}")
    
    # Check point cloud files
    for config in SITE_CONFIGS:
        pc_path = config["point_cloud_path"]
        if not Path(pc_path).exists():
            issues.append(f"Point cloud not found for {config['site_name']}: {pc_path}")
    
    return issues

# Validate paths before starting
print("Validating file paths...")
validation_issues = validate_paths()

if validation_issues:
    print("Validation failed:")
    for issue in validation_issues:
        print(f"  - {issue}")
    print("\nPlease fix these issues before proceeding.")
else:
    print("All paths validated successfully")
    
# Create output directory if it doesn't exist
os.makedirs("output_runs/pointnet_plus_plus_inference", exist_ok=True)
print("Output directory ready")

import os

def run_site_inference(site_config):
    """Run inference notebook for a given site using papermill shell command."""
    site = site_config["site_name"]
    input_nb = "04_pointnet_plus_plus_inference.ipynb"
    output_nb = f"04_pointnet_plus_plus_inference_{site}_executed.ipynb"
    output_dir = f"output_runs/pointnet_plus_plus_inference/{site}"
    
    print(f"\n--- Running inference: {site} ---")
    
    cmd = (
        f'papermill {input_nb} {output_nb} '
        f'-p NEW_SITE_NAME "{site}" '
        f'-p POINT_CLOUD_PATH "{site_config["point_cloud_path"]}" '
        f'-p OUTPUT_DIR "{output_dir}" '
        f'-p RUN_NAME "inference_{site}" '
        '--log-output --kernel pytorch-geo-dev'
    )

    try:
        if os.system(cmd) == 0:
            print(f"✓ Completed: {site}")
            return True, None
        else:
            msg = f"Papermill failed for {site}"
            print(msg)
            return False, msg
    except Exception as e:
        print(f"Exception: {e}")
        return False, str(e)

print("Inference runner ready.")

if validation_issues:
    print("Skipping execution due to validation errors.")
    results = []
else:
    print("Starting multi-site inference...\n" + "=" * 50)
    start_time = datetime.now()
    
    results = [
        {
            "site": cfg["site_name"],
            "description": cfg["description"],
            "success": (res := run_site_inference(cfg))[0],
            "error": res[1],
            "output_notebook": f"04_pointnet_plus_plus_inference_{cfg['site_name']}_executed.ipynb"
        }
        for i, cfg in enumerate(SITE_CONFIGS, 1)
    ]
    
    print(f"\nTotal execution time: {datetime.now() - start_time}")


if results:
    print("\n" + "="*60)
    print("EXECUTION SUMMARY")
    print("="*60)

    success_count = sum(r["success"] for r in results)
    fail_count = len(results) - success_count

    print(f"Total sites: {len(results)} | Success: {success_count} | Failed: {fail_count}\n")

    for r in results:
        status = "SUCCESS" if r["success"] else f"FAILED\n    Error: {r['error']}"
        print(f"{r['site']}: {status}")

    print("\nGenerated Notebooks:")
    print("="*60)
    for r in results:
        nb_path = Path(r["output_notebook"])
        size = f"{nb_path.stat().st_size / (1024 * 1024):.1f} MB" if nb_path.exists() else "not found"
        print(f"  {nb_path.name} ({size})")

    # Display and save results
    df = pd.DataFrame(results)
    df["status"] = df["success"].map({True: "SUCCESS", False: "FAILED"})
    display(df[["site", "description", "status", "output_notebook"]])
    
    csv_path = "multi_site_inference_results.csv"
    df.to_csv(csv_path, index=False)
    print(f"\nCompleted! Results saved to: {csv_path}")
else:
    print("No results to display due to validation or execution failure.")
