# Parameters cell for Papermill execution
NEW_SITE_NAME = "althea_rpcs"
POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
OUTPUT_DIR = f"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}"

MODEL_PATH = "best_pointnet_iter4.pth"  # Path to trained model

DWG_PATH = ""  # Path to DWG file (optional)
OUTPUT_DIR = "output_runs/pointnet_plus_plus_inference"  # Output directory

# Model parameters (must match training)
CONFIDENCE_THRESHOLD = 0.5  # Confidence threshold for pile detection

GRID_SPACING = 25.0 # Grid spacing for analysis points
PATCH_SIZE = 20.0  # meters radius for patch extraction
NUM_POINTS = 1024 # KEEP 1024 to match training - CRITICAL for accuracy
BATCH_SIZE = 8  # Batch size for inference

# Enhanced analysis parameters
EXTENDED_ANALYSIS = True
NEGATIVE_SAMPLE_DISTANCE = 8.0  # Distance for synthetic negatives
FULL_SITE_ANALYSIS = True  # Run on all points, not subset

# MLflow configuration
EXPERIMENT_NAME = "pointnet_plus_plus_inference"
RUN_NAME = f"inference_{NEW_SITE_NAME}"

# Parameters
NEW_SITE_NAME = "nortan_res"
POINT_CLOUD_PATH = "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
OUTPUT_DIR = "output_runs/pointnet_plus_plus_inference/nortan_res"
RUN_NAME = "inference_nortan_res"


# Display configuration
print(f"New Site Analysis Configuration:")
print(f"Site Name: {NEW_SITE_NAME}")
print(f"Patch Size: {PATCH_SIZE}m radius")
print(f"Points per Patch: {NUM_POINTS}")
print(f"Model Path: {MODEL_PATH}")
print(f"Point Cloud Path: {POINT_CLOUD_PATH}")
print(f"Output Directory: {OUTPUT_DIR}")
print(f"Extended Analysis: {EXTENDED_ANALYSIS}")
print(f"Full Site Analysis: {FULL_SITE_ANALYSIS}")


import os
import json
import pickle
import torch
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
import torch.nn as nn
import matplotlib.pyplot as plt
import laspy
import open3d as o3d
import mlflow
import mlflow.pytorch
from scipy.spatial import cKDTree
from scipy.spatial.distance import pdist
import seaborn as sns

warnings.filterwarnings('ignore')


def setup_environment():
    """Setup output directory and MLflow tracking"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    if mlflow.active_run() is not None:
        mlflow.end_run()
    
    mlflow.set_experiment(EXPERIMENT_NAME)
    mlflow.start_run(run_name=RUN_NAME)
    
    # Log parameters
    params = {
        "site_name": NEW_SITE_NAME,
        "patch_size": PATCH_SIZE,
        "num_points": NUM_POINTS,
        "confidence_threshold": CONFIDENCE_THRESHOLD,
        "batch_size": BATCH_SIZE,
        "grid_spacing": GRID_SPACING
    }
    for key, value in params.items():
        mlflow.log_param(key, value)

# PointNet++ utility functions
def square_distance(src, dst):
    """Calculate squared distance between two point sets"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def farthest_point_sample(xyz, npoint):
    """Farthest point sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """Query ball point grouping"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

class PointNetSetAbstraction(nn.Module):
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        """Forward pass of PointNet++ Set Abstraction layer"""
        B, N, C = xyz.shape
        
        if self.group_all:
            new_xyz = torch.zeros(B, 1, C).to(xyz.device)
            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)
            
            if points is not None:
                grouped_points = points[torch.arange(B)[:, None, None], idx]
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm
        
        new_points = new_points.permute(0, 3, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = torch.relu(bn(conv(new_points)))
        
        new_points = torch.max(new_points, 2)[0]
        new_points = new_points.permute(0, 2, 1)
        
        return new_xyz, new_points

class PointNetPlusPlus(nn.Module):
    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):  # ✅ CRITICAL: in_channels=20
        super(PointNetPlusPlus, self).__init__()

        # Set Abstraction Layers (SAME AS TRAINING)
        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)
        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)
        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)

        # Classification head (SAME AS TRAINING)
        self.fc1 = nn.Linear(1024, 512)
        self.bn1 = nn.BatchNorm1d(512)
        self.drop1 = nn.Dropout(dropout)

        self.fc2 = nn.Linear(512, 256)
        self.bn2 = nn.BatchNorm1d(256)
        self.drop2 = nn.Dropout(dropout)

        self.fc3 = nn.Linear(256, 64)
        self.bn3 = nn.BatchNorm1d(64)
        self.drop3 = nn.Dropout(dropout * 0.6)

        self.fc4 = nn.Linear(64, num_classes)

    def forward(self, xyz):
        # Input shape: (B, N, C), C = in_channels (20 features)
        if len(xyz.shape) == 4:
            xyz = xyz.squeeze(1)

        B, N, C = xyz.shape

        # Split input into xyz coords and features (SAME AS TRAINING)
        coords = xyz[:, :, :3]       # (B, N, 3)
        features = xyz[:, :, 3:]     # (B, N, C-3) = (B, N, 17)

        # Pass through SA layers
        l1_xyz, l1_points = self.sa1(coords, features)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)

        global_feat = l3_points.view(B, -1)

        # Classification head
        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))
        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))
        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))
        x = self.fc4(x)

        return x

def load_point_cloud(file_path):
    """Load point cloud from supported formats (.las, .ply, .pcd)"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        print(f"File not found: {file_path}")
        return None
    
    suffix = file_path.suffix.lower()
    
    try:
        if suffix == '.las':
            las_file = laspy.read(file_path)
            points = np.vstack([las_file.x, las_file.y, las_file.z]).T
            print(f"Loaded LAS file with {len(points):,} points")
                
        elif suffix in ['.ply', '.pcd']:
            pcd = o3d.io.read_point_cloud(str(file_path))
            points = np.asarray(pcd.points)
            
            if len(points) == 0:
                print(f"Warning: {suffix.upper()} file contains no points")
                return None
                
            print(f"Loaded {suffix.upper()} file with {len(points):,} points")
                
        else:
            print(f"Unsupported file format: {suffix}. Supported formats: .las, .ply, .pcd")
            return None
        
        if len(points) == 0:
            print("Warning: Point cloud contains no points")
            return None
            
        print(f"Point cloud bounds:")
        print(f"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}")
        print(f"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}")
        print(f"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}")
        
        return points
        
    except Exception as e:
        print(f"Error loading point cloud: {e}")
        return None


def load_model(model_path, device):
    """Load trained PointNet++ model"""
    try:
        model = PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device)
        
        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=device)
            
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
            else:
                model.load_state_dict(checkpoint)
            
            model.eval()
            param_count = sum(p.numel() for p in model.parameters())
            print(f"Loaded trained PointNet++ model with {param_count:,} parameters")
            return model, True
        else:
            print(f"Model file not found: {model_path}")
            return model, False
            
    except Exception as e:
        print(f"Error loading model: {e}")
        return PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device), False

def create_analysis_grid(point_cloud, grid_spacing=15.0, buffer_factor=1.5):
    """Create analysis grid with spatial bounds optimization"""
    print(f"Point cloud has {len(point_cloud):,} points")
    
    # Get bounds efficiently
    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()
    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()
    
    print(f"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})")
    
    buffer = grid_spacing * buffer_factor
    x_coords = np.arange(x_min - buffer, x_max + buffer, grid_spacing)
    y_coords = np.arange(y_min - buffer, y_max + buffer, grid_spacing)
    
    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])
    print(f"Created analysis grid with {len(grid_points)} points")
    
    return grid_points


def extract_patch_features(point_cloud, center_xy, radius=20.0, num_points=1024, kdtree=None):
    """Extract patch features EXACTLY matching training preprocessing"""
    center_x, center_y = center_xy
    
    if kdtree is not None:
        indices = kdtree.query_ball_point([center_x, center_y], radius)
        if len(indices) < 50:
            return None
        patch_xyz = point_cloud[indices]
    else:
        distances_2d = np.sqrt((point_cloud[:, 0] - center_x)**2 + 
                              (point_cloud[:, 1] - center_y)**2)
        mask = distances_2d <= radius
        patch_xyz = point_cloud[mask]
        
        if len(patch_xyz) < 50:
            return None
    
    # MATCH training preprocessing exactly - use same feature engineering as training data creation
    x, y, z = patch_xyz[:, 0], patch_xyz[:, 1], patch_xyz[:, 2]
    
    # Calculate patch statistics
    patch_center = np.mean(patch_xyz, axis=0)
    z_mean, z_std = z.mean(), z.std() + 1e-6
    z_min, z_max = z.min(), z.max()
    
    # Distance calculations
    dist_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
    
    # RECREATE EXACT TRAINING FEATURES - these come from the original data creation process
    features_list = []
    for i, point in enumerate(patch_xyz):
        px, py, pz = point
        
        # Match the exact feature engineering from training data preparation
        feature_vector = [
            px,  # 0: raw X coordinate
            py - center_y,  # 1: relative Y (small values like training)
            pz,  # 2: raw Z coordinate  
            (pz - z_mean) / z_std,  # 3: height_norm
            dist_to_center[i],  # 4: distance_norm
            len(patch_xyz) / 1000.0,  # 5: density
            px - center_x,  # 6: relative_x
            py - center_y,  # 7: relative_y  
            pz - patch_center[2],  # 8: relative_z
            pz - z_min,  # 9: height_above_min
            z_max - pz,  # 10: depth_below_max
            abs(pz - z_mean),  # 11: abs_height_deviation
            np.sqrt(px**2 + py**2),  # 12: distance_from_origin
            np.arctan2(py - center_y, px - center_x),  # 13: angle
            (px - center_x) * (py - center_y),  # 14: interaction
            px - patch_center[0],  # 15: patch_relative_x
            py - patch_center[1],  # 16: patch_relative_y
            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio
            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance
            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation
        ]
        features_list.append(feature_vector)
    
    patch_features = np.array(features_list, dtype=np.float32)
    
    # EXACT SAME SAMPLING LOGIC AS TRAINING
    if len(patch_features) >= num_points:
        # Distance-weighted sampling like training
        distances = patch_features[:, 4]  # distance_norm column
        probabilities = 1 / (distances + 0.1)
        probabilities /= probabilities.sum()
        
        sampled_indices = np.random.choice(len(patch_features), num_points, 
                                         replace=False, p=probabilities)
        sampled = patch_features[sampled_indices]
    else:
        # Upsample with weighted selection like training
        upsampled = patch_features.copy()
        needed = num_points - len(patch_features)
        
        for _ in range(needed):
            distances = patch_features[:, 4]
            weights = 1 / (distances + 0.1)
            weights /= weights.sum()
            source_idx = np.random.choice(len(patch_features), p=weights)
            
            new_point = patch_features[source_idx].copy()
            new_point[:3] += np.random.normal(0, 0.02, 3)  # Add noise to spatial like training
            upsampled = np.vstack([upsampled, new_point])
        
        sampled = upsampled[:num_points]
    
    # EXACT SAME NORMALIZATION AS TRAINING
    # Training shows: sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1
    spatial_coords = sampled[:, :3]
    spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))
    if spatial_extent > 0:
        sampled[:, :3] /= spatial_extent
    
    return sampled



def process_site_inference(point_cloud, grid_points, model, device, 
                          batch_size=16, radius=20.0, num_points=1024):
    """CPU-optimized inference maintaining 1024 points for accuracy"""
    print(f"Building spatial index for {len(point_cloud):,} points...")
    kdtree = cKDTree(point_cloud[:, :2])
    
    print("Pre-filtering valid grid points...")
    valid_grid_points = []
    for i, center in enumerate(grid_points):
        if i % 500 == 0:
            print(f"  Pre-filtering progress: {i}/{len(grid_points)}")
        
        indices = kdtree.query_ball_point([center[0], center[1]], radius)
        if len(indices) >= 50:  # Keep higher threshold for 1024 points
            valid_grid_points.append(center)
    
    valid_grid_points = np.array(valid_grid_points)
    print(f"Filtered to {len(valid_grid_points)} valid grid points (from {len(grid_points)})")
    
    if len(valid_grid_points) == 0:
        return pd.DataFrame()
    
    results = []
    total_batches = len(valid_grid_points) // batch_size + (1 if len(valid_grid_points) % batch_size != 0 else 0)
    
    print(f"Processing {len(valid_grid_points)} points with 1024 points/patch (CPU optimized)...")
    print(f"This maintains training accuracy but will be slower than 512 points")
    
    model.eval()
    if device.type == 'cpu':
        torch.set_num_threads(4)
    
    for i in range(0, len(valid_grid_points), batch_size):
        batch_idx = i // batch_size + 1
        if batch_idx % 3 == 1:  # More frequent updates since batches are smaller
            print(f"  Batch {batch_idx}/{total_batches} ({i}/{len(valid_grid_points)} points)")
        
        batch_centers = valid_grid_points[i:i+batch_size]
        batch_patches = []
        valid_centers = []
        
        for center in batch_centers:
            patch = extract_patch_features(point_cloud, center, radius, num_points, kdtree)
            if patch is not None:
                batch_patches.append(patch)
                valid_centers.append(center)
        
        if len(batch_patches) == 0:
            continue
        
        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)
        
        with torch.no_grad():
            outputs = model(batch_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            pile_probs = probabilities[:, 1].cpu().numpy()
        
        for center, prob in zip(valid_centers, pile_probs):
            results.append({
                'x': center[0],
                'y': center[1],
                'pile_probability': prob,
                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'
            })
    
    print(f"CPU processing complete: {len(results)} successful predictions")
    return pd.DataFrame(results)


def visualize_results(results_df, output_dir, site_name):
    """Create visualization of pile detection results"""
    if results_df.empty:
        print("No results to visualize")
        return
    
    pile_detections = results_df[results_df['prediction'] == 'PILE']
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Probability heatmap
    sc1 = ax1.scatter(
        results_df['x'], results_df['y'],
        c=results_df['pile_probability'],
        cmap='viridis', s=25, alpha=0.8
    )
    ax1.set_title('Pile Probability Heatmap')
    ax1.set_xlabel('X Coordinate')
    ax1.set_ylabel('Y Coordinate')
    plt.colorbar(sc1, ax=ax1, label='Probability')
    
    # Plot 2: Pile classifications
    ax2.scatter(
        pile_detections['x'], pile_detections['y'],
        color='darkgreen', label='Pile Detections',
        s=30, alpha=0.8
    )
    
    non_pile = results_df[results_df['prediction'] == 'NON-PILE']
    if not non_pile.empty:
        ax2.scatter(
            non_pile['x'], non_pile['y'],
            color='gray', label='Non-Pile',
            s=15, alpha=0.4
        )
    
    ax2.set_title('Pile Classification Results')
    ax2.set_xlabel('X Coordinate')
    ax2.set_ylabel('Y Coordinate')
    ax2.legend()
    
    plt.tight_layout()
    
    plot_path = Path(output_dir) / f"{site_name}_pile_visualization.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Saved visualization to: {plot_path}")
    
    plt.show()
    return str(plot_path)

def export_results(results_df, output_dir, site_name, model_path, patch_size, confidence_threshold):
    """Export results and summary statistics"""
    if results_df.empty:
        print("No results to export")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Export main results
    output_file = Path(output_dir) / f"{site_name}_pile_detections_{timestamp}.csv"
    results_df.to_csv(output_file, index=False)
    print(f"Results exported to: {output_file}")
    
    # Create summary statistics
    pile_detections = results_df[results_df['prediction'] == 'PILE']
    
    summary = {
        'site_name': site_name,
        'analysis_timestamp': timestamp,
        'total_analysis_points': len(results_df),
        'pile_detections': len(pile_detections),
        'detection_rate': len(pile_detections) / len(results_df),
        'average_pile_probability': float(results_df['pile_probability'].mean()),
        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),
        'model_path': model_path,
        'patch_size_meters': patch_size,
        'confidence_threshold': confidence_threshold
    }
    
    summary_file = Path(output_dir) / f"{site_name}_analysis_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Summary statistics saved to: {summary_file}")
    
    # Log to MLflow
    for key, value in summary.items():
        if isinstance(value, (int, float)):
            mlflow.log_metric(key, value)
    
    mlflow.log_artifact(str(output_file))
    mlflow.log_artifact(str(summary_file))
    
    return summary



print("Starting PointNet++ Pile Detection Pipeline")
print(f"Site: {NEW_SITE_NAME}")
print(f"Configuration: {PATCH_SIZE}m patches, {NUM_POINTS} points each")
    
# Setup
setup_environment()
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Load model
model, model_loaded = load_model(MODEL_PATH, device)
    
# Load point cloud
point_cloud = load_point_cloud(POINT_CLOUD_PATH)
if point_cloud is None:
    print("Failed to load point cloud. Exiting.")
    mlflow.end_run()    


# Create analysis grid
grid_points = create_analysis_grid(point_cloud, GRID_SPACING)

# Run inference
results_df = process_site_inference(
    point_cloud, grid_points, model, device,
    batch_size=BATCH_SIZE, radius=PATCH_SIZE, num_points=NUM_POINTS
)


if not results_df.empty:
    # Print summary
    pile_count = len(results_df[results_df['prediction'] == 'PILE'])
    print(f"\nAnalysis Results:")
    print(f"  Total analysis points: {len(results_df)}")
    print(f"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)")
    print(f"  Average confidence: {results_df['pile_probability'].mean():.4f}")
        
    # Visualize and export
    plot_path = visualize_results(results_df, OUTPUT_DIR, NEW_SITE_NAME)
    
    if plot_path:
        mlflow.log_artifact(plot_path)

    export_results(results_df, OUTPUT_DIR, NEW_SITE_NAME, 
                      MODEL_PATH, PATCH_SIZE, CONFIDENCE_THRESHOLD)
    
    # Cleanup
    mlflow.end_run()
    print(f"\nPipeline complete. Results saved to: {OUTPUT_DIR}")