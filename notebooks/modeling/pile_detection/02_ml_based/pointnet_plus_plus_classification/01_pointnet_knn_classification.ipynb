{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["# PointNet++ <PERSON>le Detection\n", "\n", "This notebook implements PointNet++ architecture for pile detection using the patch data prepared from the successful harmonization and extraction pipeline.\n", "\n", "**Architecture:**\n", "- Farthest point sampling and k-nearest neighbor (k-NN) grouping\n", "- Hierarchical PointNet layers for feature extraction\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"], "metadata": {"id": "oJyT6RTkQ9Ev"}}, {"cell_type": "markdown", "source": ["## Mount\n"], "metadata": {"id": "Lbecie4kWRFC"}}, {"cell_type": "code", "source": ["\n", "from google.colab import drive\n", "drive.mount('/content/drive')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A13AOoCORYK4", "outputId": "b03c3a85-577e-474b-9aa2-7260d804ddce"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}]}, {"cell_type": "markdown", "source": ["## Imports"], "metadata": {"id": "IMIDXMjwRRcR"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cGb-84TO7u3R"}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import pandas as pd\n", "import pickle\n", "import os\n", "from pathlib import Path\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix ,classification_report\n", "\n", "import matplotlib.pyplot as plt\n", "import json\n", "import warnings\n", "import time\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "source": ["## Configuration"], "metadata": {"id": "TROcsjtpRbCd"}}, {"cell_type": "code", "source": ["# Google Drive paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "\n", "project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER\n", "data_path = project_path / \"pointnet_data\"\n", "results_path = project_path / \"results_iter1\"\n", "models_path = project_path / \"models_iter1\"\n", "\n", "ifc_path = project_path / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "kml_path = project_path / \"pile.kml\"\n", "test_pkl_path = data_path / \"test_pointnet.pkl\"\n", "harmonized_pile_dataset = project_path/\"harmonized_pile_dataset_final.csv\"\n", "\n", "# Create directories\n", "for path in [results_path, models_path]:\n", "    path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Device configuration\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Training configuration\n", "config = {\n", "    'batch_size': 8,\n", "    'num_epochs': 100,\n", "    'learning_rate': 0.003,\n", "    'num_points': 512,\n", "    'patience': 15,\n", "    'device': device\n", "}\n", "\n", "print(\"Configuration:\", config)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C33B4efqPtm9", "outputId": "44dd5e42-6b76-402a-ac5a-611a8f8509ad"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using device: cuda\n", "Configuration: {'batch_size': 8, 'num_epochs': 100, 'learning_rate': 0.003, 'num_points': 512, 'patience': 15, 'device': device(type='cuda')}\n"]}]}, {"cell_type": "markdown", "source": ["## Load and Preprocess Data"], "metadata": {"id": "wqqNEGh9RgYs"}}, {"cell_type": "code", "source": ["def load_pile_data(data_path):\n", "    \"\"\"Load pile detection data from pickle files\"\"\"\n", "    datasets = {}\n", "    file_mapping = {\n", "        'train': 'train_pointnet.pkl',\n", "        'val': 'val_pointnet.pkl',\n", "        'test': 'test_pointnet.pkl'\n", "    }\n", "\n", "    print(f\"Loading data from: {data_path}\")\n", "\n", "    for split, filename in file_mapping.items():\n", "        filepath = data_path / filename\n", "\n", "        if not filepath.exists():\n", "            print(f\"ERROR: {filepath} not found!\")\n", "            return None\n", "\n", "        with open(filepath, 'rb') as f:\n", "            data = pickle.load(f)\n", "\n", "        patches = data['points']\n", "        labels = data['labels']\n", "\n", "        # Extract XYZ coordinates only\n", "        if patches.shape[2] > 3:\n", "            patches = patches[:, :, :3]\n", "\n", "        patches = patches.astype(np.float32)\n", "        labels = np.array(labels)\n", "\n", "        print(f\"{split}: {patches.shape}, labels: {len(labels)}\")\n", "        print(f\"Label distribution: {np.sum(labels)} piles / {len(labels)} total\")\n", "\n", "        datasets[split] = {\n", "            'patches': patches,\n", "            'labels': labels,\n", "            'metadata': data.get('metadata', [])\n", "        }\n", "\n", "    return datasets"], "metadata": {"id": "OEP8x99gPyuw"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def preprocess_patches(patches, labels, num_points):\n", "    \"\"\"Preprocess point cloud patches\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Preprocessing {len(patches)} patches...\")\n", "\n", "    for i, (patch, label) in enumerate(zip(patches, labels)):\n", "        # Remove zero-padded points\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 10:\n", "            continue\n", "\n", "        # Sample to fixed number of points\n", "        if len(valid_points) >= num_points:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=False)\n", "            sampled = valid_points[indices]\n", "        else:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=True)\n", "            sampled = valid_points[indices]\n", "            noise = np.random.normal(0, 0.01, sampled.shape)\n", "            sampled = sampled + noise\n", "\n", "        # Center and normalize\n", "        centroid = np.mean(sampled, axis=0)\n", "        sampled = sampled - centroid\n", "\n", "        max_dist = np.max(np.linalg.norm(sampled, axis=1))\n", "        if max_dist > 1e-6:\n", "            sampled = sampled / max_dist\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Preprocessed {len(processed_patches)} valid patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)"], "metadata": {"id": "gIs8XgOURlJK"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Basic PointNet++ Architecture"], "metadata": {"id": "BSHYOaQlRmsb"}}, {"cell_type": "code", "source": ["def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "\n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "\n", "    return centroids\n", "\n", "def knn_group(xyz, new_xyz, k=32):\n", "    \"\"\"K-nearest neighbor grouping\"\"\"\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    dist = torch.cdist(new_xyz, xyz)\n", "    _, idx = torch.topk(dist, k, dim=2, largest=False)\n", "    return idx"], "metadata": {"id": "pZ8u8dKzQBaS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["class PointNetLayer(nn.Module):\n", "    \"\"\"Basic PointNet layer for hierarchical feature learning\"\"\"\n", "\n", "    def __init__(self, npoint, k, in_channels, out_channels):\n", "        super().__init__()\n", "        self.npoint = npoint\n", "        self.k = k\n", "\n", "        self.conv1 = nn.Conv1d(in_channels, out_channels // 2, 1)\n", "        self.conv2 = nn.Conv1d(out_channels // 2, out_channels, 1)\n", "        self.bn1 = nn.BatchNorm1d(out_channels // 2)\n", "        self.bn2 = nn.BatchNorm1d(out_channels)\n", "\n", "    def forward(self, xyz, features=None):\n", "        B, N, C = xyz.shape\n", "\n", "        if self.npoint is None:\n", "            # Global pooling layer\n", "            if features is not None:\n", "                features = features.transpose(1, 2)\n", "                x = torch.relu(self.bn1(self.conv1(features)))\n", "                x = torch.relu(self.bn2(self.conv2(x)))\n", "                pooled = torch.max(x, dim=2)[0]\n", "                return xyz.mean(dim=1, keepdim=True), pooled.unsqueeze(1)\n", "            else:\n", "                xyz_t = xyz.transpose(1, 2)\n", "                x = torch.relu(self.bn1(self.conv1(xyz_t)))\n", "                x = torch.relu(self.bn2(self.conv2(x)))\n", "                pooled = torch.max(x, dim=2)[0]\n", "                return xyz.mean(dim=1, keepdim=True), pooled.unsqueeze(1)\n", "\n", "        # Hierarchical sampling and grouping\n", "        fps_idx = farthest_point_sample(xyz, self.npoint)\n", "        new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "        knn_idx = knn_group(xyz, new_xyz, self.k)\n", "\n", "        if features is not None:\n", "            grouped_features = features[torch.arange(B)[:, None, None], knn_idx]\n", "        else:\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], knn_idx]\n", "            grouped_features = grouped_xyz\n", "\n", "        # Pooling and MLP\n", "        pooled_features = torch.max(grouped_features, dim=2)[0]\n", "        pooled_features = pooled_features.transpose(1, 2)\n", "        x = torch.relu(self.bn1(self.conv1(pooled_features)))\n", "        x = torch.relu(self.bn2(self.conv2(x)))\n", "        x = x.transpose(1, 2)\n", "\n", "        return new_xyz, x"], "metadata": {"id": "GaPD7cfaQNPS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["class PointNetPlusPlus(nn.Module):\n", "    \"\"\"Basic PointNet++ for pile classification\"\"\"\n", "\n", "    def __init__(self, num_classes=2):\n", "        super().__init__()\n", "\n", "        # Hierarchical feature extraction\n", "        self.layer1 = PointNetLayer(256, 32, 3, 64)\n", "        self.layer2 = PointNetLayer(64, 32, 64, 128)\n", "        self.layer3 = PointNetLayer(None, 32, 128, 256)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.bn1 = nn.BatchNorm1d(128)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.bn2 = nn.BatchNorm1d(64)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        B = xyz.shape[0]\n", "\n", "        # Hierarchical feature extraction\n", "        xyz1, feat1 = self.layer1(xyz)\n", "        xyz2, feat2 = self.layer2(xyz1, feat1)\n", "        xyz3, feat3 = self.layer3(xyz2, feat2)\n", "\n", "        # Global feature vector\n", "        global_feat = feat3.view(B, -1)\n", "\n", "        # Classification\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x"], "metadata": {"id": "PGWUvxk8QPQH"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Dataset Class"], "metadata": {"id": "Jp1_OWUcR1Ak"}}, {"cell_type": "code", "source": ["class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n"], "metadata": {"id": "J33DlfmxQF9U"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Training and Validation Epoch Funcitons"], "metadata": {"id": "BK9b9gg3R6c3"}}, {"cell_type": "code", "source": ["def train_epoch(model, loader, criterion, optimizer, device):\n", "    \"\"\"Train one epoch\"\"\"\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "        optimizer.zero_grad()\n", "\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total"], "metadata": {"id": "zwaDG88KQTKU"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def validate_epoch(model, loader, criterion, device):\n", "    \"\"\"Validate one epoch\"\"\"\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total"], "metadata": {"id": "9P7srYu_R-q1"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Train Loop"], "metadata": {"id": "otB10M_ZSCQ6"}}, {"cell_type": "code", "source": ["def run_training(model, train_loader, val_loader, criterion, optimizer, config, models_path):\n", "    train_losses = []\n", "    val_losses = []\n", "    train_accs = []\n", "    val_accs = []\n", "    best_val_acc = 0\n", "    patience_counter = 0\n", "    start_time = time.time()\n", "\n", "    for epoch in range(config['num_epochs']):\n", "        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, config['device'])\n", "        val_loss, val_acc = validate_epoch(model, val_loader, criterion, config['device'])\n", "\n", "        train_losses.append(train_loss)\n", "        val_losses.append(val_loss)\n", "        train_accs.append(train_acc)\n", "        val_accs.append(val_acc)\n", "\n", "        if (epoch + 1) % 10 == 0:\n", "            print(f\"Epoch {epoch+1}/{config['num_epochs']}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}\")\n", "\n", "        if val_acc > best_val_acc:\n", "            best_val_acc = val_acc\n", "            patience_counter = 0\n", "            torch.save({\n", "                'model_state_dict': model.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'epoch': epoch,\n", "                'val_acc': val_acc,\n", "                'config': config\n", "            }, models_path / 'best_pointnet_iter1.pth')\n", "        else:\n", "            patience_counter += 1\n", "            if patience_counter >= config['patience']:\n", "                print(f\"Early stopping at epoch {epoch+1}\")\n", "                break\n", "\n", "    training_time = time.time() - start_time\n", "    print(f\"Training completed in {training_time:.1f}s\")\n", "    print(f\"Best validation accuracy: {best_val_acc:.4f}\")\n", "\n", "    history = {\n", "        'train_losses': train_losses,\n", "        'val_losses': val_losses,\n", "        'train_accs': train_accs,\n", "        'val_accs': val_accs\n", "    }\n", "\n", "    return history, best_val_acc, training_time"], "metadata": {"id": "5wYY8aRwWtDS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Load data\n", "datasets = load_pile_data(data_path)\n", "\n", "if not datasets:\n", "    print(\"ERROR: No data found!\")\n", "    exit()\n", "\n", "# Preprocess data\n", "train_patches, train_labels = preprocess_patches(\n", "    datasets['train']['patches'], datasets['train']['labels'], config['num_points']\n", ")\n", "val_patches, val_labels = preprocess_patches(\n", "    datasets['val']['patches'], datasets['val']['labels'], config['num_points']\n", ")\n", "test_patches, test_labels = preprocess_patches(\n", "    datasets['test']['patches'], datasets['test']['labels'], config['num_points']\n", ")\n", "\n", "\n", "# Create datasets and loaders\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, drop_last=True)\n", "val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q236-wSmYXiN", "outputId": "e3d4af3a-4e8d-4597-e197-ed1f12766361"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Loading data from: /content/drive/MyDrive/pointnet_pile_detection/pointnet_data\n", "train: (1287, 1024, 3), labels: 1287\n", "Label distribution: 861 piles / 1287 total\n", "val: (418, 1024, 3), labels: 418\n", "Label distribution: 287 piles / 418 total\n", "test: (418, 1024, 3), labels: 418\n", "Label distribution: 287 piles / 418 total\n", "Preprocessing 1287 patches...\n", "Preprocessed 1287 valid patches\n", "Preprocessing 418 patches...\n", "Preprocessed 418 valid patches\n", "Preprocessing 418 patches...\n", "Preprocessed 418 valid patches\n"]}]}, {"cell_type": "code", "source": ["# Initialize model\n", "model = PointNetPlusPlus(num_classes=2).to(device)\n", "\n", "# Class balancing\n", "pos_weight = len(train_labels) / (2 * np.sum(train_labels))\n", "neg_weight = len(train_labels) / (2 * (len(train_labels) - np.sum(train_labels)))\n", "class_weights = torch.FloatTensor([neg_weight, pos_weight]).to(device)\n", "criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "\n", "optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])\n", "\n", "\n", "# Model info\n", "param_count = sum(p.numel() for p in model.parameters())\n", "print(f\"Model parameters: {param_count:,}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vkoTlUOdYqHt", "outputId": "e166f786-9c55-436d-ffe9-91e9e59fe4c9"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model parameters: 107,266\n"]}]}, {"cell_type": "code", "source": ["history, best_val_acc, training_time = run_training(model, train_loader, val_loader, criterion, optimizer, config, models_path)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0yT5dNobY9jC", "outputId": "d22c7b0b-4fb2-4926-cc1d-82d11dd079c6"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 10/100: Train Acc=0.5594, Val Acc=0.6124\n", "Epoch 20/100: Train Acc=0.5789, Val Acc=0.6794\n", "Epoch 30/100: Train Acc=0.6297, Val Acc=0.6699\n", "Early stopping at epoch 31\n", "Training completed in 708.5s\n", "Best validation accuracy: 0.6866\n"]}]}, {"cell_type": "markdown", "source": ["## Evaluation and Metrics"], "metadata": {"id": "qg0wORq0Wzfr"}}, {"cell_type": "code", "source": ["def evaluate_model(model, test_loader, device):\n", "    model.eval()\n", "    all_preds = []\n", "    all_targets = []\n", "\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            all_preds.extend(output.argmax(dim=1).cpu().numpy())\n", "            all_targets.extend(target.cpu().numpy())\n", "\n", "    return all_preds, all_targets\n", "\n", "all_preds, all_targets = evaluate_model(model, test_loader, device)\n"], "metadata": {"id": "M9PVEQmQQVTA"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def compute_metrics(all_preds, all_targets):\n", "    test_accuracy = accuracy_score(all_targets, all_preds)\n", "    print(f\"True Test Accuracy: {test_accuracy:.3f}\")\n", "\n", "    test_f1 = f1_score(all_targets, all_preds)\n", "    test_precision = precision_score(all_targets, all_preds)\n", "    test_recall = recall_score(all_targets, all_preds)\n", "    cm = confusion_matrix(all_targets, all_preds)\n", "\n", "    print(\"\\nTEST METRICS:\")\n", "    print(f\"Accuracy: {test_accuracy:.4f}\")\n", "    print(f\"F1-Score: {test_f1:.4f}\")\n", "    print(f\"Precision: {test_precision:.4f}\")\n", "    print(f\"Recall: {test_recall:.4f}\")\n", "    print(\"Confusion Matrix:\\n\", cm)\n", "\n", "    return {\n", "        'accuracy': test_accuracy,\n", "        'f1_score': test_f1,\n", "        'precision': test_precision,\n", "        'recall': test_recall,\n", "        'confusion_matrix': cm.tolist()\n", "    }"], "metadata": {"id": "zG6NYzSfW-mh"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Visualization"], "metadata": {"id": "ZhiAIAafSbwR"}}, {"cell_type": "code", "source": ["# Plot training and validation loss\n", "plt.figure(figsize=(10, 4))\n", "plt.subplot(1, 2, 1)\n", "\n", "plt.plot(history['train_losses'], label='Train Loss')\n", "plt.plot(history['val_losses'], label='Val Loss')\n", "plt.title(\"Loss\")\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history['train_accs'], label='Train Acc')\n", "plt.plot(history['val_accs'], label='Val Acc')\n", "plt.title(\"Accuracy\")\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 407}, "id": "epzPV4yDTPl_", "outputId": "3c4552ab-63c8-4ca2-90cf-043e89d9ec8c"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x400 with 2 Axes>"], "image/png": "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*******************************+0nG5DM2K57qX1lFQVsulf1vOkMx4NrqkkI/rk8wvJvVlxojM9gMDdw7Yi6i59OdOiY3gtIGpDE6wcccFo/lwcxH/XLGfw8UpZGtl3P/8f6nJnMDPJ/Zh5phsR5q3q2e/3stDn+wA4Lc/GMbPTzUnZfXU/r344MbTue2/G1mz/xjnDMvggtHZTBrQq+UaWw9FhFm45vT+XDw2h0c/28nLq/bz8eYivthWwpzJfZl39kDiTKoiL7ruyx3qwmlTXDbUblDHoC7ITY7hzMFpfLG9hFdXF3Bq/xQW/Ps7ymsbSYgK488/HsM5wztYDtHcCOX2uhOBSC93zHR3YU230ZEkkEF3oPt0lwZwiYA/xKVD8RZzi6lJn27/i04BzQK6TWXsGv//T+QoohbgicUeohtepjOZEXQHol2YK6NVWNluz/ru+Zsxe5trH2dEDKTbr5KGWr9uX8109xqoriY2HYeSbeZuO4CSYyP43Q+HAfDYZzspKK1t+8mFLjPdnrIH3Q01x3j+GxVwzxqfx0c3TwmKgNsQGWblhqmD+PhXU5g8sBf1TTYe/Hg7P3z8K77dV+Z43vqCY/zsHyrgHt8nmX92MeA2nJSdyLvzT+OsIWnUN9nYeLCCCKuFS07O4Z35k/nvdZO4YHR25wNuUBk24L4/NxAZbuWSk3N5a95k4nqPAWBkWAFbjlSy8H+byL9/CYve3cJel8ryr6wq4N73VFGmX00bxNwp5s7+9YqL5B9XnsKGu87lwR+NZsrgNK8C7hO3ee9FI/jwpimcPiiVhmYbf/9yD2c9uJSXVxU4CtKJwFpmD7rj0/uqByoOdXmbs/PVBaEXl+/jqhfWUF7byOjcRN6/8fSOA26AY/tV+9HwGNW+y9+MIlnHy1RBtM5qblIFEsF9y0B/ce3TrQfg/5tjiUA///9sf4gz4eLMiWRNt/9ZLM4M3fbWdTvOd93UoBCmk6C7I8GSXh6d7AxijVmnYGIUUcsZ73wse4y6DbV13b4Kui0W5wxBqF2I6MDFY3OYNEAFmr97+3t0dydDtmZngS0Pg+66xmYe/lKlQ0bSQF68xvNzTuFPl40yJVD1hX6psbx0dT6PzhpDr9gIdhZX86OnVnD7fzfyxbZifv7saqrqm5jQN4UXrppAvIm/R2JMOM9eeQqLZp7ErTOG8M3tZ/Pwj8e06nPdKY11zv/DvfM7fHpCX3VCftvYJn77g2H06RVDVV0Tz32zl7MeWsrPn13Fw59s57dvqayH/5vSn5umBmGdinYMyYznn1dN4PlfnEL/tFhKaxq48+3NXPXqVhqbze/fLjxX39TMit3qYnluX3vWTRfTywHOHJJGVmIU9U3q7/uLSX35zy8nedy72xms9Q/McquYFLDYP2u8STE/ukMtIYuIV6nVgWKs6W5ugMbj/v/5pQFcl+8Pvmgb5ljTLUG3X3lSTM1xvisz3f4gQXdHgqGQmiHPfsJbEGRB9/FjULpT3TfWLYMqHAZqXXco8WUlx1DdJx3QNI37LhpBRJjF0bu7lbK9qnVIWJSa9e/AhgPl/PDxr3hieSE2XZ2kvn/taM5yVxU4yGiaxkVjc1jy6zO4/BTVuuq1bw8w54Vvqa5vIr9fCs/POcUnLagsFo0rJvbl+jMHkhbf8ZrtDh1er05w4zI8O9nOUBXMI49uYe6U/nzx6zN5fs4pnD00HU2Dr3Ye5fHPd6HrcMXEPtx+3lCPis4FG03TOGtoOh//agq/v2A4idHhjM2J9y6TQJhmzb5jHG9sJi0+kuze9sCoi+nlAGFWC3dfMJxRuYn8dfbJ/P7Ck9ptQdiKa4/uQNA0l7ZhXgTdjvXcowK7ljkiTi1dg8AUU3NpF9YtmbH235Wuu6zplqDbr+Ls57DtZcca57txwX9e1R10j0WlvlRjpJcHQdDdeyKsfT74gm4jtTylf8u170YK2uH1oVVMzVdrusG5rrubzXQD9E+La793d6H9pC3jpHbXsx8qP84TS3byn7UHabbppMZF06zFYWmsIkELwMxGFyTFRPDHS0dxycm53PHmJnYVVzOxfy+e/cV4t2ucg5JRR6L3qZ79Hzb6rxdvBVszFouVs4akc9aQdA6U1fLSqv28u+Ew5wzP4O4LTgrJgNtVuNXCLyb348LRWZSVlgZ6OD2esZ57yqA0tAT7SX7FIVOOQTNGZDFjhJep4aVBEKzFZUDFAe9Sh43uJIFczw3qbxidpJb+HT8GCdn++9nNjWqZAHTfme6uXJhxp6leLasAWdPtb52a6Q6epXrdWYic9QWIrrvMdAdBexijiNGRDSpdJ1g+wA7ag27X1HKA9JPAGqGuRh/bG7gr/J3h656Fxkx30WaVttvNqkW227vbKKLWRmp5UWUdT36xi9dWH6DBnqJ7wehsFl14EuFPJ0NFldte3aFgQr8UPrjxdDYdKmdUblJozYYaF/nyTm3/eYaU/hAWrWoXlO6GtMGOb+WlxLDwvGEsPG+YDwYaWEkxETRUSzG1QDPWc08ZnAoJ9lTkxhr12WGsBw6EsgBWLjc4Aipvgu4gqFxuiEqyB93l/v255QUqgAyLDsy6fH8wznvMCrobXWq8SHq5fxnrtGvaWSrgKKQmQbc/hNCZXwA01qg1TBAcM91JvdUHva0puGZKjcrluScE3WERjlTTkFnXXVeu9i/4ZqY7MU+9l2xNziC0GzF6d4MqlLV2v0uFWUe7sJEtXnO0up773tvClAe+4J8r9tPQbGNi/1688cuJPPGTsSTHRri0DSv3w2/hGxFhFsb1SQmtgNtmc1u5vF0Wq7PVTCf7dQvRFUWVdWwrrELTUEUWI2Kca4Aru15MrUuCYS2wkTpc1cmAymZzfn5njTF1SF4JVK9u1yUC3bFdGLjMjh41Z3tGark1stt0bAkZxjlse39LR2anBN3+0E0/NUxSa684bI0MjrUomuY88Q2W1mG67lK5fHzr74faGmZHz8JECDNhPeyJNM25T4LpwomJjN7dAHf8b5MqLKXrzsrl9pmS8toGHvhoG1Me+IJ/fL2X+iYb4/ok88o1+bx67amM75vi3OgJvbqFnxzdrvZ5eGznKs4bF9uMwnlC+MGXO9Xn96icRFJiI9SDCfa+7yas6/ZaU4NK64bAppc72oa5qbnRnrLd9noc0ZAaBEUPA9Wru7SbVy4Hl/7Ox1TF+q4yiqgFwzl0TyPp5UFHgu72uK7tDZZ1h0aKZ8GqwI7DULZHtSCxRkLGyNbfd6zr3uDXYXnNH5UcjWJzoXIhwgsLzxtGSmwE24uq+MdXe1WqWk0JaBaqEgfx6Gc7OP1PX/DXpbupbWhmZE4iL8w5hTd+OZFJA93sewm6A8O4uJc7vnOzFEY2Q5EE3cJ/vtqhjtlnDHY5gUzMUbcVXa9g7rVj+1S/3Ig452xzIHhbJMs4fmeOVJksgRaoXt3dvYgaqCr3aICuzu26StqFBY4nleilkJpfSa5He4z13IHu0e3KmOk+sFq1YAr0AdBoFZY1SqWTn8goHHZkQ3CMtyP+uOrXjYupGYze3Qv+/R2PLdnBpfFNpANl0X04+5FVlNc2AjA0M54F5wzmnOEZ7RfUkqA7MAo6mVpuMGa6izabO57O2r8CDq6G/Ovcfz6JbqPZpvPVLnXMnuIadBuFtgKZXu5IS+4X2Av48V72YA6WImoG117d/hQMSwR8zWJV57y1R9X5UFeDsUYJugOmo/Tyxjqor2z5XOFTMtPdnlp7JdpgejNmjFCpnvUVqjpwoB2yB925p7j/fupgCI9R63pKd/lvXN7yy0y3Peg+uhPqKn33cwLM6N1d12jj/U8/BuDLqizKaxsZkBbLX346lg9uPJ1zT8rsuIK1BN2B4Vq5vDMyTlK3lYecy3T8zWaDN66CT++CLx8MzBiE32wtqqXieCPxUWGMyUtyfiPBPtMdyPTysiAJ1ryd6TaKqGWPMXU4XjPWdPs7vbwnzHSDZ2nJnjJmusODpPBvT+JaSE3XW3/f+Ptawp3ZI8KnJOhuj3F1KBiKqBmsYc610wdXB3Ys4Jzpdu3P7coa5rw6Hgrp1P4oKhGbCom9Ad05g9ANufbuTq/ZDsDhqEE8/OPRfHLzGZw/KhuLxcNZHwm6/a/ysKrWq1navqjWlqgESOqj7gcqxfzwOqiyB1pf/Tl0lrgIr6zcrz4bThuYSphrscKEIEgvLw2CyuXgrLhdU6Iyzzyh6y5F1IJkpjsQ6eVNDerzEAJ/8cTXPCnA5SlJLw8c4+/Y3OCc0XblmtkZLEtouzkJutuhBWN6OUBevro9EOCgu7HOWYHbXRE1g2u/7mDnr6ISOfZ9EgoXIrqgf1oc9100gpMjVBGha398EZecnIvV02DbIEG3/xmp5ZkjITK+86831nUHqpja1nfVrWZRbX7enqdOnEW3tGq/OqlssZ4bnGu6g2GmO9AzpLGp9v8PNs9nMY/tVZl11ghIG+rb8XkqEOnl5QVqv4XHONP0uyufBN1xXd+W6JzwaIiwH7vd/S1d61YJv5Cguz1GWmQw9Oh25Qi6A1xMrXAj2BpVgGrMarkTSmuY/RV0h9I+6aIfj0gkq1lVyw3L9nKmRIJu/+tsf+4TOdZ1ByDo1nXY9p66P30xRKeocXz1Z/+PRfhcxfFGNheqk/spJwbdjvTyQ+5TLP2h1FjTHeCg22J1ppx6uq7bSC3POAms4b4ZV2cFIr3ctc96d58VNDO93OjTHSHp5QERZ/Rdd1NMzejfLUXU/EaC7vbUBmF6OUCuPZW7bI95vRS94UgtH9/+QciY6S7cBM2Nvh9XV/jryl+otVLrCiPoSsjx/gJWVIK6laDbfzrbn/tEmUbbsAD06i7ZrmpIWCNgzE/hhw+px796yJkqK7qNb3YdxabDoPQ4spOiW37TKKTWWOv/wlugMsKCoV2YIb6T67qNoDtYUsvBmV7uz79nsCwR8AdT13Tb+3RLenlgtPe3lHZhfidBd3tqgrCQGqirvEaaVyBTzA9+q25z21jPbUjpr/peN9UFR/G39virfULWGEBTJ2PVJhzYgpkRdHWmz/OJjJlud+uShPnqq5x/N2+DbmOmu2Sb/y+2bbOnlvc7Q12wOekSGHYB2Jrg7euD/+Kf6JRl9lZhpw9yc6wOj3YuEasIQAXz8v2ArtI8g+HkNq6TFcwdQfcYnwzHK44+3X5c0x0sSwT8wdT0cvtMd7gE3QHhCLrdzHRX+6FwsGhBgu72BOtMN0DeBHUbyBTzjiqXGywWMNKKg31dt5GC4+uTo6gEVdkdun+KuTGzmOmmj7unJL3cvw5+q9YvJvV2zhR2VlIfFWg0N6hK/f601Z5aPux8datp8MOH1QXLwk3w9SP+HY/wGV3X+WqnOnk8Y3Abx+pAtg0zZkh7BUlasjHT7UnQrevBPdN9vNx/SwZ6Qrswg3HOa2b1cpnpDgxH0O1uTbcRdEt6ub9I0N2e2iAuMpBrD7qN2WZ/qy62V/LUnOuT2xMKa5ibGpzpav6YkegpKebGhSFjmYE3JOj2L0d/7oneb8NicbYO8+e67vID9q4AGgz5gfPxuHT4gT3NfNkDgSvwJky1o6iawsp6Iq0ap/RNcf+khFx1G4ig2+hQ0WuQ/3+2O8ZMd7UHQXfFQdU61RIG6cN9O67OMNZ0683O9GVfM3qt94iZbjPXdBtBt6zpDghJLw8qEnS3pbkBrb5K3Q+26uXgLKZ2aG1gUiWN9dxpQ53rbdsTChXMjb7smtU/PQtD4UJEV1UehtKdqmJun0neb8cIuhtrpQK1PxR0cT23wQi6/bmue9v76rb3qa2XiYy4FIaerwpASpp5txAbaeX/pvTnwpGpRIVb3T/JmOkORHr5tg/U7cCp/v/Z7jhmuj1Y023McqcNg/Ao342ps8KjVb0G8E8xtaYG57r8njDTbQRhxjlRV0j18sAyjoFuC6kZyykl6PYXCbrbYKmzrxXyVwDWWb0Gqqu9TXWqiri/OVLLO1jPbTBmdYs2q8IywajGZX2LxQ//NVxnugNVVdfX9n6lbjNHOdfheSPS5cKOrOv2reZG50W1rsx0g7OYmj9nuo2q5UPPb/09I808KkkFFN886r9xCZ/ITY7hthlD+PWZvdt+UqDahh3bD0Wb1EXHQdP9+7Pb0pmZbiPo9rbrhK9omn97dR/bp5bbRMT1jErPRnZnfWXXz9cca7plpjsg2lufLzPdfidBdxssxgd5TIp/ArDOslica6kPBCDF3LVyuScS81TGgK1JBd7ByN8fQBkjVNpe7VHnVfTuZt+X6rbflK5tx2J1Bt6SYu5bhZtUSmBUIqQO6dq2Mvzcq7umFPZ/o+4PcxN0g5rp+8GD6v7SP0HRFv+MTQSOo23YQf/+3O0fqtveE4On9Wh8lrrtzEx3MBVRM/izV7ejXVi/4FiX72tRiWCxt4er7WIxNVnTHVhtFVLTbS7deiTo9pcgjCaDg6XO3qM7GIuoGQJVTM3W7FyH3FERNYOmBX86tb/ahRnCo5zpt911XfdeI+g+o+vbcqzrLu/6tkTbjM+TvFO7fsExYzigqQO+u/Q2s+34UJ1MZIyE5L5tP2/kj9R6b1sjvHUdNDf5fmwB9OSTT9K3b1+ioqLIz89n9eq2u1688MILaJrW4l9UVMvUYl3Xueuuu8jKyiI6Oppp06axc6efi+V1hhF0+zu9fLt9qYNrbYFAc20Z1lGGlbEePZiKqBn82au7JxVRA3W+FmtSMbVGCboDyiiSduLf8fgxVRMBgjvO6WYk6G6D5bg96A7GImoGY123v9uGHd0BDVWqBUT6MM9fZ6zrDtYAMxCpNsaFiENr/fcz/eXYPlVszxLW9bXBIMXU/KVghbo1428WEevsa+uPdd0nVi1vi6bB+Y+o99SRDbD8MZ8PLVBef/11FixYwN133826desYPXo006dPp7i47YsgCQkJHDlyxPFv//79Lb7/wAMP8Pjjj/PUU0+xatUqYmNjmT59OnV1Qbp0yDW93F9LeY4fg332rIuhQRR0GyfhtkaoLWv7eVWFKjDXXAoiBhN/9uruSe3CDGa1DZOZ7sAy/o51FS3r4Rjnu1FJEBbh92H1VBJ0t8E50x0kKWHuZJ+sDoiVB/17Bd9ILc8eq9J+PWWsYQ7WYmqBCLqDfZ90hbGeO2ccRJpQREWCbt/TdfOKqBn8ta67vhp2f67uu1vPfaL4TDjvAXV/6R+heKvvxhZADz/8MHPnzmXOnDkMHz6cp556ipiYGJ577rk2X6NpGpmZmY5/GRkZju/pus6jjz7K7373O2bOnMmoUaP45z//yeHDh3nrrbf88Bt5Id5eSK3puP96O+/8VM0kpQ1zXngKBmERzvOa9tZ1G6nlqYODM2DyZ69uo3J5T5nphvZbTXWGrOkOrOhkNfEBLWe7jR7dPaFGQRCRoLsNjpnuYA66I+PUumCAg36c7TbalOV6uJ7bYMx0H92uTpCDjb/Ty0EFpACHN4DN5r+f6w/77EF339PN2Z4E3b53bK+a3bJGeNYK0BP+Wte96zNorldp5Z7OzI2aBYNnqF7ib13f7dLMGxoaWLt2LdOmTXM8ZrFYmDZtGitWrGjzddXV1fTp04e8vDxmzpzJ5s3OOhx79+6lsLCwxTYTExPJz89vd5sBFR7lTKH0V9swo4p+MM1yG4xiau316g7m9dzQsle3r5X2oHZhBrPahhkt3aR6eWBomvu/Za0UUQuEsEAPIFg5qpcHc3o5qBTzwo0qxfyki/3zM41U6M4G3fGZasah6rAac1daSPlCIGa6U4eoK8ANVaq1VloXC1cFC113Wc/dxSJqBimk5nsFLj3VzWoR5K+ZbkeQc77nxY40Dc5/FP6ar2pNrHgCTrvZZ0P0t6NHj9Lc3NxiphogIyODbdu2uX3NkCFDeO655xg1ahQVFRU89NBDTJo0ic2bN5Obm0thYaFjGydu0/jeierr66mvr3d8XVmpOhDYbDZsJlxstNls6Lre7ra0hGy02qPYyg9Auo/TpZvq0XZ9hgbYBs3w2wVVT/YDgBaXgVa8GVvlkTbHph1er8afOSooLwhrUUlogH78GPoJ4/N0P3ikqQ6t4oDaF0l9g3JftMfbfaHFpKr9W13cav92ajuNtWrfhUUFdN+Z+p4IMVpsKlrVEWxVRdjS1X7Q7YUU9ZjULv19Q5XZ7wdPtyNBdxscQXewFxjImwDfPuO/dd311VBsr/braeVyVzknw7bDal23BN1gDVNFagpWqH3SXYLu0t1QdUTNmBoF/7rKMdMtLcN8xsz13AYjG+foDmiqh7BI87ZtaGqAHR+r+8Mu6NxrE7Jgxh9VQbUv7ofB50H6UPPHGCImTpzIxInOVnGTJk1i2LBh/P3vf+fee+/1apuLFy/mnnvuafV4SUmJKevAbTYbFRUV6LqOpY3if0lRqUQBVYe2czzJpCyONkQUfElKQzXNMWmUhOdCO+vnzeTJfgBIDEskGqgp3E1NG2NLO7QeK3AsqjeNfhp/Z8Q0WUkA6soLqThhfJ7uB09Yy3aRho4tPJbiGh1qg29ftMfbfRGrRxEP1JUebLV/PdbcSGazWkdcUnkcvT5w+87M90SoSQ5PJBKoLNxDbfxIKioqiCvZTzxQa4mjKgj/f/ua2e+Hqqoqj54nQXcbnIXUgji9HJwBzZHvoPE4hEf79ucdXq+qAyfkqpPVzsoeo/roBuMa5kC1T8g+WQU7h9fBmJ/492f7yt5l6jYv37z3pKSX+56xnjvPxKA7MVf97eoqoGSbbyoh7/sK6itUkahcLy7yjP4JbH4Tdn4Cb18PV32iLoiFuNTUVKxWK0VFLdtDFRUVkZmZ6dE2wsPDGTt2LLt27QJwvK6oqIisLOcxoKioiDFjxrjdxsKFC1mwYIHj68rKSvLy8khLSyMhIaEzv5JbNpsNTdNIS0tr8wRKS+0H+yBBryQ+3bfrGLVvlwNgGfoD0jM8289m8GQ/AGhpfWAHxFFNrLt9UXMUS/URAJKHne7MMgomaXkAROl1RJ7wO3i6HzxyTNWw0VIHkn5Cdkco8HpfpPcFIMpW1Wr/eszlWJ2W3UddhA8QU98TIUZLzoEDkGCtJy49HU3TiOU4ANGpvYn28edhMDL7/XBih4+2hP5ZhY+ERMswgKQ+EJeh1mEe3gB9Jnb4ki45ZC+iljvOu9cHa9swXXeZ6fbz39wophasVd29YXZqOUjQ7Wu1ZareAjg7I5hB09S67v1fQ9Fm3wTd2+xVy4f+wLs2Z5oGFzwGT56qls+sfBIm32TuGAMgIiKCcePGsWTJEi666CJAnWwsWbKE+fPne7SN5uZmNm3axA9+oNYm9+vXj8zMTJYsWeIIsisrK1m1ahXXXXed221ERkYSGdk6w8FisZh2AqxpWvvbS8xVz6s8jObLk26bDXZ8pH7W0PN9+7Pc6HA/gKNXt1Zd5H58RfZOA70GYjEKlgWbGNUyTKsrd/s7eLQfPGEvoqalDPD739IsXu0Le4EtrbbU+9+70V5EzRKOxazlSl1g2nsi1Nj/lpaaErBYVCtI+/muJT69661BQ5SZ7wdPt9Ez97QHQqJlGKiTRWO22x/F1IzK5d6kloOzmFrZHv9VkfVEQ42qbAsBmOm275PCjS1bOoQqmw32fa3um1VEDSTo9jWjP3fqEPMzfIx13b4opmazwbYP1P2hnUwtd5WQDTPuV/c//wOU7Oj62ILAggULeOaZZ3jxxRfZunUr1113HTU1NcyZMweAK664goULFzqev2jRIj755BP27NnDunXr+NnPfsb+/fu55pprAHWi8qtf/Yr77ruPd955h02bNnHFFVeQnZ3tCOyDkj3o9nkhtSMb1NKa8FhzLzqaKd4opFbk/vvB3J/b4K8+3Ubl8p5URA3MqV5uBN0RUrk8oNz9LQOV2dnDyUy3O7ZmNKP3YzBXLzfkToCt7/p+XbeuO4Pu3FO820ZMiqoufGyfmpkfcJZJg+siY5Y7PMb/7VFS+qtKrHXlULzZGYSHqpKtUHtU7cscLzMi3JGg27d8sZ7bYKzrLvJBr+5Da1Tro8iErgc5Y2arNPNdn8EHv4Yr3vG8KFuQmjVrFiUlJdx1110UFhYyZswYPvroI0chtIKCghZX6Y8dO8bcuXMpLCwkOTmZcePGsXz5coYPH+54zq233kpNTQ3XXnst5eXlnHbaaXz00Ucep9gFRIK9bZivg+7t9gtAA6eaV4zQbEbQ3VbLMEfl8iAOuh3Vy3188d7o0d2T2oWBS5/uEnXu583noFQuDw6OoNtl7bZxX4Juv5Kg2526cjR0dT8Ugm4jFfTAKu8/HD1ReUgdpDVr1w7G2WPtQff6IAq6A9AuzKBpap/s+UKlmId60G305+59quoJaxYJun2nvgq2vK3u+yLodp3pNvszauu76nbQuV1/v2ka/PBheHKCWiKx+U0YcUnXxxhg8+fPbzOdfOnSpS2+fuSRR3jkkUfa3Z6maSxatIhFixaZNUTfS8hRt5WHfXucdGRd/NA32zdDnH1tclWR+30RCkG3kfZeV6GyXXyVImu0CwumXuv+YJwLNdWp4DkyvvPbkB7dwSHWvmbbtWWYzHQHhKSXu2N/M+pRiWAND/BgPJA1WhWoqClRwayvGLPcGSd1LV0oGNd1B6JyuStHv+4g2ife8sV6bpCg21d0Hd67WX12xGfDEB/0FU4bpi7WHS9Tqbdm0XXneu5h55uzzeQ+cJq96NfHv1UdG0ToM2a6m+pU/QJfOLZPZStpVnURKFgZM91Nx6H+hG4Qx485zyMyR/l1WJ1izHSjt/4dzNJ4HCoPqvs9Lb08ItYZLHvbq7uhxrktETiOrAUV22iNtWiN9r+NBN1+JUG3O7X2K0DBXkTNEB7lvCLtyxTzg9+qW29Tyw3GTO7hDV3bjpkCHnQbxdSCsKp7Z9iaVcEsgL4SdIeEdS/Cpv+oQOGy55wzSGYKj4LUQeq+meu6i7eqNZfWSBg4zbztTr5RFamsOgxfPWTedkXghEU6P999lWJuzHL3maSWUgWr8GiItH+enriuu9C+BCSpT5D/DlEQZu+MYSwHNFvZXnUbmRgaWY9mOyFY67RGCbqDQpzLTLduw3K8VH0dFuVdBoPwmgTd7tTa35Ch9CHrmmLuK4fWqttcL4uoGbJGAxpUHIBqL6+gmi1QlcsNxux/yVbn1eFQVLhRBcWRCeanJhpBd2MNNDeau+2eqnATfHCruj/1Tt92P/DFum5jlrv/meaePIRHw3l/UveX/wWO7jRv2yJwfL2u21jP7YtsEbPFGynmJ2SeGBfDgzm13GBcIPTVum5jPXev/iFf28ErXS2mJjPdwcGYQLQ1QV2FS6HotJ75vg4gCbrdMT5gQiro9nEF8+ZG58HY28rlhqgE56xXsPTrDvT6loQs1cZFt8GRjYEZgxmM1PI+k8zvc+zaK7bOR+mEPUl9FfznF9Bcr1JhJ/m4RZYvKpgb67nNSi13NeQ8GDQdbI3w4a0qlV2EtgR7BfOKg+Zvu7YM9qv+3AwNgaDbWNddfcJMdyis5zY4iqmV+2b7RuXynlZEzeAIur1NL5c13UEhLMI5aVFdjOV4AGsY9XASdLtjzHSH0hsy1x50F21WJ9NmK96i1n9FJUKvgV3fXrCt6w50ejkE3z7xhlFEzRetcqxhziqo9ZJi3iW6Du/+Ckp3qQJTFz3l+16dGSPVbZFJQfex/SqzQrP4bmZxxmJVL2P3585ZdRG6El2KqZlt5yegN0P6SapDR7BztA07oYK5I+ge49fheMVoG+ar9PJSY6a7hwbdMS4VzL0h1cuDh6OY2lGXme70wI2nh5Kg2w0tFNPLE7IgsbeaKTXSwM10yOjPPc6ck3PHuu5gmekOgqA7x75PfPH384fmRudMj6/608q6bnOsfQG+f8O+jvt58/tyu2PMdJfuUgWKumrb++q290TfXSDtNQAm2zMAPlronLkRocmX6eXG+zEUZrnBpW2Yy0x3fZX6/wmhMdPtSC8v9832y3po5XJDl9d0S5/uoGGc29aWONd0SxE1v5Og2w09No3GXkPQQ+FqtSsjxfzAt6ZvWjMCwa6mlhschcPWBUfaZiBbhhmyXfZJKDq8Xq23jk5Rsz2+IEF31x3ZCB/epu5Puxt65/vn58ZlqAuZuk1lznSVMfM81Aep5a5OWwCJeaoGxdcP+/ZnCd8y0svNnulurINdS9T9UFjPDRDnZqa7cBOgq+yXuBA4Ifd1r+7SHtqj29Dl9HJZ0x00jP/P1cVYjGLRofB/vJuRoNud039N6Y/egZOvDPRIOscRdPugmJrRLqyrlcsNGSPULFtNsW9S/TorGGa6jdn/Y3t919LGl/YuU7d9T/NdqrIE3V1TV+myjns6TLzBfz9b05zF1Lq6rrvmKBSsUPd93Q85Igam36/uf/OY80RchB5jptvsNd17v1QXHOOznZ/jwc7dTHcorecGl17d5eZvu6FWdS+Anpte7pgd7WIhtXAJugPO/rfUao5iqStr8ZjwH6/OjJ988kn69u1LVFQU+fn5rF7dfvGu8vJy5s2bR1ZWFpGRkQwePJgPPvjA8f3m5mbuvPNO+vXrR3R0NAMGDODee+9FD4YZ0FDiWkzNZjNts1p9BVqpvXqv0U+6qyJiIH24uh/oNcw2m/OgEsgPoZgUZxrbmmdDr0K3L9dzGyTo9p6uw7s3qYq8CblwsR/WcZ8o06R13ds/UDPmmaNUX21fG3YBDDgbmhtUmrkITa5rus08v9huTy0fcl7oVAM2Cqm5znSHXNBtX9Pti/RyI7U8Kim4W6f5UlfTy2WmO3g4shZcZrol6Pa7Tp9xvf766yxYsIC7776bdevWMXr0aKZPn05xcbHb5zc0NHDOOeewb98+3njjDbZv384zzzxDTk6O4zl/+tOf+Nvf/sZf/vIXtm7dyp/+9CceeOABnnjiCe9/s54oY4SqEllXAaXmtbgJL7a3+EnuZ+7az+wx6jbQ67qPH1Mn8BD4dfxGauLn98ETJ8O6f4ZG8N1Y58ywkKA7OK15Djb/Dyxh8KPnA3MiadZM91Z7avmwC7q2HU9pGpz3AFjCYefHsP1D//xcYa54+0x3c72zYGpX2WzO90OopJZD95jp9mV6uRF099RZbuh6erms6Q4eLu3fWrQME37V6aD74YcfZu7cucyZM4fhw4fz1FNPERMTw3PPPef2+c899xxlZWW89dZbTJ48mb59+3LGGWcwerTzQ3358uXMnDmTH/7wh/Tt25fLLruMc889t8MZdHECa7hzXbCJKebhRRvUHbNSyw05QbKG2TigRCerfRhIU+9Sqayx6VBeAO/cAE+Mg3X/Cu7g++C30FSnZk9SB/vu50jQ7Z0j3zlnaKf93pkV429GMbWizd7PNNZXwZ4v1H1fr+d2lToIJs5T9z+8zZxicMK/wiKcFXvNSjE/vE4FrhHx0O90c7bpD8ZMd32lmpFsqIWSbeqxUKhcDr5NLy/r4eu5oeVMtzfZk1K9PHi4XECRQmqB06mgu6GhgbVr1zJt2jTnBiwWpk2bxooVK9y+5p133mHixInMmzePjIwMRowYwf33309zc7PjOZMmTWLJkiXs2LEDgO+++46vv/6a8847z5vfqWfzwbru8GJ73+hck4qoGVwrmAdyKUEwrOc2hEWqE/ubvoNz/6DGVL4f3pkPfxkP618KzuB7nz21vO/pvk2vlKC781zXcQ8+DybOD9xYUoeo2eL6CnVRyRs7P1Vp3in9IX2YuePryJRbVJGp8v3wzeP+/dnCHGa3DTOqlg+cqj6/Q0VkvLN/clWh/UKYTV2UMGbBg50v+3Q7iqj10Mrl4GwZpjd7d2FD+nQHjzj7xcbqQix1x1o+JvwmrDNPPnr0KM3NzWRkZLR4PCMjg23btrl9zZ49e/j888+ZPXs2H3zwAbt27eL666+nsbGRu+++G4Dbb7+dyspKhg4ditVqpbm5mT/84Q/Mnj27zbHU19dTX1/v+LqyshIAm82GrYvrmW02G7qud3k7AZF7ChZAP/AtugnjtzU3E1GsUs5s2SebulactGFo1gi0unJspXsgpZ952+6M6mK1z2LT2txnfn9PhEXBqdfDuF/AmufQlj+OdmwfvD0P/cuH0E//DYz6sUoV9qO29oO2ZxkaYOt7urnvkRNFJqi/1fFyU97f3gqZzwhdR3vnRrSyPeiJuegzn1QXuEy6yNXp/WAJQ0sbjFa0GVvhJlUVvJO0re+iAfqQH6q6H/68YBceA+fci+W/V6F//TD6yB871pSb/Z4I+vdWqErIURd6zWobtt1en8bXBf3MpmlqtvvYXjVTX7RZPZ41OnTWpfuyT7ekl6vMkKhEdZG75mjnlyTJmu7gYRRSs1/s1tHQontorYIA8vkZu81mIz09naeffhqr1cq4ceM4dOgQDz74oCPo/ve//83LL7/MK6+8wkknncSGDRv41a9+RXZ2Nlde6b6C+OLFi7nnnntaPV5SUkJdXV2Xx1xRUYGu61j8XWioi7TIvmQA2tHtFBfsQDeuBHu7vWN7yagrR7dGUGzJgDbW7nsrJWUIESWbqNy2jLqBgflgjinaSwJQZ42noo3fL6DviQE/Rut9PtFbXiN2/TNYj+1Fe2ceTUv/RPW466kbdIHfgm93+0FrrCXd3lKuNH44zSa/R1xFN2gkAvWVJZT78Od0xJv3g9ZQTcShFdT3PgOsET4eoRL9/cskbnkT3RJG2dl/prGqEarM22/e7IfExIFEF22mZvdKapI7mT3T3ED6jo/RgLKMyTQG4j2QOonknFOJPLSS+nd/TfmMvwLmf0ZUVVV1eRvCjQRjptuEoLt0t0rJ1qww6Jyub8/f4jNV0F1VGHrrucG3fbp7erswQ2yaPegugbROLh1rlKA7aJzYDjcmBaz+nbQRnQy6U1NTsVqtFBUVtXi8qKiIzEz36UhZWVmEh4djtVodjw0bNozCwkIaGhqIiIjglltu4fbbb+fyyy8HYOTIkezfv5/Fixe3GXQvXLiQBQsWOL6urKwkLy+PtLQ0EhISOvNrtWKz2dA0jbS0tJALuiEdvddAtNJdpNXvg97ndmlr+hH72snMUaRn5XZ9eCfQ+kyAkk0kVu8mIT0wqS6aptZmRvXKJbKNMQTFeyLndjjjBmz2me+wygKSvrgdfcPf0afcAiN/5PPg2+1+2P05mq0RPTGXXgPH+XaW5Kh6D0bajpMeoPcLePF+KPoe7a0r1YzzyVein/+oz8fIke/QVvwRAH3aPSSP7NpngTte/b/oMx52vE1cwWfEDjkT+p8Jmoev3fkplsYa9LhMkkee4/nrzHbhI+h/P52ofUtIr/gOBp1j+mdEVFSUCQMVrTjahpkQdBsF1PpOds66hhLXYmpHNqj7RoHTUGBMKtRXgq0ZLNZ2n+6x+mqotld179WD08tBBd2lu7wrpiYz3cEjMgGskWqZGQTHcsoeqFNn6BEREYwbN44lS5Zw0UUXAeqka8mSJcyf736d4OTJk3nllVew2WyOE5EdO3aQlZVFRISa7amtrW11kmK1WttNr4uMjCQysvX6KYvFYsoJj6Zppm3L7/LyoXQXlkNrYMiMLm1KP2IvcpY73jf7IudkWPMs2pENaIHa1/b2CVpsertjCIr3RFQ8nHYTTLgGvv0HfPMY2rG9aG9fD2ufh0v/Acl9fTqEVvth/9fq8b5T0KwmnfS0xT6zodVXBe79Yufx+2HDq/DezdCkLu5oG19Hm/Z731YPr6uAN+aotc9DfoBl4jyfXQzp9P+LvqcBGlrxVrSXL4XE3jD2ZzB2NiR2cGHP3ppJG/oDtEBepc8YDvm/hBV/wfLx7TDgTLCEm/oZEZLHnlBgvMfMmOk2UsuHhFhquSHOHnSXF0DxVnU/FGe6QX3mmfWZemyvffspoXkxxUxGRxevgm5Z0x00NE0F2pX2ApISdAdEp4/qCxYs4JlnnuHFF19k69atXHfdddTU1DBnzhwArrjiChYudPYxve666ygrK+Omm25ix44dvP/++9x///3MmzfP8ZwLLriAP/zhD7z//vvs27ePN998k4cffpiLL77YhF+xBzKzmNrBNQDoOSYXUTMY1daPbFBXqgPB6EF5YvpNMIuIhck3wU0b4ZxF6irmwW/hqdNh0xv+HcveL9WtL1uFGUKpkFpTvQq23/qlCrgHToP0k1SV9/Uv+fZnv3uTOnFM7A0X/TW41mjmnAy//BomXKv+nhUFsPR+eGQEvHQpbH4Lmhpav87W7LJ+1o9Vy9tyxm1qTWzZHlgu7S1Dhlnp5TWlUGAvIDs0hFqFuYq31+fZ/QXYmlSA6UWdhYCxhjsrY5vZNsxILe/J67kNLq2mOsXW7LjQLNXLg0ScS6AtQXdAdDronjVrFg899BB33XUXY8aMYcOGDXz00UeO4moFBQUcOXLE8fy8vDw+/vhjvv32W0aNGsWNN97ITTfdxO233+54zhNPPMFll13G9ddfz7Bhw/jNb37D//3f/3Hvvfea8Cv2QLn2oPvgWmhu8n47jXVQaO/R7augO3WwugraUK1SmAIhmKqXd1ZknAq+f/m1ynCor4T/Xg1vXa9S5HytrsLZZ90f7XJCJeguPwDPzVC9sdHgzIXw0//Aqb9U3//2H767yHTgW9j8pr0f9wvBOVOTOQJ+8CD8ejtc8oyqeo8Ouz6D/1wJDw+Dj38LJdudrzmwWv1fjUy0Pz/AohLg3PvU/S8fgooDgR2P8IyRXl55uGtFH3d+rKp9Z4yEpN7mjM3fjJnuEpdZ7mC6QOcJX1QwL5PK5Q7GeVFtJ4Nuo0c3SJ/uYBErQXegeZWfN3/+/DbTyZcuXdrqsYkTJ7Jy5co2txcfH8+jjz7Ko48+6s1wxInShqqZz/pKKN4CWaO8207hRjRbI81RKWi+OqmwhqkDfcEK1a87bYhvfk57QjnoNiT3gV98AF8+AF8+CBtehoKVKt3c6IfuC/tXqBPPlP4dpwabwTjBaqhSF5SCsRDIriXw32vgeJkKeC/5Bwyyt1kccRl8cqdqObXz0y4v/3Br+WPqdtTlkDvO/O2bKTxaVeEf9WM1Y7z+JVj/slpPueIv6l9ePoz9ufPizuDpqqpuMBj5I1jzPBQsR/vkTjjjgUCPSHQkPgvQ1NKL2tKWsz+dYbQKC9VZbnDOdBtCKbXcEJ2kUmbrzJzptlcu7+lF1KBFf+dOMdZzaxbVjUUEnss5rh6bSohdXusWZNFYd2SxQO4p6r63KeY2G3yuZnEas8b79uq3o1/3Ot/9jPY40stDOOgGFYCedQdc+Z5KoSzbDc+eC9885rs2Xv5MLQc1u2ior/TPz/SUzQbLHlAp0sfLIGsMXLvMGXCDuuJ/8s/V/dVPmz+Go7tg63vq/qQbzN++L6X0h6l3wc2b4Sevq3WymlV9hr0zH9Y8q543LAhSyw2apmbsNSva1reJOLg80CMSHQmLcPanNdY3dlbjcdj9ubo/JISD7rgTCuBmjQnIMLrEyOTxxUy3pJc7l911Nr3cCLrDY0Mve6K7ajHTLT26A0GC7u7Ksa57tXevX/4Y7F2GHh5D1YRfmTYst4x13cZMlj811jmDt1Ba092evpNVuvmwC8DWCJ/eBS9dotrCmG2fPej2V7qvNVwdxCG4Usxry+DVWfDFHwBd9Ve/6mNHD+cWxl8NaLB7iQqSzbTiCfXzB58H6UPN3ba/WMNUBsBPXoEFW2Da751pntHJam18MMkcARPmApDw9SJobgzwgESHHOu6D3v3+j3LVPpsQm5ozg4b4k8MukPwd3EsOSo3b5ulkl7u4Ai6vZzplsrlwaNF0N1NzndDjATd3ZURdB/0Iug+uNYxy63P+BPNyT6+2mvMdBdu8v8Jq7FOyRLuPHh3BzEp8ON/wQWPQVg07PkC/jYZdnxs3s+oLXOu+ffXTDcE37ruwxvg6TNg5ycqjW7mX9V+D28jpS6ln0qRBrW22yxVRapSOqh1/t1BfCacdjPcsA6u+Rzmfh6cJ3FnLkTPPpmqCQt83rZPmKCrbcPsVfQZcl5oz+JFJ4PVvlQjMgGS+wV2PN5w9Oo2Kb28vgpqitV9men2Pr3cWNMt67mDR5zL7LbMdAeEBN3dVc54QINj+9TJuKfqKuG/V6lKpiddDGNm+2qETin9VSDVVAd7l/n+57lyXc8dyidP7miamnH9v2Wq2E/tUXjlx/DhbWqGv6v2qVZhpA1t+WHua8EUdK//l0rhLy9Qrdqu/lS1vuqIfWaUDS+bV/Bu1VOqB2dePvSZaM42g4WmqfXpwTrzFJ2EfvVn1Pc/t/t9jnRHjrZhXqSX22yw/SN1P5TXc4N6r8bZ13VnjlJL00KN2YXUyuzruWNSu9eFeG8ZQffxY52bFGmwH9eC8SJpT+U6ux3qyylDVAh+wgqPRCVA+nB139PZbl2H9xeoQD2xN5z/qH9OIC0WVfQJ4PM/qHH4Syi2C+ustCFwzWeqrzCo4Owf01pWhvaGv9dzG4Ih6G48TsLSO7C8e6MKdAefp9Zve1q0sP/ZqkhPfSVsfL3r46mvgm/ta567yyx3qJFgO3R0Jb380Bo1ExqZAH1OM3dcgWCkmGePCegwvGas6TYrvVxSy1uKTlbF0EBlt3nK0aNbgu6gIenlASdBd3fW2XXd370Gm/6jihdd+g9n2pY/TPmN+nA+vA62vuu/n9sdKpd7IjwKzvsT/PTf6gp+0Sb4+xld6xe918/ruQ1GMbVABN1le+Gz36M9PpqYbf9F1yxw9p1w+Sud+/9isThnu1c/0/ULTWtfhPoK6DVIXQAQQrStK+nlRtXyQecETxX9rsg4Sd0GQxs+bzjSy8vN2Z4UUWvJYoWYXup+Z1LMZU138Enqgx4eS1N8rvxdAkSC7u4sL1/dehJ0l+6GD36j7p+5EHrn+25c7sSlw8Tr1f3P7+1af/HO6ClBt2HwdLjuG+h/FjQdh7fnwad3d766eXURHN0OaNDXz7M9/p7pbm5UF4L+dTE8Pga+fgStpoTm2Az02f9VF4y8Scsc/RN1oalkqzNV3xtNDbDyr+r+5BtDM0VUCH9ypJd7EXRv/0DdhnLVclcz/gjXLnXWmQg1ZqeXS7uw1mK8KKbWaATdsqY7aEQloP/ya8oufi3QI+mx5OysOzNmug+vVyfmbWlqgDeuUmtw+pwGpy/wz/hONOkGlcp0dAds9NOHgiPo7kGpNvGZ8LP/wRm3q6+/eVSt4+/MOu99X6nbzJGqaJs/GUG3r1uGlR9Qyx0eGQGv/8zZImjA2dh+/C9KfroE+p/p/fajk2D0LHW/K+3Dvn9DBQ9xmTBqlvfbEaKnMGa6Kw937oLj0V3q+GQJC74q+t4Kj1bFTEN1eYQx021WerljplvSyx28aRvmmOmOM388wnvJfbHF9JBJpiAkZVa7s5T+Ki2othQKN0LuePfP+3wRHNmgAt5LnlbpRIEQlQinLYBP74QvFsOIy9quAG2W7tKju7MsFjhroSr+9c4NsPlNdQJ6+SseXYDQjJlZf6/nBt/OdNuaYeensPZ5VY1ct5+Qx6Sq/tonX6mqj9tsUFzc9Z93ylxY85xKWa046JyB83i8NvjmcXX/1OsgLLLrYxKiu4vPAjTVUvHly1QrQk8Ya8D7nubf5VeibWb36S6Tme5WvKlgbqzpljRmIRwk6O7ONE2lmG//AA6sch907/oMlj+h7s98EhJz/DvGE02YCyv/pqrKrnnOmXLuKz0tvfxEY36iAr3XZ6v3yD+mwew3IHVg+68LVBE18E3QXXlEVSJf90+oOOB8vO/pMP4qGHq+b9ZvZgxX2SX7v4Y1z8PUOzv3+l2fqvT0iHgYP8f88QnRHVnDIXWwWiKze0nnXz/8ItOHJLzkSC83oWVYXaXznEDWdDsZ50e1nZnptlcvD5f0ciEMEnR3d7mnOIPuifNafq+6GN68Tt0/5RoY+kP/j+9E4dFw5m3w7k3w1UNqdjEy3nc/r6cH3QD9Tletrl6+DI7thWenqRnvPpPcPt1SdRjt2F5VcK93AFpTmR10f3ibvZhZs/o6Olm1yhs3p+OLD2aYMFcF3WtfgDNu7dxs9TePqdvxc6S9jRCdMfvfsPerzr8uKjE4jpVCMWa6G2tU/Q1PsxbcMVLLY9N9e94RaryZ6Xb06Zb0ciEMEnR3d67F1HTduW7LZoO3rlOtT9KHw7n3BW6MJxrzMzX7XroLVjwJZ97uu5/VE1qGeSJtCFyzBF69HA6thX/OhJl/hVE/avXUiMOr1J3ssc5K4v5kZtBdX6VaqIG6gDD+Khh2oe+XNbga+kOIz4aqw7D5Lec6744c+Bb2fwOWcJVaLoTwXHJf9U+ENteLjcfLIa4LF9ClXZh7sUb1cm/WdMtMtxAGKaTW3WWPVUVfqo6oNaOGlX9VqeVhUXDZc2qGOVhYw+Cs36r7y5/o3Ad9Z+i6zHS7ikuHK9+DYRdAcwP87xpY9mCrdlaRh1aqO/0C1GLGzKD72H51G50CV30Eo37s34Ab1MzM+KvU/W+f8fx13zyqbkfNchaGEkKInsRihUjjmFDetW0Z67kltbwlr9Z0S8swIU4kQXd3FxGjKkyDSjEHVc38s9+r+9Pvh/RhARlau4ZfBFmj1bqgrx72zc+or1TBJchMtyEiBn70T5g4X339xX3w9nyVtgeg686Z7kCs5waTg+696jbQM17jrlQz1ge/hUPrOn7+0Z3OfsGTb/Tt2ETIe/LJJ+nbty9RUVHk5+ezerUHbSSB1157DU3TuOiii1o8/otf/AJN01r8mzFjhg9GLoQHou3HhK6u65aZbve6EnSHS9AthEGC7p7ASDE/+C3UV8MbV6uqrUPPd86wBRuLBabepe5/+4xq32Q2YwY9Ij64ZvoDzWKB6X+AH/4ZNAtseAleulSl7h3bi7X6CLolHPJODcz4jMI5pgTd+9RtSr+ub6sr4tLhpIvV/W//0fHzlz8B6KpXcNoQnw5NhLbXX3+dBQsWcPfdd7Nu3TpGjx7N9OnTKe6g+v6+ffv4zW9+w+mnu89omTFjBkeOHHH8e/XVV30xfCE6Zlavbpnpds8RdJd6/hqZ6RaiFQm6ewKjX/eBVfDhrapYSEIOXPhEcPfmHDBVVY9uboBlfzR/+z2xR3dnnHIN/OR1daV67zJ4bjrahlfU93LHB26tlmufbltz17ZVFiQz3QATrlW3m95o/+Smqgi+swc4k2/y/bhESHv44YeZO3cuc+bMYfjw4Tz11FPExMTw3HPPtfma5uZmZs+ezT333EP//u5n/SIjI8nMzHT8S05O9tWvIET7zOrVbRRSk3ZhLRnnSA1V0Hjcs9c4CqnJmm4hDFJIrSfItQfdh9erf5oFLnkGYlICO66OaBpMvVtV097wCky6CdIGm7d9Wc/dscHnwlUfwiuzoGQbWsk29XjfAKWWA0S6FG+rr3RWr/WGMdOdHOCZblAXMrLGwJENsP6fcNrN7p+36m/qQlRePvQOULaBCAkNDQ2sXbuWhQsXOh6zWCxMmzaNFStWtPm6RYsWkZ6eztVXX81XX7mv8L106VLS09NJTk7m7LPP5r777qNXr15un1tfX099fb3j68rKSgBsNhs2m82bX60Fm82GruumbCuU9dT9oEUlowG22jKwv6c6vR/qKrDUqoudtuS+qthsN2DKeyI8Ds0SjmZrxFZdDIl5Hb5Ea6hWf5OwmKDYlz31/8aJZD8oZu8HT7cjQXdPkJjrrI4MMOUW6Ds5sGPyVN4pMOSHsP19+PxemPUv87YtQbdnskaryuav/BiKvgdA73s6AcuRCIuAsGhoOq5SzLsUdAfRTLemqdnut6+Hb5+FSTeqIkGu6irhW/sMpcxyiw4cPXqU5uZmMjIyWjyekZHBtm3b3L7m66+/5tlnn2XDhg1tbnfGjBlccskl9OvXj927d3PHHXdw3nnnsWLFCqxWa6vnL168mHvuuafV4yUlJdTV1XXul3LDZrNRUVGBrutYLD03ga+n7ocEIokBao4epKa42Kv9EFa8kVSgOSaNkvJaoNaXQ/Ybs94TadEpWGuKKDuwg6b6jttaptVVYwXKquto6mApiz/01P8bJ5L9oJi9H6qqqjx6ngTdPYGmqRmxzf9T63Cn3BroEXXO2b9Tvca3vqPaWeWMM2e70i7Mc4k5cNVH6O/+ivqqUiKMJQuBEpUI1cdVEOotWzOUF6j7gV7TbRhxCXzyO6g4ADs+at0PeN2LUF8BqYNh8HmBGaPotqqqqvj5z3/OM888Q2pq25+Ll19+ueP+yJEjGTVqFAMGDGDp0qVMnTq11fMXLlzIggULHF9XVlaSl5dHWloaCQldbztos9nQNI20tLQefyLZE/eDlpwJQJy1idj0dO/2Q5EqwmZJHUh6erqvhup3Zr0ntPgMqCkiJbIZPNg/WpNKQ0/J7A3Jgd+fPfX/xolkPyhm74eoKM+63kjQ3VOc9VtI6g2nXq9acoWSjOEw+nK1jnXJIrjibXO2KzPdnRMZj37JM5QXF5NuDQ/sWKISobqwa8XUKg6CrQmsERCfZd7YuiI8Gk6+QrUDW/10y6C7qQFW/FXdn3SjKngnRDtSU1OxWq0UFRW1eLyoqIjMzMxWz9+9ezf79u3jggsucDxmpM2FhYWxfft2Bgxovd61f//+pKamsmvXLrdBd2RkJJGRrWfHLBaLaSd+mqaZur1Q1SP3g32pnFZXgWb/vTu9H0p3qtf1GuDYRndhynvCfp5kOV7W8bHHZnOs6bZExgXNsapH/t9wQ/aDYuZ+8HQbPXuP9ySpA+GceyA+o+PnBqMzF6qWSnuWqn9mkKA7dJnRNsxYz53Up3UadyCNv0rVXdizFEq2Ox/f9B+1RCQuU/UTF6IDERERjBs3jiVLljges9lsLFmyhIkTJ7Z6/tChQ9m0aRMbNmxw/Lvwwgs566yz2LBhA3l57tdyHjx4kNLSUrKyguTilehZHB0tyr3fRsFKdWtWJl1305m2YU3HAV3dl+rlQjhI0C1CQ3IfZ3uzJYtA17u+TSO9PE6C7pBjStAdROu5XSX3caaOG+3DbDZY/ri6f+p1ENbxmjohABYsWMAzzzzDiy++yNatW7nuuuuoqalhzpw5AFxxxRWOQmtRUVGMGDGixb+kpCTi4+MZMWIEERERVFdXc8stt7By5Ur27dvHkiVLmDlzJgMHDmT69OmB/FVFT2VUL/e2T3dTg2qpCtAnROrd+JuxDM+ToLvBZT18mLRjFcIgQbcIHVN+o9pXHVoL297r+vZkpjt0mTnTHWxBN8CEuep2wytq3frOT6Bkm6rcPn5OYMcmQsqsWbN46KGHuOuuuxgzZgwbNmzgo48+chRXKygo4MiRIx5vz2q1snHjRi688EIGDx7M1Vdfzbhx4/jqq6/cppAL4XNd7dN9eD001UFML1UvQ7TmCLqPdvzchmp1Gx4bNKnlQgSDEFvcK3q0uHSYeD18+SAsuReG/KBracESdIcuM4Juo0d3sBRRc9X/TOg1SK0z3Pg6fP8/9fj4Oc7fXQgPzZ8/n/nz57v93tKlS9t97QsvvNDi6+joaD7++GOTRiaECYwOFt6mlxcsV7e9J6rCs6K1zqSXS49uIdySS1AitEy6QR1gj26H717zfjvNTVBbpu5L0B16uvtMt9E+DGDpYnVSaAmH/OsCOy4hhAg2XU0v328PuvtMMmU43VJngu6GGnUr67mFaEGCbhFaohLhNHvrmaWLoaneu+3UlgK6KljVlT7PIjBMXdMdhDPdoCr2R8TZ36vA6FmQIIWqhBCiBSO9vKkOGjvZ993WDAWr1P3erYsLCjtHenlpx881gu5wCbqFcCVBtwg9E+ZCfLbqZbzmOe+2YVytjekVXJWrhWei7L19vQ26jx9zvja5jzljMltUggq8DZNuDNxYhBAiWEUmAPa08M6mmBdvgfoKdYEzc5TZI+s+XGe6OypkKzPdQrglQbcIPeHRcMat6v6XD0F9Vee3Ieu5Q1tXZ7qN1PK4jOA+MZg4T71Hx/0C0oYEejRCCBF8LBaXFPPyzr3WSC3PmwBWKXPUphj7THdzfcfnXLKmWwi3JOgWoWnszyBlANQeheVPdP71RgVOI2VKhJauBt1lQdou7EQp/eGWXXDBY4EeiRBCBC9HBfNOruuW9dyeiYhxpot3tK7bqF4eEefbMQkRYiToFqHJGg5T71L3lz8BlZ63vAFkpjvUGSdY9V2c6Q7W9dxCCCE8Z8x0dya9XNedQXdvCbo75GnbMKNPd7jMdAvhSoJuEbqGz4TcCSqV6Ys/dO61EnSHti6nl4fITLcQQoiOedOru2wP1BSDNQJyxvliVN2Lcb5U21HQLWu6hXBHgm4RujQNptuD7fUvQeH3nr/WEXRLenlIcgTdlWCzdf71xkx3MPboFkII0Tne9Oo2ZrlzxkF4lOlD6nY8bRvWKEG3EO5I0C1CW94EGH4RoMOnd3r+OseabpnpDkmR9url6NDgRSG9sn3qVma6hRAi9HnTq9uRWi6twjwS20vddrimW4JuIdyRoFuEvmm/B0s47P4cdn3m2WskvTy0hUdBmH1morMp5k0NUHlQ3Zc13UIIEfq8SS8vMIqoTTZ7NN2TY6Zb1nQL4Q0JukXoS+kH+f+n7n9yJ9iaO36NBN2hz9t13RUHQLepE4K4dPPHJYQQwr86W0it8rBaZqRZVMac6Jin6eWO6uUy0y2EKwm6Rfdw+q/Vle7iLbDh5Y6fLy3DQp+3QbdrETVNM3VIQgghAsBY0+3pTLeRWp4xAqIS2n+uUDyd6Xb06ZagWwhXEnSL7iEmBc64Vd3//A9QX932cxtqnIU+ZKY7dHkbdIdKj24hhBCe6Wyf7oIV6lZSyz3nccswWdMthDsSdIvu45RrVCBVXQgr/tL284wDRlgURMT5ZWjCB4xiap2e6d6nbmU9txBCdA+dTS83Zrr7SBE1j8UYQbeHhdTCJegWwpUE3aL7CItURdUAvnkMqgrdP8+1crmkF4cur9PL96lbmekWQojuoTPp5bVlaikaQO9JPhtSt+Pap7u9Vp0y0y2EWxJ0i+5l+EWQe4paU/TFH9w/R3p0dw+uvbo7Q3p0CyFE92Kkl9eVg663/9wDq9Rtr0EQJ0vMPBZjbxmm29pP43es6Zbq5UK4kqBbdC+aBufag+31L0HR5tbPkcrl3YM3M926Lmu6hRCiuzHSy5sbnEFfW/Z/o24ltbxzwiKcFzdq21nX7ZjpluV7QriSoFt0P73z1Yy3blMtxE4kQXf34E3QXXPUXkRPg6TePhmWEEIIP4uIA82q7ne0rnu/FFHzWkdtw3TdZU23zHQL4UqCbtE9TbsbLOGwewns+qzl96RdWPfgCLrLPX+NkVqekKNqAAghhAh9mubZuu6GGjiyQd3vLTPdnRbbQTG1pnrQm9V9WdMtRAsSdIvuKaU/TLhW3f/kLrA1O78nM93dgzcz3ccktVwIIbolI8W8vaD74Ldga4KEXMl28kZHbcNcU/sl6BaiBQm6Rfc15TcqMCveDBtecT4uQXf34Cic05mge5+6Telr8mCEEEIElGsxtbY4UssnSvcSb3SUXt5QrW7DosBi9c+YhAgREnSL7ismBabcqu5/fp9znZGkl3cP3sx0SxE1IYTonjzp1W0UUZPUcu90GHTbZ7plPbcQrUjQLbq3CXMhqQ9UF8Lyv6jHZKa7e/AqvXyfuk2WdmFCCNGtdLSmu6kBDq5R96WImnccQXcb6eVSuVyINknQLbq3sEiY9nt1/5vHoPKIs9WFBN2hLSpB3dZXgs3m2Wsca7ol6BZCiG7Fnl6utdVD+sh30HQcolMgbYj/xtWdGL2621zTbQTdMtMtxIkk6Bbd30kXQ854dTD48FZVRAUgRtLLQ5ox063bnOvI2tN4HKqOqPspEnQLIUS30lF6uaM/9yRZz+2tDtPLjaBbiqgJcSIJukX3p2kw/X51f+s76jYqEcIiAjcm0XVhUWC1/w09STE/tl/dRiY40xCFEEJ0Dx0VUiuwF1GT9dze8zToljXdQrQiQbfoGXrnw/CZzq9j0wM3FmEOTXPOdtdXdvx8x3ruvjLLIYQQ3U17a7ptNmfQ3UeCbq8ZQXddOTQ3tv6+rOkWok0SdIueY+rdYAlX92U9d/fQmWJq0qNbCCG6r/b6dBdvUceJ8FjIHO3PUXUv0cmg2UOH2tLW3zf6dMuabiFakaBb9By9Bqhq5gBJeYEdizBHp4LufepW1nMLIUT30156uTHLnTcBrGH+GlH3Y7G4FFNzk2Ju1FeRNd1CtCKfPKJnmXo3pPSHQecGeiTCDJ0JuqVHtxBCdF/tFVJzLaImuiY2TQXcboNuo0+3BN1CnEiCbtGzhEc5Z7tF6PNmplvahQkhRPfjuqZb152P6zrsN9ZzS9DdZbH2zi/u2oZJ9XIh2iTp5UKI0OVp0G2ztSykJoQQonsx+nTrzWhGv2iAsj1QXahquuSMC8zYupP2Kpg3StAtRFsk6BZChC5Pg+7qQmiuB80KibKeX/QsTz75JH379iUqKor8/HxWr17t0etee+01NE3joosuavG4ruvcddddZGVlER0dzbRp09i5c6cPRi5EJ4RHO9pIavUuxwRjPXfOOPUc0TWOoFtmuoXoDK+C7s4ewMvLy5k3bx5ZWVlERkYyePBgPvjggxbPOXToED/72c/o1asX0dHRjBw5kjVr1ngzPCFET+EIusvbf54xy52UJ0V0RI/y+uuvs2DBAu6++27WrVvH6NGjmT59OsXFxe2+bt++ffzmN7/h9NNPb/W9Bx54gMcff5ynnnqKVatWERsby/Tp06mrq/PVryFExzTNkWJucW0juV9ahZkqxkgvb2dNtwTdQrTS6aC7swfwhoYGzjnnHPbt28cbb7zB9u3beeaZZ8jJyXE859ixY0yePJnw8HA+/PBDtmzZwp///GeSk5O9/82EEN1fZIK67Wim21FETdZzi57l4YcfZu7cucyZM4fhw4fz1FNPERMTw3PPPdfma5qbm5k9ezb33HMP/fv3b/E9Xdd59NFH+d3vfsfMmTMZNWoU//znPzl8+DBvvfWWj38bITpgTzG31Jc7HzOKqPWW9dym8GRNd7i0DBPiRJ2e8nE9gAM89dRTvP/++zz33HPcfvvtrZ7/3HPPUVZWxvLlywkPVz2S+/bt2+I5f/rTn8jLy+P55593PNavn5wcCyE64GgR00HQLeu5RQ/U0NDA2rVrWbhwoeMxi8XCtGnTWLFiRZuvW7RoEenp6Vx99dV89dVXLb63d+9eCgsLmTZtmuOxxMRE8vPzWbFiBZdffnmr7dXX11NfX+/4urJSzULabDZsNpvXv5/BZrOh67op2wplsh9Ai1oDVe8AABr3SURBVEpEA7S6CrUfqgqxHNuLjoaee4qq79GD+OQ9EZOKBdBrStBP2K7WWIMG2MJjgmpfy/8NRfaDYvZ+8HQ7nQq6vTmAv/POO0ycOJF58+bx9ttvk5aWxk9/+lNuu+02rFar4znTp0/nRz/6EcuWLSMnJ4frr7+euXOlyrQQoh2eruk+Ju3CRM9z9OhRmpubycjIaPF4RkYG27Ztc/uar7/+mmeffZYNGza4/X5hYaFjGydu0/jeiRYvXsw999zT6vGSkhJTUtJtNhsVFRXouo7F0nNL1ch+gCRLDFFA3bFCjhcXE7PnI5KApl5DKa2sh8r2l1V0N754T4Q3WOkFNFcWcvSELNfU2grCgGM1DTR2sITFn+T/hiL7QTF7P1RVVXn0vE4F3d4cwPfs2cPnn3/O7Nmz+eCDD9i1axfXX389jY2N3H333Y7n/O1vf2PBggXccccdfPvtt9x4441ERERw5ZVXut2uL6+cy5UgJ9kXiuwHJej2Q2S8uuJeV9nqirsrrWyfuvqe3NeUq+9Btx8CRPaDU6CunJupqqqKn//85zzzzDOkpqaatt2FCxeyYMECx9eVlZXk5eWRlpZGQkJCl7dvs9nQNI20tLQefyLZ0/eDlpQBBRBrbSImPR3r+s0AhA2YQnp6eoBH538+eU+EDQbAWnes1T7VbOq8PDk9F4Jof8v/DUX2g2L2foiKivLoeT6vKGSz2UhPT+fpp5/GarUybtw4Dh06xIMPPugIum02G+PHj+f+++8HYOzYsXz//fc89dRTbQbdvrxyLleCnGRfKLIflGDbD9aaZtIA/fixdgtDpZXuxgqU6Qk0mXD1Pdj2Q6DIfnAK1JXz9qSmpmK1WikqKmrxeFFREZmZma2ev3v3bvbt28cFF1zgeMwI/sPCwti+fbvjdUVFRWRlZbXY5pgxY9yOIzIyksjIyFaPWywW0943mqaZur1Q1eP3g1FIraECi8WCVrASAK3PJLQeuk9Mf0/EqerlWmMNWlMdRLis37av6bZExkGQ7e8e/3/DTvaDYuZ+8HQbnQq6O3sAB8jKyiI8PNyRSg4wbNgwCgsLaWhoICIigqysLIYPH97idcOGDeO///1vm2Px5ZVzuRLkJPtCkf2gBN1+iG4GQKuvIj0tTVWvPVF9FZa6MgBS+o91pqR3QdDthwCR/eAUqCvn7YmIiGDcuHEsWbLE0fbLZrOxZMkS5s+f3+r5Q4cOZdOmTS0e+93vfkdVVRWPPfYYeXl5hIeHk5mZyZIlSxxBdmVlJatWreK6667r8piF6BJHIbUKOF4ORWqmmz5SRM00kQmqNVtzA9QehYjezu81SvVyIdrSqaC7swdwgMmTJ/PKK69gs9kcJyI7duwgKyuLiIgIx3O2b9/e4nU7duygT58+bY7F11fO5UqQk+wLRfaDElT7wT6roenNaE3HITKu9XMqCuzPTcESY15HhKDaDwEk+8EpEFfOO7JgwQKuvPJKxo8fz4QJE3j00UepqalxFEO94ooryMnJYfHixURFRTFixIgWr09KSgJo8fivfvUr7rvvPgYNGkS/fv248847yc7ObtXPWwi/i04CQKuvhAMrAR16DYS44El1Dnmapnp1Vx5SbcOS7EF3c6MKxKHl7LcQAvAivbwzB3CA6667jr/85S/cdNNN3HDDDezcuZP777+fG2+80bHNm2++mUmTJnH//ffz4x//mNWrV/P000/z9NNPm/RrCiG6pfBosISDrVEVU3MXdBuVy1OkI4LoeWbNmkVJSQl33XUXhYWFjBkzho8++shRm6WgoKDTAf6tt95KTU0N1157LeXl5Zx22ml89NFHpszOC9ElLn26tQJ7gd/e0p/bdLGp9qDbpW2Y0S4MIFxmuoU4UaeD7s4ewPPy8vj444+5+eabGTVqFDk5Odx0003cdtttjueccsopvPnmmyxcuJBFixbRr18/Hn30UWbPnm3CryiE6LY0TaWL1x5VQXdiTuvnlEnlctGzzZ8/v81stKVLl7b72hdeeKHVY5qmsWjRIhYtWmTC6IQwkWuf7v32oLvP5IANp9uKVeu6qSlxPmYE3ZZwCIvw/5iECHJeFVLr7AF84sSJrFy5st1tnn/++Zx//vneDEcI0ZO5Bt3uOHp0y0y3EEJ0a/b0cktNMZQeU4/1kZlu0zmCbpeZblnPLUS7fF69XAghfCrKXjixzaBbZrqFEKJHsM90W2vtM7Dx2ZDUdn0g4aWYXuq2xUx3tbqVoFsIt6TyjRAitBnVyDua6ZY13UII0b1Fn1Ass88k910tRNe4m+lukJluIdojQbcQIrS1F3Q3N0G5vXq5zHQLIUT3Zk8vd5DUct9ob013uFQuF8IdCbqFEKGtvaC78hDYmlRP0fhs/45LCCGEf4VFoodFO7+WImq+4S7obrQH3RFuuogIISToFkKEOCPorncTdBup5Ul9QPpICyFE92ef7dajkyF1SGDH0l3Fpqrb2lLnY8ZMt/ToFsItOQsVQoS29ma6jSJqsp5bCCF6BiPFPO9UudjqK0bQXVMCuq7uy5puIdoln0ZCiNBmr1brPujep25lPbcQQvQMUaqYmt5nUoAH0o3F2IPu5gaor1T3jerl4RJ0C+GOtAwTQoS29ma6y4x2YTLTLYQQPYE+4VoabBrhI38c6KF0XxExau12Q7WqYB6V6JJeLkG3EO5I0C2ECG3tppfvU7cy0y2EED3D8JkcS51Ielx6oEfSvcWm2oPuEug1ABqN9HJZ0y2EO5JeLoQIbbKmWwghhPCvE3t1G+nlMtMthFsSdAshQltbQffxY87Hkvr4d0xCCCFEdxbjUkwNnIXUZE23EG5J0C2ECG2RCeq2rsJZRRWc67njMiTdTQghhDCTo4K5MdMta7qFaI8E3UKI0GbMdNuanGvKwGU9t6SWCyGEEKZypJfbZ7obJegWoj0SdAshQltELGhWdd81xdxYzy1F1IQQQghznRh0y0y3EO2SoFsIEdo0zf26bmOmW4qoCSGEEOZqFXQba7plOZcQ7kjQLYQIfY6gu9L5WJnMdAshhBA+EdtL3daWqlvHTHdcYMYjRJCToFsIEfrcznTvV7eyplsIIYQwV5trumWmWwh3JOgWQoS+E4PupgaoPKjuy0y3EEIIYS4j6K4tBVuzrOkWogMSdAshQp8j6C5XtxUHQLeptWVx6QEblhBCCNEtxdjTy3WbahvWVKe+lj7dQrglQbcQIvSdONPtWrlc0wIyJCGEEKLbsoZDdLK6X17gfFxmuoVwS4JuIUToOzHodhRRk/XcQgghhE/EpKrbcnsNFc0CYZGBG48QQUyCbiFE6Gs1071P3cp6biGEEMI3jHXdRnZZRJxklwnRBgm6hRChr62gW3p0CyGEEL4Ra5/pNrqFSI9uIdokQbcQIvTJTLcQQgjhX46Z7n3qVtZzC9EmCbqFEKHPNejWdVnTLYQQQviaEXQba7qlR7cQbZKgWwgR+oygu75StS5prAE0SMoL6LCEEEKIbstIL684pG4j4gI3FiGCnATdQojQ5zrTbRR0ScyVKqpCCCGErxhBt96sbmVNtxBtkqBbCBH6XIPuMpce3UIIIYTwDSO93CBruoVokwTdQojQZwTdzQ1QslXdl6BbCCGE8B0JuoXwmATdQojQFxEHmv3j7Mh36laCbiGEEMJ3JOgWwmMSdAshQp+mOWe7D29QtxJ0CwHAk08+Sd++fYmKiiI/P5/Vq1e3+dz//e9/jB8/nqSkJGJjYxkzZgz/+te/WjznF7/4BZqmtfg3Y8YMX/8aQohgE5UEmtX5tazpFqJNYYEegBBCmCIqEY4fg+Nl6usUaRcmxOuvv86CBQt46qmnyM/P59FHH2X69Ols376d9PT0Vs9PSUnht7/9LUOHDiUiIoL33nuPOXPmkJ6ezvTp0x3PmzFjBs8//7zj68hIKVooRI9jsUBML6gpVl9L9XIh2iQz3UKI7iEyoeXX0qNbCB5++GHmzp3LnDlzGD58OE899RQxMTE899xzbp9/5plncvHFFzNs2DAGDBjATTfdxKhRo/j6669bPC8yMpLMzEzHv+TkZH/8OkKIYOOaYi59uoVokwTdQojuwUgvB4hMhGgJAkTP1tDQwNq1a5k2bZrjMYvFwrRp01ixYkWHr9d1nSVLlrB9+3amTJnS4ntLly4lPT2dIUOGcN1111FaWmr6+IUQIcBoGwaypluIdkh6uRCie3ANupP7qHXeQvRgR48epbm5mYyMjBaPZ2RksG3btjZfV1FRQU5ODvX19VitVv76179yzjnnOL4/Y8YMLrnkEvr168fu3bu54447OO+881ixYgVWq7XV9urr66mvr3d8XVlZCYDNZsNms3X118Rms6HruinbCmWyHxTZD07+2BdabCrG0dYWHgNBuN/lPaHIflDM3g+ebkeCbiFE9xCV5Lwv67mF8Fp8fDwbNmygurqaJUuWsGDBAvr378+ZZ54JwOWXX+547siRIxk1ahQDBgxg6dKlTJ06tdX2Fi9ezD333NPq8ZKSEurq6ro8XpvNRkVFBbquY7H03AQ+2Q+K7Acnf+yLeC0WY367oraJ+uJin/ycrpD3hCL7QTF7P1RVVXn0PAm6hRDdQ4uZ7r4BG4YQwSI1NRWr1UpRUVGLx4uKisjMzGzzdRaLhYEDBwIwZswYtm7dyuLFix1B94n69+9Pamoqu3btcht0L1y4kAULFji+rqysJC8vj7S0NBISElo9v7NsNhuappGWltbjTyRlP8h+cOWXfZGa57ibmJYNbgo0Bpq8JxTZD4rZ+yEqKsqj50nQLYToHloE3TLTLURERATjxo1jyZIlXHTRRYA62ViyZAnz58/3eDs2m61FeviJDh48SGlpKVlZWW6/HxkZ6ba6ucViMe3ET9M0U7cXqmQ/KLIfnHy+L+KcQbYlMk5VNA9C8p5QZD8oZu4HT7chQbcQonuQmW4hWlmwYAFXXnkl48ePZ8KECTz66KPU1NQwZ84cAK644gpycnJYvHgxoFLBx48fz4ABA6ivr+eDDz7gX//6F3/7298AqK6u5p577uHSSy8lMzOT3bt3c+uttzJw4MAWLcWEED1Ei+rlUkhNiLZI0C2E6B5cg25Z0y0EALNmzaKkpIS77rqLwsJCxowZw0cffeQorlZQUNDiKn1NTQ3XX389Bw8eJDo6mqFDh/LSSy8xa9YsAKxWKxs3buTFF1+kvLyc7Oxszj33XO69917p1S1ET+QadIdLyzAh2iJBtxCiezCCbksYJOQGdixCBJH58+e3mU6+dOnSFl/fd9993HfffW1uKzo6mo8//tjM4QkhQlmLlmFxgRuHEEFOgm4hRPdgrCtL7gdW+WgTQgghfC42XV3sBoiUoFuItsiZqRCie8g+Gc5ZBLmnBHokQgghRM8QGQcX/13dD48O7FiECGISdAshugeLBSbfFOhRCCGEED3LyMsCPQIhgl7PrhcvhBBCCCGEEEL4kATdQgghhBBCCCGEj0jQLYQQQgghhBBC+IgE3UIIIYQQQgghhI9I0C2EEEIIIYQQQviIBN1CCCGEEEIIIYSPSNAthBBCCCGEEEL4iATdQgghhBBCCCGEj0jQLYQQQgghhBBC+IgE3UIIIYQQQgghhI9I0C2EEEIIIYQQQvhIWKAHYBZd1wGorKzs8rZsNhtVVVVERUVhsfTs6xKyLxTZD4rsB0X2gyL7wcnsfWEcy4xjW3di5vEa5H1okP2gyH5wkn2hyH5QZD8ogTped5ugu6qqCoC8vLwAj0QIIYQwR1VVFYmJiYEehqnkeC2EEKK76eh4rend5DK6zWbj8OHDxMfHo2lal7ZVWVlJXl4eBw4cICEhwaQRhibZF4rsB0X2gyL7QZH94GT2vtB1naqqKrKzs7vdjISZx2uQ96FB9oMi+8FJ9oUi+0GR/aAE6njdbWa6LRYLubm5pm4zISGhR78pXcm+UGQ/KLIfFNkPiuwHJzP3RXeb4Tb44ngN8j40yH5QZD84yb5QZD8osh8Ufx+vu9flcyGEEEIIIYQQIohI0C2EEEIIIYQQQviIBN1uREZGcvfddxMZGRnooQSc7AtF9oMi+0GR/aDIfnCSfRE4su8V2Q+K7Acn2ReK7AdF9oMSqP3QbQqpCSGEEEIIIYQQwUZmuoUQQgghhBBCCB+RoFsIIYQQQgghhPARCbqFEEIIIYQQQggfkaBbCCGEEEIIIYTwEQm63XjyySfp27cvUVFR5Ofns3r16kAPya9+//vfo2lai39Dhw4N9LD84ssvv+SCCy4gOzsbTdN46623Wnxf13XuuususrKyiI6OZtq0aezcuTMwg/WhjvbDL37xi1bvkRkzZgRmsD60ePFiTjnlFOLj40lPT+eiiy5i+/btLZ5TV1fHvHnz6NWrF3FxcVx66aUUFRUFaMS+4cl+OPPMM1u9J375y18GaMS+8be//Y1Ro0aRkJBAQkICEydO5MMPP3R8vye8F4JNTz9eQ889ZsvxWpHjtSLHa0WO10owHq8l6D7B66+/zoIFC7j77rtZt24do0ePZvr06RQXFwd6aH510kknceTIEce/r7/+OtBD8ouamhpGjx7Nk08+6fb7DzzwAI8//jhPPfUUq1atIjY2lunTp1NXV+fnkfpWR/sBYMaMGS3eI6+++qofR+gfy5YtY968eaxcuZJPP/2UxsZGzj33XGpqahzPufnmm3n33Xf5z3/+w7Jlyzh8+DCXXHJJAEdtPk/2A8DcuXNbvCceeOCBAI3YN3Jzc/njH//I2rVrWbNmDWeffTYzZ85k8+bNQM94LwQTOV479cRjthyvFTleK3K8VuR4rQTl8VoXLUyYMEGfN2+e4+vm5mY9OztbX7x4cQBH5V933323Pnr06EAPI+AA/c0333R8bbPZ9MzMTP3BBx90PFZeXq5HRkbqr776agBG6B8n7gdd1/Urr7xSnzlzZkDGE0jFxcU6oC9btkzXdfX3Dw8P1//zn/84nrN161Yd0FesWBGoYfrciftB13X9jDPO0G+66abADSpAkpOT9X/84x899r0QSHK8VuSYLcdrgxyvneR4rcjx2inQx2uZ6XbR0NDA2rVrmTZtmuMxi8XCtGnTWLFiRQBH5n87d+4kOzub/v37M3v2bAoKCgI9pIDbu3cvhYWFLd4fiYmJ5Ofn97j3B8DSpUtJT09nyJAhXHfddZSWlgZ6SD5XUVEBQEpKCgBr166lsbGxxXti6NCh9O7du1u/J07cD4aXX36Z1NRURowYwcKFC6mtrQ3E8PyiubmZ1157jZqaGiZOnNhj3wuBIsfrluSY3ZIcr1uS47Ucr+V4HfjjdZjPthyCjh49SnNzMxkZGS0ez8jIYNu2bQEalf/l5+fzwgsvMGTIEI4cOcI999zD6aefzvfff098fHyghxcwhYWFAG7fH8b3eooZM2ZwySWX0K9fP3bv3s0dd9zBeeedx4oVK7BarYEenk/YbDZ+9atfMXnyZEaMGAGo90RERARJSUktntud3xPu9gPAT3/6U/r06UN2djYbN27ktttuY/v27fzvf/8L4GjNt2nTJiZOnEhdXR1xcXG8+eabDB8+nA0bNvS490IgyfHaSY7Zrcnx2kmO13K8luN1cByvJegWrZx33nmO+6NGjSI/P58+ffrw73//m6uvvjqAIxPB4vLLL3fcHzlyJKNGjWLAgAEsXbqUqVOnBnBkvjNv3jy+//77HrFWsj1t7Ydrr73WcX/kyJFkZWUxdepUdu/ezYABA/w9TJ8ZMmQIGzZsoKKigjfeeIMrr7ySZcuWBXpYogeTY7Zojxyvey45XgfX8VrSy12kpqZitVpbVa8rKioiMzMzQKMKvKSkJAYPHsyuXbsCPZSAMt4D8v5orX///qSmpnbb98j8+fN57733+OKLL8jNzXU8npmZSUNDA+Xl5S2e313fE23tB3fy8/MBut17IiIigoEDBzJu3DgWL17M6NGjeeyxx3rceyHQ5HjdNjlmy/G6PXK8Lm/x/O76npDjdfAdryXodhEREcG4ceNYsmSJ4zGbzcaSJUuYOHFiAEcWWNXV1ezevZusrKxADyWg+vXrR2ZmZov3R2VlJatWrerR7w+AgwcPUlpa2u3eI7quM3/+fN58800+//xz+vXr1+L748aNIzw8vMV7Yvv27RQUFHSr90RH+8GdDRs2AHS798SJbDYb9fX1Pea9ECzkeN02OWbL8bo9crzu3p/RcrxuW8CP1z4r0RaiXnvtNT0yMlJ/4YUX9C1btujXXnutnpSUpBcWFgZ6aH7z61//Wl+6dKm+d+9e/ZtvvtGnTZump6am6sXFxYEems9VVVXp69ev19evX68D+sMPP6yvX79e379/v67ruv7HP/5RT0pK0t9++21948aN+syZM/V+/frpx48fD/DIzdXefqiqqtJ/85vf6CtWrND37t2rf/bZZ/rJJ5+sDxo0SK+rqwv00E113XXX6YmJifrSpUv1I0eOOP7V1tY6nvPLX/5S7927t/7555/ra9as0SdOnKhPnDgxgKM2X0f7YdeuXfqiRYv0NWvW6Hv37tXffvttvX///vqUKVMCPHJz3X777fqyZcv0vXv36hs3btRvv/12XdM0/ZNPPtF1vWe8F4KJHK+VnnrMluO1IsdrRY7XihyvlWA8XkvQ7cYTTzyh9+7dW4+IiNAnTJigr1y5MtBD8qtZs2bpWVlZekREhJ6Tk6PPmjVL37VrV6CH5RdffPGFDrT6d+WVV+q6rtqQ3HnnnXpGRoYeGRmpT506Vd++fXtgB+0D7e2H2tpa/dxzz9XT0tL08PBwvU+fPvrcuXO75Ymuu30A6M8//7zjOcePH9evv/56PTk5WY+JidEvvvhi/ciRI4EbtA90tB8KCgr0KVOm6CkpKXpkZKQ+cOBA/ZZbbtErKioCO3CTXXXVVXqfPn30iIgIPS0tTZ86darjAK7rPeO9EGx6+vFa13vuMVuO14ocrxU5XityvFaC8Xit6bqumz9/LoQQQgghhBBCCFnTLYQQQgghhBBC+IgE3UIIIYQQQgghhI9I0C2EEEIIIYQQQviIBN1CCCGEEEIIIYSPSNAthBBCCCGEEEL4iATdQgghhBBCCCGEj0jQLYQQQgghhBBC+IgE3UIIIYQQQgghhI9I0C2EEEIIIYQQQviIBN1CCCGEEEIIIYSPSNAthBBCCCGEEEL4iATdQgghhBBCCCGEj/w/K7WwBkC9wyMAAAAASUVORK5CYII=\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Confusion matrix\n", "metrics_dict = compute_metrics(all_preds, all_targets)\n", "\n", "from sklearn.metrics import confusion_matrix\n", "import seaborn as sns\n", "\n", "cm = confusion_matrix(all_targets, all_preds)\n", "print(\"Confusion Matrix:\")\n", "print(cm)\n", "\n", "plt.figure(figsize=(6, 5))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap=\"Blues\", xticklabels=[\"Non-Pile\", \"<PERSON>le\"], yticklabels=[\"Non-Pile\", \"Pile\"])\n", "plt.xlabel(\"Predicted\")\n", "plt.ylabel(\"Actual\")\n", "plt.title(\"Confusion Matrix\")\n", "plt.tight_layout()\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 721}, "id": "WtmWjC7MT6bK", "outputId": "258903ae-0ed6-4917-cbe0-c6bef911964e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "TEST METRICS:\n", "Accuracy: 0.6770\n", "F1-Score: 0.8074\n", "Precision: 0.6836\n", "Recall: 0.9861\n", "Confusion Matrix:\n", " [[  0 131]\n", " [  4 283]]\n", "Confusion Matrix:\n", "[[  0 131]\n", " [  4 283]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 600x500 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAHqCAYAAAD4YG/CAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAQmJJREFUeJzt3XucTfX+x/H3nmG2MVeDMaYYcr+Me2mSuzAkootShqSSSa5pOu7KREVRON2QKF1EqdyZOTJUjkHIZSKnY8Z9jBnMdf3+2Mf+tRuX2WyzZ1avZ4/1eNjf9d1rfdbu6Hwen893rWUxDMMQAACASXi4OwAAAABXIrkBAACmQnIDAABMheQGAACYCskNAAAwFZIbAABgKiQ3AADAVEhuAACAqZDcAAAAUyG5AYqQAwcOqGPHjgoICJDFYtGyZctcevzDhw/LYrFo/vz5Lj1ucdamTRu1adPG3WEAcCGSG+AvkpKS9PTTT+u2225TqVKl5O/vrxYtWuitt97ShQsXbuq5o6KitGvXLr3yyitauHChmjVrdlPPV5j69esni8Uif3//y/6OBw4ckMVikcVi0euvv+708Y8ePaoJEyYoMTHRBdECKM5KuDsAoCj59ttv9eCDD8pqtapv376qX7++srKytGnTJo0aNUq7d+/Wu+++e1POfeHCBSUkJOgf//iHoqOjb8o5wsLCdOHCBZUsWfKmHP9aSpQoofPnz+ubb77RQw895LBv0aJFKlWqlC5evHhdxz569KgmTpyoKlWqqFGjRgX+3urVq6/rfACKLpIb4H8OHTqk3r17KywsTOvXr1fFihXt+wYPHqyDBw/q22+/vWnnP3HihCQpMDDwpp3DYrGoVKlSN+3412K1WtWiRQt98skn+ZKbxYsXq2vXrvryyy8LJZbz58+rdOnS8vLyKpTzASg8tKWA/5k2bZrS09P1wQcfOCQ2l1SvXl3PP/+8/XNOTo4mT56satWqyWq1qkqVKnrppZeUmZnp8L0qVaro3nvv1aZNm3THHXeoVKlSuu222/TRRx/Z50yYMEFhYWGSpFGjRslisahKlSqSbO2cS3/+swkTJshisTiMrVmzRnfffbcCAwPl6+urWrVq6aWXXrLvv9Kam/Xr16tly5by8fFRYGCgunfvrr179172fAcPHlS/fv0UGBiogIAA9e/fX+fPn7/yD/sXjz76qL7//nulpqbax3766ScdOHBAjz76aL75p0+f1siRIxUeHi5fX1/5+/srMjJSO3bssM/ZuHGjbr/9dklS//797e2tS9fZpk0b1a9fX9u2bVOrVq1UunRp++/y1zU3UVFRKlWqVL7r79Spk8qUKaOjR48W+FoBuAfJDfA/33zzjW677TbdddddBZr/5JNPaty4cWrSpIlmzJih1q1bKzY2Vr1798439+DBg3rggQd0zz336I033lCZMmXUr18/7d69W5LUs2dPzZgxQ5L0yCOPaOHChXrzzTedin/37t269957lZmZqUmTJumNN97Qfffdpx9++OGq31u7dq06deqk48ePa8KECRo+fLg2b96sFi1a6PDhw/nmP/TQQzp37pxiY2P10EMPaf78+Zo4cWKB4+zZs6csFouWLl1qH1u8eLFq166tJk2a5Jv/22+/admyZbr33ns1ffp0jRo1Srt27VLr1q3tiUadOnU0adIkSdJTTz2lhQsXauHChWrVqpX9OKdOnVJkZKQaNWqkN998U23btr1sfG+99ZbKly+vqKgo5ebmSpL++c9/avXq1Zo1a5ZCQ0MLfK0A3MQAYJw9e9aQZHTv3r1A8xMTEw1JxpNPPukwPnLkSEOSsX79evtYWFiYIcmIj4+3jx0/ftywWq3GiBEj7GOHDh0yJBmvvfaawzGjoqKMsLCwfDGMHz/e+PNf4RkzZhiSjBMnTlwx7kvnmDdvnn2sUaNGRnBwsHHq1Cn72I4dOwwPDw+jb9+++c73xBNPOBzz/vvvN8qWLXvFc/75Onx8fAzDMIwHHnjAaN++vWEYhpGbm2uEhIQYEydOvOxvcPHiRSM3NzffdVitVmPSpEn2sZ9++inftV3SunVrQ5Ixd+7cy+5r3bq1w9iqVasMScbLL79s/Pbbb4avr6/Ro0ePa14jgKKByg0gKS0tTZLk5+dXoPnfffedJGn48OEO4yNGjJCkfGtz6tatq5YtW9o/ly9fXrVq1dJvv/123TH/1aW1OsuXL1deXl6BvpOcnKzExET169dPQUFB9vEGDRronnvusV/nnz3zzDMOn1u2bKlTp07Zf8OCePTRR7Vx40alpKRo/fr1SklJuWxLSrKt0/HwsP2nKjc3V6dOnbK33P79738X+JxWq1X9+/cv0NyOHTvq6aef1qRJk9SzZ0+VKlVK//znPwt8LgDuRXIDSPL395cknTt3rkDzf//9d3l4eKh69eoO4yEhIQoMDNTvv//uMF65cuV8xyhTpozOnDlznRHn9/DDD6tFixZ68sknVaFCBfXu3VufffbZVROdS3HWqlUr3746dero5MmTysjIcBj/67WUKVNGkpy6li5dusjPz09LlizRokWLdPvtt+f7LS/Jy8vTjBkzVKNGDVmtVpUrV07ly5fXzp07dfbs2QKf85ZbbnFq8fDrr7+uoKAgJSYmaubMmQoODi7wdwG4F8kNIFtyExoaql9++cWp7/11Qe+VeHp6XnbcMIzrPsel9SCXeHt7Kz4+XmvXrtXjjz+unTt36uGHH9Y999yTb+6NuJFrucRqtapnz55asGCBvvrqqytWbSRpypQpGj58uFq1aqWPP/5Yq1at0po1a1SvXr0CV6gk2+/jjO3bt+v48eOSpF27djn1XQDuRXID/M+9996rpKQkJSQkXHNuWFiY8vLydODAAYfxY8eOKTU11X7nkyuUKVPG4c6iS/5aHZIkDw8PtW/fXtOnT9eePXv0yiuvaP369dqwYcNlj30pzn379uXb9+uvv6pcuXLy8fG5sQu4gkcffVTbt2/XuXPnLrsI+5IvvvhCbdu21QcffKDevXurY8eO6tChQ77fpKCJZkFkZGSof//+qlu3rp566ilNmzZNP/30k8uOD+DmIrkB/ueFF16Qj4+PnnzySR07dizf/qSkJL311luSbG0VSfnuaJo+fbokqWvXri6Lq1q1ajp79qx27txpH0tOTtZXX33lMO/06dP5vnvpYXZ/vT39kooVK6pRo0ZasGCBQ7Lwyy+/aPXq1fbrvBnatm2ryZMn6+2331ZISMgV53l6euarCn3++ef673//6zB2KQm7XCLorNGjR+vIkSNasGCBpk+fripVqigqKuqKvyOAooWH+AH/U61aNS1evFgPP/yw6tSp4/CE4s2bN+vzzz9Xv379JEkNGzZUVFSU3n33XaWmpqp169b68ccftWDBAvXo0eOKtxlfj969e2v06NG6//77NWTIEJ0/f15z5sxRzZo1HRbUTpo0SfHx8eratavCwsJ0/PhxzZ49W7feeqvuvvvuKx7/tddeU2RkpCIiIjRgwABduHBBs2bNUkBAgCZMmOCy6/grDw8PjRkz5prz7r33Xk2aNEn9+/fXXXfdpV27dmnRokW67bbbHOZVq1ZNgYGBmjt3rvz8/OTj46PmzZuratWqTsW1fv16zZ49W+PHj7ffmj5v3jy1adNGY8eO1bRp05w6HgA3cPPdWkCRs3//fmPgwIFGlSpVDC8vL8PPz89o0aKFMWvWLOPixYv2ednZ2cbEiRONqlWrGiVLljQqVapkxMTEOMwxDNut4F27ds13nr/egnylW8ENwzBWr15t1K9f3/Dy8jJq1aplfPzxx/luBV+3bp3RvXt3IzQ01PDy8jJCQ0ONRx55xNi/f3++c/z1dum1a9caLVq0MLy9vQ1/f3+jW7duxp49exzmXDrfX281nzdvniHJOHTo0BV/U8NwvBX8Sq50K/iIESOMihUrGt7e3kaLFi2MhISEy97CvXz5cqNu3bpGiRIlHK6zdevWRr169S57zj8fJy0tzQgLCzOaNGliZGdnO8wbNmyY4eHhYSQkJFz1GgC4n8UwnFgFCAAAUMSx5gYAAJgKyQ0AADAVkhsAAGAqJDcAAMBUSG4AAICpkNwAAABTIbkBAACmYsonFF/McXcEgDlMXrPf3SEApvBKZM1COY9342iXHu/C9rdderzCQuUGAACYiikrNwAA/C1ZqFlIJDcAAJiHxeLuCIoEUjwAAGAqVG4AADAL2lKSqNwAAACToXIDAIBZsOZGEskNAADmQVtKEm0pAABgMlRuAAAwC9pSkkhuAAAwD9pSkmhLAQAAk6FyAwCAWdCWkkTlBgAAmAyVGwAAzII1N5JIbgAAMA/aUpJoSwEAAJOhcgMAgFnQlpJEcgMAgHnQlpJEWwoAAJgMlRsAAMyCtpQkkhsAAMyD5EYSbSkAAGAyVG4AADALDxYUS1RuAACAyVC5AQDALFhzI4nkBgAA8+A5N5JoSwEAAJOhcgMAgFnQlpJEcgMAgHnQlpJEWwoAAJgMlRsAAMyCtpQkKjcAAMBkqNwAAGAWrLmRRHIDAIB50JaSRFsKAACYDJUbAADMgraUJJIbAADMg7aUJNpSAADAZKjcAABgFrSlJJHcAABgHrSlJNGWAgAAJkNyAwCAWVg8XLs5ITY2Vrfffrv8/PwUHBysHj16aN++fQ5z2rRpI4vF4rA988wzDnOOHDmirl27qnTp0goODtaoUaOUk5PjVCy0pQAAwA2Li4vT4MGDdfvttysnJ0cvvfSSOnbsqD179sjHx8c+b+DAgZo0aZL9c+nSpe1/zs3NVdeuXRUSEqLNmzcrOTlZffv2VcmSJTVlypQCx0JyAwCAWbhxQfHKlSsdPs+fP1/BwcHatm2bWrVqZR8vXbq0QkJCLnuM1atXa8+ePVq7dq0qVKigRo0aafLkyRo9erQmTJggLy+vAsVCWwoAALNwcVsqMzNTaWlpDltmZmaBQjl79qwkKSgoyGF80aJFKleunOrXr6+YmBidP3/evi8hIUHh4eGqUKGCfaxTp05KS0vT7t27C/wzkNwAAIDLio2NVUBAgMMWGxt7ze/l5eVp6NChatGiherXr28ff/TRR/Xxxx9rw4YNiomJ0cKFC/XYY4/Z96ekpDgkNpLsn1NSUgocN20pAADMwsVtqZiYGA0fPtxhzGq1XvN7gwcP1i+//KJNmzY5jD/11FP2P4eHh6tixYpq3769kpKSVK1aNdcELZIbAADMw8XPubFarQVKZv4sOjpaK1asUHx8vG699darzm3evLkk6eDBg6pWrZpCQkL0448/Osw5duyYJF1xnc7l0JYCAAA3zDAMRUdH66uvvtL69etVtWrVa34nMTFRklSxYkVJUkREhHbt2qXjx4/b56xZs0b+/v6qW7dugWOhcgMAgFm48W6pwYMHa/HixVq+fLn8/Pzsa2QCAgLk7e2tpKQkLV68WF26dFHZsmW1c+dODRs2TK1atVKDBg0kSR07dlTdunX1+OOPa9q0aUpJSdGYMWM0ePBgpypIJDcAAJiExY3JzZw5cyTZHtT3Z/PmzVO/fv3k5eWltWvX6s0331RGRoYqVaqkXr16acyYMfa5np6eWrFihQYNGqSIiAj5+PgoKirK4bk4BUFyAwAAbphhGFfdX6lSJcXFxV3zOGFhYfruu+9uKBaSGwAATMKdlZuihAXFAADAVKjcAABgFhRuJJHcAABgGrSlbGhLAQAAU6FyAwCASVC5sSG5AQDAJEhubGhLAQAAU6FyAwCASVC5saFyAwAATIXKDQAAZkHhRhLJDQAApkFbyoa2FAAAMBUqNwAAmASVGxuSGwAATILkxoa2FAAAMBUqNwAAmASVGxuSGwAAzILcRhJtKQAAYDJUbgAAMAnaUjZUbgAAgKlQuQEAwCSo3NgUqcpNVlaW9u3bp5ycHHeHAgBAsWOxWFy6FVdFIrk5f/68BgwYoNKlS6tevXo6cuSIJOm5557Tq6++6uboAABAcVIkkpuYmBjt2LFDGzduVKlSpezjHTp00JIlS9wYGQAAxYjFxVsxVSTW3CxbtkxLlizRnXfe6VAGq1evnpKSktwYGQAAxUdxbiW5UpGo3Jw4cULBwcH5xjMyMvgXBQAAnFIkkptmzZrp22+/tX++lNC8//77ioiIcFdYAAAUKywotikSbakpU6YoMjJSe/bsUU5Ojt566y3t2bNHmzdvVlxcnLvDAwCgWCjOCYkrFYnKzd13363ExETl5OQoPDxcq1evVnBwsBISEtS0aVN3hwcAAIqRIlG5kaRq1arpvffec3cYAAAUW1RubNyW3KSlpRV4rr+//02MBAAAmInbkpvAwMBrZpiGYchisSg3N7eQogIAoBijcCPJjcnNhg0b3HVqAABMibaUjduSm9atW7vr1AAAwMTcltzs3LlT9evXl4eHh3bu3HnVuQ0aNCikqAAAKL6o3Ni4Lblp1KiRUlJSFBwcrEaNGsliscgwjHzzWHMDAEDBkNzYuC25OXTokMqXL2//MwAAgCu4LbkJCwuTZLslfP/+/crKytIdd9xhT3gAAICTKNxIcvND/BITE9WlSxcdO3ZMhmHIz89Pn332mTp16uTOsAAAQDHm1tcvjB49WlWrVtWmTZu0bds2tW/fXtHR0e4MCQCAYosXZ9q4tXKzbds2rV69Wk2aNJEkffjhhwoKClJaWhpPJTaxTxcv0oJ5H+jkyROqWau2XnxprMK5Iw6wO5H0i/avX6oz/0nSxbTTinjiJd3SIMK+f/f3i/XH9nidTz0pD88SKlOpuup1eVxlq9Syz9m7eomS9/yss//9TR6eJdX91U/dcSkoZMU5IXElt1ZuTp8+rVtvvdX+OTAwUD4+Pjp16pQbo8LNtPL77/T6tFg9/exgffr5V6pVq7YGPT2Af+fAn+RkXlRAaFU1fuCZy+73Cw5Vo17P6J4X3labIVNVOihY/5o7TpnpZ+1z8nJzdGujFrqtRZfCChsoMtz+4sw9e/YoJSXF/tkwDO3du1fnzp2zj/GcG/NYuGCeej7wkHrc30uSNGb8RMXHb9SypV9qwMCn3BwdUDRUrNtMFes2u+L+yk3bOHxu2ONJHd6yRqlHD6tCzYaSpHqRfSRJh7euvWlxouihcmPj9uSmffv2+Z5vc++999qfe8NzbswjOytLe/fs1oCBT9vHPDw8dOedd2nnju1ujAwovvJysvXb5pUqWcpHgaFV3B0O3IzkxsatyQ3Pt/l7OZN6Rrm5uSpbtqzDeNmyZXXo0G9uigoono7u/lFbF7ym3OxMlfIvo5bPTpLVN8DdYQFFgluTm0vPurkRmZmZyszMdBgzPK2yWq03fGwAKKqCqzfQPaPeUmZGmg4lrNaW+VPVbtgbKuUX6O7Q4E4UbiS5eUHx5YSHh+s///lPgefHxsYqICDAYXttauxNjBDXq0xgGXl6euZbPHzq1CmVK1fOTVEBxVMJayn5lg9V2Sq11eyRIfLw8NThLWvcHRbcjFvBbYpccnP48GFlZ2cXeH5MTIzOnj3rsI0aHXMTI8T1KunlpTp162nrlgT7WF5enrZuTVCDho3dGBlQ/BmGodycgv+3EzAzty8ovlFWa/4W1MUcNwWDa3o8qr/GvjRa9erVV/3wBvp44QJduHBBPe7v6e7QgCIjJ/OC0k8k2z9nnD6m1D9+k5ePr7xK+2vvms8UWv8OlfIPUlZGmpL+9a0unD2lWxu1sH/n/JnjyspI1/kzJ2QYeUr9w7auzbd8RZWwehf6NaFwFOdqiysVueSmZcuW8vbmL55ZdY7sojOnT2v22zN18uQJ1apdR7P/+b7K0pYC7E4fOaj4d16yf9657ANJUtjt7dTkocE6d/wPJcxbp6z0NHn5+KtM5RpqM+RVBVT8/3WMu79bpN9/Wm//vPb15yVJrQZPUXCN8EK6EsA9LMZf78M2ASo3gGtMXrPf3SEApvBKZM1COU/1kd+79HgHX4906fEKS5Gp3Bw4cEAbNmzQ8ePHlZeX57Bv3LhxbooKAIDig7aUTZFIbt577z0NGjRI5cqVU0hIiMO/HIvFQnIDAAAKrEgkNy+//LJeeeUVjR492t2hAABQbFG4sSkSyc2ZM2f04IMPujsMAACKNdpSNkXiOTcPPvigVq9e7e4wAACACRSJyk316tU1duxYbdmyReHh4SpZsqTD/iFDhrgpMgAAig8KNzZFIrl599135evrq7i4OMXFxTnss1gsJDcAABSAhwfZjVREkhveDg4AAFylSKy5+TPDMGTC5woCAHDTWSyu3ZwRGxur22+/XX5+fgoODlaPHj20b98+hzkXL17U4MGDVbZsWfn6+qpXr146duyYw5wjR46oa9euKl26tIKDgzVq1Cjl5Dj3dN4ik9x89NFHCg8Pl7e3t7y9vdWgQQMtXLjQ3WEBAIACiIuL0+DBg7VlyxatWbNG2dnZ6tixozIyMuxzhg0bpm+++Uaff/654uLidPToUfXs+f/vFszNzVXXrl2VlZWlzZs3a8GCBZo/f77Tz7srEq9fmD59usaOHavo6Gi1aGF78dumTZv0zjvv6OWXX9awYcOcOh6vXwBcg9cvAK5RWK9fqD9mjUuP98vL91z3d0+cOKHg4GDFxcWpVatWOnv2rMqXL6/FixfrgQcekCT9+uuvqlOnjhISEnTnnXfq+++/17333qujR4+qQoUKkqS5c+dq9OjROnHihLy8vAp07iJRuZk1a5bmzJmjqVOn6r777tN9992nadOmafbs2Zo5c6a7wwMAoFhwZ1vqr86ePStJCgoKkiRt27ZN2dnZ6tChg31O7dq1VblyZSUkJEiSEhISFB4ebk9sJKlTp05KS0vT7t27C3zuIrGgODk5WXfddVe+8bvuukvJycluiAgAAGRmZiozM9NhzGq1ymq1XvV7eXl5Gjp0qFq0aKH69etLklJSUuTl5aXAwECHuRUqVFBKSop9zp8Tm0v7L+0rqCJRualevbo+++yzfONLlixRjRo13BARAADFj8VicekWGxurgIAAhy02NvaacQwePFi//PKLPv3000K46vyKROVm4sSJevjhhxUfH29fc/PDDz9o3bp1l016AABAfq5+/UJMTIyGDx/uMHatqk10dLRWrFih+Ph43XrrrfbxkJAQZWVlKTU11aF6c+zYMYWEhNjn/Pjjjw7Hu3Q31aU5BVEkKje9evXS1q1bVbZsWS1btkzLli1TuXLl9OOPP+r+++93d3gAAPwtWa1W+fv7O2xXSm4Mw1B0dLS++uorrV+/XlWrVnXY37RpU5UsWVLr1q2zj+3bt09HjhxRRESEJCkiIkK7du3S8ePH7XPWrFkjf39/1a1bt8BxF4nKjWS76EWLFrk7DAAAii13vn5h8ODBWrx4sZYvXy4/Pz/7GpmAgAB5e3srICBAAwYM0PDhwxUUFCR/f38999xzioiI0J133ilJ6tixo+rWravHH39c06ZNU0pKisaMGaPBgwdfs2L0Z25Nbjw8PK5ZQrNYLE4/vAcAABSuOXPmSJLatGnjMD5v3jz169dPkjRjxgx5eHioV69eyszMVKdOnTR79mz7XE9PT61YsUKDBg1SRESEfHx8FBUVpUmTJjkVi1ufc7N8+fIr7ktISNDMmTOVl5enixcvOnVcnnMDuAbPuQFco7Cec9N44nqXHm/7+HYuPV5hcWvlpnv37vnG9u3bpxdffFHffPON+vTp43S2BgDA3xVvBbcpEguKJeno0aMaOHCgwsPDlZOTo8TERC1YsEBhYWHuDg0AABQjbk9uzp49q9GjR6t69eravXu31q1bp2+++cb+0B8AAFAwrn7OTXHl1rbUtGnTNHXqVIWEhOiTTz65bJsKAAAUTDHOR1zKrcnNiy++KG9vb1WvXl0LFizQggULLjtv6dKlhRwZAAAortya3PTt27dYl70AAChK+P9UG7cmN/Pnz3fn6QEAMBVyGxu3LygGAABwpSLz+gUAAHBjaEvZULkBAACmQuUGAACToHBjQ3IDAIBJ0JayoS0FAABMhcoNAAAmQeHGhuQGAACToC1lQ1sKAACYCpUbAABMgsKNDZUbAABgKlRuAAAwCdbc2JDcAABgEiQ3NrSlAACAqVC5AQDAJCjc2JDcAABgErSlbGhLAQAAU6FyAwCASVC4sSG5AQDAJGhL2dCWAgAApkLlBgAAk6BwY0PlBgAAmAqVGwAATMKD0o0kkhsAAEyD3MaGthQAADAVKjcAAJgEt4LbkNwAAGASHuQ2kmhLAQAAk6FyAwCASdCWsiG5AQDAJMhtbGhLAQAAU6FyAwCASVhE6UaicgMAAEyGyg0AACbBreA2JDcAAJgEd0vZ0JYCAACmQuUGAACToHBjQ3IDAIBJeJDdSKItBQAATIbKDQAAJkHhxobKDQAAMBUqNwAAmAS3gtuQ3AAAYBLkNja0pQAAgKlQuQEAwCS4FdyG5AYAAJMgtbGhLQUAAEyFyg0AACbB3VI2JDcAAJiEB7mNJNpSAADAZKjcAABgErSlbAqU3Hz99dcFPuB999133cEAAADcqAIlNz169CjQwSwWi3Jzc28kHgAAcJ0o3NgUKLnJy8u72XEAAIAbRFvKhgXFAADghsXHx6tbt24KDQ2VxWLRsmXLHPb369dPFovFYevcubPDnNOnT6tPnz7y9/dXYGCgBgwYoPT0dKdjua4FxRkZGYqLi9ORI0eUlZXlsG/IkCHXc0gAAHCD3HkreEZGhho2bKgnnnhCPXv2vOyczp07a968efbPVqvVYX+fPn2UnJysNWvWKDs7W/3799dTTz2lxYsXOxWL08nN9u3b1aVLF50/f14ZGRkKCgrSyZMnVbp0aQUHB5PcAADgJu5sS0VGRioyMvKqc6xWq0JCQi67b+/evVq5cqV++uknNWvWTJI0a9YsdenSRa+//rpCQ0MLHIvTbalhw4apW7duOnPmjLy9vbVlyxb9/vvvatq0qV5//XVnDwcAAIqozMxMpaWlOWyZmZnXfbyNGzcqODhYtWrV0qBBg3Tq1Cn7voSEBAUGBtoTG0nq0KGDPDw8tHXrVqfO43Ryk5iYqBEjRsjDw0Oenp7KzMxUpUqVNG3aNL300kvOHg4AALiIxcVbbGysAgICHLbY2Njriq1z58766KOPtG7dOk2dOlVxcXGKjIy032WdkpKi4OBgh++UKFFCQUFBSklJcepcTrelSpYsKQ8PW04UHBysI0eOqE6dOgoICNB//vMfZw8HAABcxMPFbamYmBgNHz7cYeyv62QKqnfv3vY/h4eHq0GDBqpWrZo2btyo9u3b31Ccf+V0ctO4cWP99NNPqlGjhlq3bq1x48bp5MmTWrhwoerXr+/S4AAAgPtYrdbrTmau5bbbblO5cuV08OBBtW/fXiEhITp+/LjDnJycHJ0+ffqK63SuxOm21JQpU1SxYkVJ0iuvvKIyZcpo0KBBOnHihN59911nDwcAAFzEYnHtdjP98ccfOnXqlD2niIiIUGpqqrZt22afs379euXl5al58+ZOHdvpys2fF/oEBwdr5cqVzh4CAACYTHp6ug4ePGj/fOjQISUmJiooKEhBQUGaOHGievXqpZCQECUlJemFF15Q9erV1alTJ0lSnTp11LlzZw0cOFBz585Vdna2oqOj1bt3b6fulJJ4iB8AAKbx14fk3ejmjJ9//lmNGzdW48aNJUnDhw9X48aNNW7cOHl6emrnzp267777VLNmTQ0YMEBNmzbVv/71L4e216JFi1S7dm21b99eXbp00d13331dXSGnKzdVq1a96gX/9ttvTgcBAABunDvfvtCmTRsZhnHF/atWrbrmMYKCgpx+YN/lOJ3cDB061OFzdna2tm/frpUrV2rUqFE3HBAAAMCNcDq5ef755y87/s477+jnn3++4YAAAMD1cfWt4MWVy9bcREZG6ssvv3TV4QAAgJOK091SN5PLkpsvvvhCQUFBrjocAADAdbmuh/j9eUGxYRhKSUnRiRMnNHv2bJcGBwAACs6dL84sSpxObrp37+7w43l4eKh8+fJq06aNateu7dLgAAAAnGUxrnbfVjF1Pst0lwS4Rdnmz7k7BMAULmx/u1DO89xXe116vFn313Hp8QqL02tuPD098737QZJOnTolT09PlwQFAACc586H+BUlTic3Vyr0ZGZmysvL64YDAgAAuBEFXnMzc+ZMSbas8P3335evr699X25uruLj41lzAwCAG3kU32KLSxU4uZkxY4YkW+Vm7ty5Di0oLy8vValSRXPnznV9hAAAoEBIbmwKnNwcOnRIktS2bVstXbpUZcqUuWlBAQAAXC+nbwXfsGHDzYgDAADcoOK8CNiVnF5Q3KtXL02dOjXf+LRp0/Tggw+6JCgAAOA8D4trt+LK6eQmPj5eXbp0yTceGRmp+Ph4lwQFAABwvZxuS6Wnp1/2lu+SJUsqLS3NJUEBAADn0ZWycbpyEx4eriVLluQb//TTT1W3bl2XBAUAAHC9nK7cjB07Vj179lRSUpLatWsnSVq3bp0WL16sL774wuUBAgCAgvGgdCPpOpKbbt26admyZZoyZYq++OILeXt7q2HDhlq/fr2CgoJuRowAAKAAnG7HmJTTyY0kde3aVV27dpUkpaWl6ZNPPtHIkSO1bds25ebmujRAAAAAZ1x3khcfH6+oqCiFhobqjTfeULt27bRlyxZXxgYAAJxgsbh2K66cqtykpKRo/vz5+uCDD5SWlqaHHnpImZmZWrZsGYuJAQBwM9bc2BS4ctOtWzfVqlVLO3fu1JtvvqmjR49q1qxZNzM2AAAApxW4cvP9999ryJAhGjRokGrUqHEzYwIAANeBwo1NgSs3mzZt0rlz59S0aVM1b95cb7/9tk6ePHkzYwMAAE7g9Qs2BU5u7rzzTr333ntKTk7W008/rU8//VShoaHKy8vTmjVrdO7cuZsZJwAAQIE4fbeUj4+PnnjiCW3atEm7du3SiBEj9Oqrryo4OFj33XffzYgRAAAUgIfF4tKtuLqh5/3UqlVL06ZN0x9//KFPPvnEVTEBAABct+t6iN9feXp6qkePHurRo4crDgcAAK5DMS62uJRLkhsAAOB+xXkRsCvxGgoAAGAqVG4AADAJiyjdSCQ3AACYBm0pG9pSAADAVKjcAABgElRubKjcAAAAU6FyAwCASVh40I0kkhsAAEyDtpQNbSkAAGAqVG4AADAJulI2JDcAAJhEcX6TtyvRlgIAAKZC5QYAAJNgQbENyQ0AACZBV8qGthQAADAVKjcAAJiEB28Fl0TlBgAAmAyVGwAATII1NzYkNwAAmAR3S9nQlgIAAKZC5QYAAJPgCcU2JDcAAJgEuY0NbSkAAGAqVG4AADAJ2lI2JDcAAJgEuY0NbSkAAGAqVG4AADAJKhY2/A4AAMBUqNwAAGASFhbdSCK5AQDANEhtbGhLAQCAGxYfH69u3bopNDRUFotFy5Ytc9hvGIbGjRunihUrytvbWx06dNCBAwcc5pw+fVp9+vSRv7+/AgMDNWDAAKWnpzsdC8kNAAAm4WGxuHRzRkZGhho2bKh33nnnsvunTZummTNnau7cudq6dat8fHzUqVMnXbx40T6nT58+2r17t9asWaMVK1YoPj5eTz31lNO/g8UwDMPpbxVx57NMd0mAW5Rt/py7QwBM4cL2twvlPIu2/eHS4/Vpeut1fc9iseirr75Sjx49JNmqNqGhoRoxYoRGjhwpSTp79qwqVKig+fPnq3fv3tq7d6/q1q2rn376Sc2aNZMkrVy5Ul26dNEff/yh0NDQAp+fyg0AALipDh06pJSUFHXo0ME+FhAQoObNmyshIUGSlJCQoMDAQHtiI0kdOnSQh4eHtm7d6tT5WFAMAIBJuPpmqczMTGVmZjqMWa1WWa1Wp46TkpIiSapQoYLDeIUKFez7UlJSFBwc7LC/RIkSCgoKss8pKCo3AADgsmJjYxUQEOCwxcbGujusa6JyAwCASbj6OTcxMTEaPny4w5izVRtJCgkJkSQdO3ZMFStWtI8fO3ZMjRo1ss85fvy4w/dycnJ0+vRp+/cLisoNAAAm4eHizWq1yt/f32G7nuSmatWqCgkJ0bp16+xjaWlp2rp1qyIiIiRJERERSk1N1bZt2+xz1q9fr7y8PDVv3typ81G5AQAANyw9PV0HDx60fz506JASExMVFBSkypUra+jQoXr55ZdVo0YNVa1aVWPHjlVoaKj9jqo6deqoc+fOGjhwoObOnavs7GxFR0erd+/eTt0pJZHcAABgGu58/cLPP/+stm3b2j9famdFRUVp/vz5euGFF5SRkaGnnnpKqampuvvuu7Vy5UqVKlXK/p1FixYpOjpa7du3l4eHh3r16qWZM2c6HQvPuQFwRTznBnCNwnrOzeeJR116vAcbOVcxKSpYcwMAAEyFthQAACbBW8FtSG4AADAJ2jE2/A4AAMBUqNwAAGAStKVsqNwAAABToXIDAIBJULexIbkBAMAk6ErZ0JYCAACmQuUGAACT8KAxJYnkBgAA06AtZUNbCgAAmAqVGwAATMJCW0oSlRsAAGAyVG4AADAJ1tzYkNwAAGAS3C1lQ1sKAACYCpUbAABMgraUDckNAAAmQXJjQ1sKAACYCpUbAABMgufc2JDcAABgEh7kNpJoSwEAAJOhcgMAgEnQlrKhcgMAAEyFyg0AACbBreA2JDcAAJgEbSmbItOWSkpK0pgxY/TII4/o+PHjkqTvv/9eu3fvdnNkAACgOCkSyU1cXJzCw8O1detWLV26VOnp6ZKkHTt2aPz48W6ODgCA4sHD4tqtuCoSyc2LL76ol19+WWvWrJGXl5d9vF27dtqyZYsbIwMAoPiwuPif4qpIrLnZtWuXFi9enG88ODhYJ0+edENEKAwfvv+uZr01XY8+1lejRr/k7nCAImHkEx3Vo11D1axSQRcys7V1x2/6x1vLdeD34/Y5Fcr6acrQ+9Xuztry87Fq/+HjmvbBKi1bl2if8/mbT6thzVtUPshPZ9LOa8PWfRozc7mST5x1w1UBhatIVG4CAwOVnJycb3z79u265ZZb3BARbrbdv+zSl18sUY2atdwdClCktGxSXXOXxKt139d176C3VaKEp1bMiVbpUv9f1X5/cl/VrBKsB4f+U80enKLl6xP18dQn1LDWrfY58T/t12OjP1TD+yfp0VHv67ZK5bT4tQHuuCQUIovFtVtxVSSSm969e2v06NFKSUmRxWJRXl6efvjhB40cOVJ9+/Z1d3hwsfPnM/TSiyM1dvxk+fv7uzscoEjpHj1bH3+zVXt/S9Gu/f/VU+M/VuWKQWpct5J9zp0Nb9PsT+P08+7fdfi/pzT1/VVKPXfBYc6sRRv0467DOpJ8Rlt2HNLr89bojvAqKlGiSPxnHzeJxcVbcVUk/lc+ZcoU1a5dW5UqVVJ6errq1q2rVq1a6a677tKYMWPcHR5cLPaVSWrZso3ujLjL3aEARZ6/bylJ0pmz5+1jW3b8pgc6NlUZ/9KyWCx6sFNTlbKWUPzPBy57jDL+pdU7spm27DiknJy8QokbcKcisebGy8tL7733nsaOHatffvlF6enpaty4sWrUqOHu0OBiK7//Vr/u2aOPP/3C3aEARZ7FYtFrIx/Q5u1J2pP0/637x174UAunPqGjcdOUnZ2r8xez9PDw9/TbfxzXKL48pLue6d1KPt5Wbd15SD2HzC3sS0Ah8yjOvSQXKhLJzSWVK1dW5cqVnfpOZmamMjMzHcZyLV6yWq2uDA0ukJKSrNdenaI5737Ivx+gAN6MeUj1qldU+/4zHMbHD75XgX7einx6pk6lZqhbmwb6eNoT6vDEm9p98Kh93oyP1mr+sgRVrhikfzwdqfcnP06Cg78FtyU3w4cPL/Dc6dOnX3FfbGysJk6c6DD20phx+sfYCdcbGm6Svbt36/TpU3r04Z72sdzcXP17289a8skibd22U56enm6MECg6Zox+UF1a1leHAW/qv8dT7eNVby2nQb1bq0mvl7X3txRJ0q79/1WLJtX09MOtNOSVT+1zT6Vm6FRqhg4eOa59h1J0cNXLat6gqrbuPFTYl4NCQt3Gxm3Jzfbt2ws0z3KNEltMTEy+RCnX4nWF2XCnO+68U58v/dphbPzYl1S16m3q98STJDbA/8wY/aDua9dQHQe+pd+PnnLYd+muqTzDcBjPzTWu2pLw+N8T2bxKFqmCPVyN7EaSG5ObDRs2uOQ4Vqs1X4vjfJZxhdlwJx8fX1WvUdNhzNvbWwGBgfnGgb+rN2Me0sORzfTgsHeVnnFRFcr6SZLOpl/Uxcxs7TucooNHjuvtMY8oZvpXOnU2Q/e1baD2d9ZSz+dtLafb64epab0wbd6epNRz51X11vIa/2xXJR05QdUGfwuk8ABQhDz9UCtJ0pr3hzqMDxy3UB9/s1U5OXnq8dwcvTyku75462n5lrYq6T8n9OS4hVq1aY8k6fzFbHVv11BjnukqH28vpZw8q9Wb92rqex8qKzunsC8Jhag4P1XYlSyGYbilzNGzZ0/Nnz9f/v7+uv/++6/aflq6dKlTx6ZyA7hG2ebPuTsEwBQubH+7UM7z42+ufQL1HbcFuPR4hcVtlZuAgAB7QhMYGCiLxSI35VkAAMBE3JbczJs3T7m5uZo6dar279+vrKwstWvXThMmTJC3t7e7wgIAoNiiKWXj1icUT5kyRS+99JJ8fX11yy23aObMmRo8eLA7QwIAAMWcW5Objz76SLNnz9aqVau0bNkyffPNN1q0aJHy8ng8OAAATuPlUpLcnNwcOXJEXbp0sX/u0KGDLBaLjh49epVvAQCAy7G4+J/iyq3JTU5OjkqVKuUwVrJkSWVnZ7spIgAAUNy59Tk3hmGoX79+Dg/hu3jxop555hn5+PjYx5y9FRwAgL8j3ptp49bkJioqKt/YY4895oZIAAAo/shtbNya3MybN8+dpwcAACbE6xcAADALSjeSSG4AADCN4nyHkyu59W4pAAAAV6NyAwCASXC3lA2VGwAAYCpUbgAAMAkKNzYkNwAAmAXZjSTaUgAAwGSo3AAAYBLcCm5DcgMAgElwt5QNbSkAAGAqJDcAAJiExcWbMyZMmCCLxeKw1a5d277/4sWLGjx4sMqWLStfX1/16tVLx44du4GrvTKSGwAAzMKd2Y2kevXqKTk52b5t2rTJvm/YsGH65ptv9PnnnysuLk5Hjx5Vz549r/dKr4o1NwAAwCVKlCihkJCQfONnz57VBx98oMWLF6tdu3aSpHnz5qlOnTrasmWL7rzzTpfGQeUGAACTsLj4n8zMTKWlpTlsmZmZVzz/gQMHFBoaqttuu019+vTRkSNHJEnbtm1Tdna2OnToYJ9bu3ZtVa5cWQkJCS7/HUhuAADAZcXGxiogIMBhi42Nvezc5s2ba/78+Vq5cqXmzJmjQ4cOqWXLljp37pxSUlLk5eWlwMBAh+9UqFBBKSkpLo+bthQAACbh6lvBY2JiNHz4cIcxq9V62bmRkZH2Pzdo0EDNmzdXWFiYPvvsM3l7e7s2sGsguQEAwCRc/Zgbq9V6xWTmWgIDA1WzZk0dPHhQ99xzj7KyspSamupQvTl27Nhl1+jcKNpSAADA5dLT05WUlKSKFSuqadOmKlmypNatW2ffv2/fPh05ckQREREuPzeVGwAAzMKNTygeOXKkunXrprCwMB09elTjx4+Xp6enHnnkEQUEBGjAgAEaPny4goKC5O/vr+eee04REREuv1NKIrkBAMA03PluqT/++EOPPPKITp06pfLly+vuu+/Wli1bVL58eUnSjBkz5OHhoV69eikzM1OdOnXS7Nmzb0osFsMwjJtyZDc6n2W6SwLcomzz59wdAmAKF7a/XSjn+TX5vEuPV7tiaZcer7BQuQEAwCR4caYNC4oBAICpULkBAMAkKNzYkNwAAGAWZDeSaEsBAACToXIDAIBJuPNW8KKE5AYAAJPgbikb2lIAAMBUqNwAAGASFG5sSG4AADALshtJtKUAAIDJULkBAMAkuFvKhsoNAAAwFSo3AACYBLeC25DcAABgEuQ2NrSlAACAqVC5AQDALCjdSCK5AQDANLhbyoa2FAAAMBUqNwAAmAR3S9mQ3AAAYBLkNja0pQAAgKlQuQEAwCRoS9lQuQEAAKZC5QYAANOgdCOR3AAAYBq0pWxoSwEAAFOhcgMAgElQuLEhuQEAwCRoS9nQlgIAAKZC5QYAAJPgxZk2VG4AAICpULkBAMAsKNxIIrkBAMA0yG1saEsBAABToXIDAIBJcCu4DckNAAAmwd1SNrSlAACAqVC5AQDALCjcSCK5AQDANMhtbGhLAQAAU6FyAwCASXC3lA2VGwAAYCpUbgAAMAluBbchuQEAwCRoS9nQlgIAAKZCcgMAAEyFthQAACZBW8qGyg0AADAVKjcAAJgEd0vZULkBAACmQuUGAACTYM2NDckNAAAmQW5jQ1sKAACYCpUbAADMgtKNJJIbAABMg7ulbGhLAQAAU6FyAwCASXC3lA3JDQAAJkFuY0NbCgAAmArJDQAAZmFx8XYd3nnnHVWpUkWlSpVS8+bN9eOPP97ABV0fkhsAAOASS5Ys0fDhwzV+/Hj9+9//VsOGDdWpUycdP368UOMguQEAwCQsLv7HWdOnT9fAgQPVv39/1a1bV3PnzlXp0qX14Ycf3oSrvTKSGwAATMJice3mjKysLG3btk0dOnSwj3l4eKhDhw5KSEhw8ZVeHXdLAQCAy8rMzFRmZqbDmNVqldVqzTf35MmTys3NVYUKFRzGK1SooF9//fWmxvlXpkxuSntxM1xRl5mZqdjYWMXExFz2LwmKhgvb33Z3CLgK/h7hr0q5+P/VJ7wcq4kTJzqMjR8/XhMmTHDtiVzMYhiG4e4g8PeTlpamgIAAnT17Vv7+/u4OByiW+HuEm82Zyk1WVpZKly6tL774Qj169LCPR0VFKTU1VcuXL7/Z4dqx5gYAAFyW1WqVv7+/w3alKqGXl5eaNm2qdevW2cfy8vK0bt06RUREFFbIkkzalgIAAIVv+PDhioqKUrNmzXTHHXfozTffVEZGhvr371+ocZDcAAAAl3j44Yd14sQJjRs3TikpKWrUqJFWrlyZb5HxzUZyA7ewWq0aP348iyCBG8DfIxRF0dHRio6OdmsMLCgGAACmwoJiAABgKiQ3AADAVEhuUGwcPnxYFotFiYmJkqSNGzfKYrEoNTXVrXEBhalfv34OzxBp06aNhg4d6rZ4gKKI5AYO+vXrJ4vFoldffdVhfNmyZbI4+6IRJ11KXi5tZcuWVceOHbV9+3ZJUqVKlZScnKz69evf1DgAd7v099BiscjLy0vVq1fXpEmTlJOTo7feekvz5893d4hAkUZyg3xKlSqlqVOn6syZM245/9q1a5WcnKxVq1YpPT1dkZGRSk1Nlaenp0JCQlSiBDf5wfw6d+6s5ORkHThwQCNGjNCECRP02muvKSAgQIGBge4ODyjSSG6QT4cOHRQSEqLY2Ngrzvnyyy9Vr149Wa1WValSRW+88YbD/ipVqmjKlCl64okn5Ofnp8qVK+vdd98t0PnLli2rkJAQNWvWTK+//rqOHTumrVu35mtLXc6mTZvUsmVLeXt7q1KlShoyZIgyMjIKdF6gKLFarQoJCVFYWJgGDRqkDh066Ouvv87XlvqrzMxMjRw5Urfccot8fHzUvHlzbdy4sdDiBooCkhvk4+npqSlTpmjWrFn6448/8u3ftm2bHnroIfXu3Vu7du3ShAkTNHbs2Hyl8jfeeEPNmjXT9u3b9eyzz2rQoEHat2+fU7F4e3tLsr2z5FqSkpLUuXNn9erVSzt37tSSJUu0adMmtz9vAXAFb2/vAv09iI6OVkJCgj799FPt3LlTDz74oDp37qwDBw4UQpRA0UByg8u6//771ahRI40fPz7fvunTp6t9+/YaO3asatasqX79+ik6Olqvvfaaw7wuXbro2WefVfXq1TV69GiVK1dOGzZsKHAMqampmjx5snx9fXXHHXdcc35sbKz69OmjoUOHqkaNGrrrrrs0c+ZMffTRR7p48WKBzwsUJYZhaO3atVq1apXatWt31blHjhzRvHnz9Pnnn6tly5aqVq2aRo4cqbvvvlvz5s0rpIgB9yO5wRVNnTpVCxYs0N69ex3G9+7dqxYtWjiMtWjRQgcOHFBubq59rEGDBvY/WywWhYSE6Pjx45KkyMhI+fr6ytfXV/Xq1XM41l133SVfX1+VKVNGO3bs0JIlSwr06O4dO3Zo/vz59uP6+vqqU6dOysvL06FDh5y+fsCdVqxYIV9fX5UqVUqRkZF6+OGHNWHChKt+Z9euXcrNzVXNmjUd/h7ExcUpKSmpcAIHigBWZuKKWrVqpU6dOikmJkb9+vVz+vslS5Z0+GyxWJSXlydJev/993XhwoXLzluyZInq1q2rsmXLOrVwMj09XU8//bSGDBmSb1/lypWdjB5wr7Zt22rOnDny8vJSaGhogRbSp6eny9PTU9u2bZOnp6fDPl9f35sVKlDkkNzgql599VU1atRItWrVso/VqVNHP/zwg8O8H374QTVr1sz3H9QrueWWW664r1KlSqpWrZrTsTZp0kR79uxR9erVnf4uUNT4+Pg4/b/lxo0bKzc3V8ePH1fLli1vUmRA0UdbClcVHh6uPn36aObMmfaxESNGaN26dZo8ebL279+vBQsW6O2339bIkSPdGKk0evRobd68WdHR0UpMTNSBAwe0fPlyFhTjb6NmzZrq06eP+vbtq6VLl+rQoUP68ccfFRsbq2+//dbd4QGFhuQG1zRp0iR7O0myVUg+++wzffrpp6pfv77GjRunSZMmXVfrypUaNGiguLg47d+/Xy1btlTjxo01btw4hYaGujUuoDDNmzdPffv21YgRI1SrVi316NFDP/30E61Z/K3wVnAAAGAqVG4AAICpkNwAAABTIbkBAACmQnIDAABMheQGAACYCskNAAAwFZIbAABgKiQ3AADAVEhuAEiS+vXrpx49etg/t2nTRkOHDi30ODZu3CiLxaLU1NRCPzcAcyC5AYq4fv36yWKxyGKxyMvLS9WrV9ekSZOUk5NzU8+7dOlSTZ48uUBzSUgAFCW8FRwoBjp37qx58+YpMzNT3333nQYPHqySJUsqJibGYV5WVpa8vLxccs6goCCXHAcAChuVG6AYsFqtCgkJUVhYmAYNGqQOHTro66+/treSXnnlFYWGhqpWrVqSpP/85z966KGHFBgYqKCgIHXv3l2HDx+2Hy83N1fDhw9XYGCgypYtqxdeeEF/fc3cX9tSmZmZGj16tCpVqiSr1arq1avrgw8+0OHDh9W2bVtJUpkyZWSxWOwvUc3Ly1NsbKyqVq0qb29vNWzYUF988YXDeb777jvVrFlT3t7eatu2rUOcAHA9SG6AYsjb21tZWVmSpHXr1mnfvn1as2aNVqxYoezsbHXq1El+fn7617/+pR9++EG+vr7q3Lmz/TtvvPGG5s+frw8//FCbNm3S6dOn9dVXX131nH379tUnn3yimTNnau/evfrnP/8pX19fVapUSV9++aUkad++fUpOTtZbb70lSYqNjdVHH32kuXPnavfu3Ro2bJgee+wxxcXFSbIlYT179lS3bt2UmJioJ598Ui+++OLN+tkA/F0YAIq0qKgoo3v37oZhGEZeXp6xZs0aw2q1GiNHjjSioqKMChUqGJmZmfb5CxcuNGrVqmXk5eXZxzIzMw1vb29j1apVhmEYRsWKFY1p06bZ92dnZxu33nqr/TyGYRitW7c2nn/+ecMwDGPfvn2GJGPNmjWXjXHDhg2GJOPMmTP2sYsXLxqlS5c2Nm/e7DB3wIABxiOPPGIYhmHExMQYdevWddg/evTofMcCAGew5gYoBlasWCFfX19lZ2crLy9Pjz76qCZMmKDBgwcrPDzcYZ3Njh07dPDgQfn5+Tkc4+LFi0pKStLZs2eVnJys5s2b2/eVKFFCzZo1y9eauiQxMVGenp5q3bp1gWM+ePCgzp8/r3vuucdhPCsrS40bN5Yk7d271yEOSYqIiCjwOQDgckhugGKgbdu2mjNnjry8vBQaGqoSJf7/r66Pj4/D3PT0dDVt2lSLFi3Kd5zy5ctf1/m9vb2d/k56erok6dtvv9Utt9zisM9qtV5XHABQECQ3QDHg4+Oj6tWrF2hukyZNtGTJEgUHB8vf3/+ycypWrKitW7eqVatWkqScnBxt27ZNTZo0uez88PBw5eXlKS4uTh06dMi3/1LlKDc31z5Wt25dWa1WHTly5IoVnzp16ujrr792GNuyZcu1LxIAroIFxYDJ9OnTR+XKlVP37t31r3/9S4cOHdLGjRs1ZMgQ/fHHH5Kk559/Xq+++qqWLVumX3/9Vc8+++xVn1FTpUoVRUVF6YknntCyZcvsx/zss88kSWFhYbJYLFqxYoVOnDih9PR0+fn5aeTIkRo2bJgWLFigpKQk/fvf/9asWbO0YMECSdIzzzyjAwcOaNSoUdq3b58WL16s+fPn3+yfCIDJkdwAJlO6dGnFx8ercuXK6tmzp+rUqaMBAwbo4sWL9krOiBEj9PjjjysqKkoRERHy8/PT/ffff9XjzpkzRw888ICeffZZ1a5dWwMHDlRGRoYk6ZZbbtHEiRP14osvqkKFCoqOjpYkTZ48WWPHjlVsbKzq1Kmjzp0769tvv1XVqlUlSZUrV9aXX36pZcuWqWHDhpo7d66mTJlyE38dAH8HFuNKKwgBAACKISo3AADAVEhuAACAqZDcAAAAUyG5AQAApkJyAwAATIXkBgAAmArJDQAAMBWSGwAAYCokNwAAwFRIbgAAgKmQ3AAAAFMhuQEAAKbyfzztCK0L+ij6AAAAAElFTkSuQmCC\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle\n", "from sklearn.metrics import accuracy_score\n", "\n", "def create_pointnet_spatial_plot_correct_way(all_preds, all_targets, test_pkl_path, results_path, metrics):\n", "    \"\"\"Create PointNet++ spatial plot using the same method as classical ML models.\"\"\"\n", "\n", "    # Load reference data\n", "    ifc_df = pd.read_csv(ifc_path)\n", "    ifc_coords = ifc_df[['X', 'Y']].values\n", "\n", "    gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "    gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)\n", "    gdf_kml['geometry'] = gdf_kml.geometry.centroid\n", "    kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)\n", "\n", "    # Load the original pile dataset\n", "    pile_df = pd.read_csv(harmonized_pile_dataset)\n", "\n", "    # Load test metadata\n", "    with open(test_pkl_path, 'rb') as f:\n", "        test_data = pickle.load(f)\n", "    test_metadata = test_data.get('metadata', [])\n", "\n", "    # Reconstruct coordinates using pile IDs\n", "    pred_coords = []\n", "    valid_indices = []\n", "\n", "    for i, meta in enumerate(test_metadata[:len(all_preds)]):\n", "        pile_id = meta.get('pile_id') if isinstance(meta, dict) else None\n", "\n", "        if pile_id and pile_id in pile_df['pile_id'].values:\n", "            pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]\n", "            pred_coords.append([pile_row['x'], pile_row['y']])\n", "            valid_indices.append(i)\n", "\n", "    if len(pred_coords) == 0:\n", "        print(\"ERROR: No pile IDs found in metadata!\")\n", "        return\n", "\n", "    pred_coords = np.array(pred_coords)\n", "\n", "    # Filter predictions to match valid coordinates\n", "    all_preds_filtered = [all_preds[i] for i in valid_indices]\n", "    all_targets_filtered = [all_targets[i] for i in valid_indices]\n", "\n", "    # Calculate accuracy for SPATIAL subset (for display purposes)\n", "    correct_mask = (np.array(all_targets_filtered) == np.array(all_preds_filtered))\n", "    error_mask = ~correct_mask\n", "    spatial_accuracy = np.mean(correct_mask)\n", "\n", "    # Use the TRUE accuracy from full test set for title\n", "    true_accuracy = metrics['accuracy']\n", "\n", "    print(f\"Reconstructed {len(pred_coords)} coordinates from pile IDs\")\n", "    print(f\"Spatial subset accuracy: {spatial_accuracy:.3f} ({len(pred_coords)} samples)\")\n", "    print(f\"Full test set accuracy: {true_accuracy:.3f} ({len(all_preds)} samples)\")\n", "    print(f\"Coordinate range: X[{pred_coords[:, 0].min():.0f}, {pred_coords[:, 0].max():.0f}], Y[{pred_coords[:, 1].min():.0f}, {pred_coords[:, 1].max():.0f}]\")\n", "\n", "    # Create plot exactly like classical ML models\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "\n", "    # Plot reference data\n", "    ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1],\n", "              s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')\n", "    ax.scatter(kml_coords[:, 0], kml_coords[:, 1],\n", "              s=8, alpha=0.7, color='green', marker='s', label='KML Ground Truth')\n", "\n", "    # Plot predictions\n", "    if np.sum(correct_mask) > 0:\n", "        ax.scatter(pred_coords[correct_mask, 0], pred_coords[correct_mask, 1],\n", "                  c='blue', s=30, alpha=0.8, marker='o',\n", "                  label=f'Correct ({np.sum(correct_mask)})')\n", "\n", "    if np.sum(error_mask) > 0:\n", "        ax.scatter(pred_coords[error_mask, 0], pred_coords[error_mask, 1],\n", "                  c='red', s=50, alpha=0.9, marker='x', linewidth=2,\n", "                  label=f'Errors ({np.sum(error_mask)})')\n", "\n", "    # Use TRUE accuracy in title, but show spatial stats in legend\n", "    ax.set_title(f'PointNet++\\nAccuracy: {true_accuracy:.3f}')\n", "    ax.set_xlabel('X (UTM)')\n", "    ax.set_ylabel('Y (UTM)')\n", "    ax.legend(fontsize=8)\n", "    ax.grid(True, alpha=0.3)\n", "    ax.set_aspect('equal', adjustable='box')\n", "\n", "    # Add text box with full stats\n", "    stats_text = f'Full Test Set:\\n{len(all_preds)} samples\\n{true_accuracy:.1%} accuracy\\n\\nSpatial Subset:\\n{len(pred_coords)} samples\\n{spatial_accuracy:.1%} accuracy'\n", "    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,\n", "            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "\n", "    plt.tight_layout()\n", "    plt.savefig(results_path / 'pointnet_spatial_final_corrected.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n", "\n", "    return {\n", "        'spatial_accuracy': spatial_accuracy,\n", "        'spatial_samples': len(pred_coords),\n", "        'true_accuracy': true_accuracy,\n", "        'total_samples': len(all_preds)\n", "    }\n", "\n", "spatial_results = create_pointnet_spatial_plot_correct_way(\n", "    all_preds, all_targets, test_pkl_path, results_path, metrics_dict\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 878}, "id": "xzI8uDdPpk2E", "outputId": "9c721479-6495-4dba-ef4f-e4a28995960b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reconstructed 287 coordinates from pile IDs\n", "Spatial subset accuracy: 0.986 (287 samples)\n", "Full test set accuracy: 0.677 (418 samples)\n", "Coordinate range: X[435305, 436682], Y[5010922, 5012461]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x800 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## Save Results"], "metadata": {"id": "kdyPX9zcXDpt"}}, {"cell_type": "code", "source": ["def save_results(results_path, metrics, history, config, best_val_acc, training_time, preds, targets):\n", "    results = {\n", "        'iteration': 1,\n", "        'model': 'Basic PointNet++',\n", "        'architecture': 'Simplified layers with k-NN grouping',\n", "        'training_time': training_time,\n", "        'best_val_acc': best_val_acc,\n", "        'test_metrics': metrics,\n", "        'config': config,\n", "        'training_history': history,\n", "        'predictions': preds,\n", "        'ground_truth': targets\n", "    }\n", "\n", "    with open(results_path / 'pointnet_knn_classification.json', 'w') as f:\n", "        json.dump(results, f, indent=2, default=str)\n", "\n", "    print(f\"Results saved to: {results_path / 'pointnet_knn_classification.json'}\")\n", "\n", "save_results(results_path, metrics_dict, history, config, best_val_acc, training_time, all_preds, all_targets)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "m86-RdTDXE5-", "outputId": "94780bd6-ad34-4379-935d-6c209bc7b13f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Results saved to: /content/drive/MyDrive/pointnet_pile_detection/results_iter1/pointnet_knn_classification.json\n"]}]}]}