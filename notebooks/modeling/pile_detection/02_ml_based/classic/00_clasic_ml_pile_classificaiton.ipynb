import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[3] if '__file__' in globals() else Path.cwd().parents[3]
print(f"Notebooks root: {notebooks_root}")

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import (
    get_data_path,
    get_output_path,
    get_mlflow_tracking_uri,
    find_latest_file,
    get_processed_data_path
)


# Papermill parameters - these will be injected by <PERSON><PERSON>
from datetime import datetime

site_name = "trino_enel"  # Site name for output file naming
project_type = "ENEL"  # Options: "ENEL", "USA"
method_name = "classical_ml"  # Method identifier for this analysis
timestamp = None  # Auto-generated if None
model_types = ["random_forest", "gradient_boosting", "svm", "logistic_regression"]  # Models to train

# Generate timestamp if not provided
if timestamp is None:
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Standardized paths using your path functions
input_data_path = get_processed_data_path(site_name, "modelling")
output_path = get_processed_data_path(site_name, f"modelling/{method_name}")

# Create output directories
for folder in ["models", "predictions", "figures","kmls", "results"]:
    (output_path / folder).mkdir(parents=True, exist_ok=True)

mlflow_tracking_uri = get_mlflow_tracking_uri()

coordinate_system = "EPSG:32632"

REFERENCE_PATHS = {
    'pile_dataset': get_processed_data_path(site_name, f'ml_patch_data/harmonized_pile_dataset_final.csv'),
    'ifc_metadata': get_processed_data_path(site_name, f'ifc_metadata/{site_name}_enhanced_metadata.csv'),
    'kml_ground_truth': get_data_path(site_name)/'kml/pile.kml'
}

print(f"Input data path: {input_data_path}")
print(f"Output path: {output_path}")
print(f"MLflow tracking URI: {mlflow_tracking_uri}")
print(f"Coordinate system: {coordinate_system}")
print(f"Reference paths: {REFERENCE_PATHS}")
print(f"Model types: {model_types}")


import numpy as np
import pandas as pd
import pickle
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import geopandas as gpd
from shapely.geometry import Point
import joblib
import json

import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib and pandas
plt.style.use('default')
pd.set_option('display.max_columns', None)

def calculate_feature_vector(x_real, y_real, z_real, height_real, dist_real, density_real):
    """Calculate 25-dimensional feature vector from valid points."""
    
    return [
        # Basic counts and density
        len(x_real),                    # Number of real points
        len(x_real) / 1024,            # Density ratio
        
        # Height statistics (normalized)
        np.mean(height_real),
        np.std(height_real),
        np.min(height_real),
        np.max(height_real),
        np.percentile(height_real, 75) - np.percentile(height_real, 25),  # IQR
        
        # Z-coordinate statistics (relative to pile)
        np.mean(z_real),
        np.std(z_real),
        np.min(z_real),
        np.max(z_real),
        
        # Distance statistics (normalized)
        np.mean(dist_real),
        np.std(dist_real),
        np.min(dist_real),
        np.max(dist_real),
        
        # Spatial distribution
        np.std(x_real),
        np.std(y_real),
        np.corrcoef(x_real, y_real)[0, 1] if len(x_real) > 1 and np.std(x_real) > 0 and np.std(y_real) > 0 else 0,
        
        # Density features
        np.mean(density_real),
        np.std(density_real),
        
        # Shape analysis (using normalized distances)
        np.sum(dist_real < 0.3) / len(dist_real),      # Close points
        np.sum((dist_real >= 0.3) & (dist_real < 0.7)) / len(dist_real),  # Medium points
        np.sum(dist_real >= 0.7) / len(dist_real),     # Far points
        
        # Vertical structure indicators
        np.sum(height_real > np.mean(height_real) + 0.5 * np.std(height_real)) / len(height_real) if np.std(height_real) > 0 else 0,
        
        # Radial symmetry (pile indicator)
        np.std(np.sqrt(x_real**2 + y_real**2)) / np.mean(np.sqrt(x_real**2 + y_real**2)) if np.mean(np.sqrt(x_real**2 + y_real**2)) > 0 else 0
    ]

def extract_patch_features(patches):
    """
    Extract comprehensive features from point cloud patches.
    """
    features = []
    
    for patch in patches:
        # Extract coordinates and attributes
        x_rel = patch[:, 0]          # Relative X
        y_rel = patch[:, 1]          # Relative Y  
        z_rel = patch[:, 2]          # Relative Z
        height_norm = patch[:, 3]    # Normalized height
        distance_norm = patch[:, 4]  # Normalized distance
        density = patch[:, 5]        # Density
        
        # Filter out padded points
        valid_mask = (np.abs(x_rel) + np.abs(y_rel) + np.abs(z_rel)) > 1e-6
        
        # Apply mask to get real points only
        x_real = x_rel[valid_mask]
        y_real = y_rel[valid_mask]
        z_real = z_rel[valid_mask]
        height_real = height_norm[valid_mask]
        dist_real = distance_norm[valid_mask]
        density_real = density[valid_mask]
        
        # Handle edge case of no valid points
        if len(x_real) == 0:
            feature_vector = [0] * 25
        else:
            # Calculate comprehensive features
            feature_vector = calculate_feature_vector(
                x_real, y_real, z_real, height_real, dist_real, density_real
            )
        
        features.append(feature_vector)
    
    return features

def load_and_extract_features():
    """
    Load PointNet patches and extract classical ML features.
    """
    datasets = {}
    
    file_mapping = {
        'train': 'train_pointnet.pkl',
        'val': 'val_pointnet.pkl', 
        'test': 'test_pointnet.pkl'
    }
    
    for split, filename in file_mapping.items():
        filepath = input_data_path/"ml_patch_data" / filename
        
        print(f"Loading {filepath}...")
        with open(filepath, 'rb') as f:
            data = pickle.load(f)
        
        patches = data['points']  # (N, 1024, 6)
        labels = data['labels']   # (N,)
        metadata = data.get('metadata', [])
        
        print(f"Loaded {split}: {patches.shape}, labels: {len(labels)}")
        
        # Extract aggregate features from each patch
        features = extract_patch_features(patches)
        
        datasets[split] = {
            'features': np.array(features),
            'labels': labels,
            'metadata': metadata,
            'n_samples': len(features)
        }
        
        print(f"Extracted features for {split}: {datasets[split]['features'].shape}")
    
    return datasets


def extract_patch_features(patches):
    """
    Extract comprehensive features from point cloud patches.
    """
    features = []
    
    for patch in patches:
        # Extract coordinates and attributes
        x_rel = patch[:, 0]          # Relative X
        y_rel = patch[:, 1]          # Relative Y  
        z_rel = patch[:, 2]          # Relative Z
        height_norm = patch[:, 3]    # Normalized height
        distance_norm = patch[:, 4]  # Normalized distance
        density = patch[:, 5]        # Density
        
        # Filter out padded points
        valid_mask = (np.abs(x_rel) + np.abs(y_rel) + np.abs(z_rel)) > 1e-6
        
        # Apply mask to get real points only
        x_real = x_rel[valid_mask]
        y_real = y_rel[valid_mask]
        z_real = z_rel[valid_mask]
        height_real = height_norm[valid_mask]
        dist_real = distance_norm[valid_mask]
        density_real = density[valid_mask]
        
        # Handle edge case of no valid points
        if len(x_real) == 0:
            feature_vector = [0] * 25
        else:
            # Calculate comprehensive features
            feature_vector = calculate_feature_vector(
                x_real, y_real, z_real, height_real, dist_real, density_real
            )
        
        features.append(feature_vector)
    
    return features



def calculate_feature_vector(x_real, y_real, z_real, height_real, dist_real, density_real):
    """Calculate 25-dimensional feature vector from valid points."""
    
    return [
        # Basic counts and density
        len(x_real),                    # Number of real points
        len(x_real) / 1024,            # Density ratio
        
        # Height statistics (normalized)
        np.mean(height_real),
        np.std(height_real),
        np.min(height_real),
        np.max(height_real),
        np.percentile(height_real, 75) - np.percentile(height_real, 25),  # IQR
        
        # Z-coordinate statistics (relative to pile)
        np.mean(z_real),
        np.std(z_real),
        np.min(z_real),
        np.max(z_real),
        
        # Distance statistics (normalized)
        np.mean(dist_real),
        np.std(dist_real),
        np.min(dist_real),
        np.max(dist_real),
        
        # Spatial distribution
        np.std(x_real),
        np.std(y_real),
        np.corrcoef(x_real, y_real)[0, 1] if len(x_real) > 1 and np.std(x_real) > 0 and np.std(y_real) > 0 else 0,
        
        # Density features
        np.mean(density_real),
        np.std(density_real),
        
        # Shape analysis (using normalized distances)
        np.sum(dist_real < 0.3) / len(dist_real),      # Close points
        np.sum((dist_real >= 0.3) & (dist_real < 0.7)) / len(dist_real),  # Medium points
        np.sum(dist_real >= 0.7) / len(dist_real),     # Far points
        
        # Vertical structure indicators
        np.sum(height_real > np.mean(height_real) + 0.5 * np.std(height_real)) / len(height_real) if np.std(height_real) > 0 else 0,
        
        # Radial symmetry (pile indicator)
        np.std(np.sqrt(x_real**2 + y_real**2)) / np.mean(np.sqrt(x_real**2 + y_real**2)) if np.mean(np.sqrt(x_real**2 + y_real**2)) > 0 else 0
    ]


print("Feature extraction functions defined")

# Load and extract features
print("Loading PointNet data and extracting features...")
datasets = load_and_extract_features()

# Feature names for interpretation
feature_names = [
    'real_point_count', 'point_density_ratio',
    'height_mean', 'height_std', 'height_min', 'height_max', 'height_iqr',
    'z_rel_mean', 'z_rel_std', 'z_rel_min', 'z_rel_max',
    'dist_norm_mean', 'dist_norm_std', 'dist_norm_min', 'dist_norm_max',
    'x_rel_std', 'y_rel_std', 'xy_correlation',
    'density_mean', 'density_std',
    'close_point_ratio', 'medium_point_ratio', 'far_point_ratio',
    'high_point_ratio', 'radial_symmetry'
]

print(f"Feature names ({len(feature_names)}): {feature_names}")

# Prepare data arrays
X_train = datasets['train']['features']
y_train = datasets['train']['labels']
X_val = datasets['val']['features']
y_val = datasets['val']['labels']
X_test = datasets['test']['features']
y_test = datasets['test']['labels']

# Handle any NaN values
X_train = np.nan_to_num(X_train)
X_val = np.nan_to_num(X_val)
X_test = np.nan_to_num(X_test)

# Scale features for algorithms that need it
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test_scaled = scaler.transform(X_test)

print(f"\nData shapes:")
print(f"Training: {X_train.shape}")
print(f"Validation: {X_val.shape}")
print(f"Test: {X_test.shape}")

print(f"\nClass distribution:")
print(f"Training: {np.bincount(y_train)} (pos/neg)")
print(f"Validation: {np.bincount(y_val)} (pos/neg)")
print(f"Test: {np.bincount(y_test)} (pos/neg)")

# Define models to compare
models = {
    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10),
    'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42, max_depth=6),
    'SVM': SVC(random_state=42, probability=True),
    'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
}

results = {}

for name, model in models.items():
    print(f"\nTraining {name}...")
    
    # Use scaled data for SVM and Logistic Regression, raw for tree-based
    if name in ['SVM', 'Logistic Regression']:
        X_tr, X_v, X_te = X_train_scaled, X_val_scaled, X_test_scaled
    else:
        X_tr, X_v, X_te = X_train, X_val, X_test
    
    # Train model
    model.fit(X_tr, y_train)
    
    # Generate predictions
    y_train_pred = model.predict(X_tr)
    y_val_pred = model.predict(X_v)
    y_test_pred = model.predict(X_te)
    
    # Get prediction probabilities for AUC
    y_train_proba = model.predict_proba(X_tr)[:, 1]
    y_val_proba = model.predict_proba(X_v)[:, 1]
    y_test_proba = model.predict_proba(X_te)[:, 1]
    
    # Calculate metrics
    train_acc = np.mean(y_train_pred == y_train)
    val_acc = np.mean(y_val_pred == y_val)
    test_acc = np.mean(y_test_pred == y_test)
    
    train_auc = roc_auc_score(y_train, y_train_proba)
    val_auc = roc_auc_score(y_val, y_val_proba)
    test_auc = roc_auc_score(y_test, y_test_proba)
    
    # Store results
    results[name] = {
        'train_acc': train_acc, 'val_acc': val_acc, 'test_acc': test_acc,
        'train_auc': train_auc, 'val_auc': val_auc, 'test_auc': test_auc,
        'model': model, 'test_predictions': y_test_pred, 'test_probabilities': y_test_proba
    }
    
    print(f"{name} Results:")
    print(f"  Train Acc: {train_acc:.3f}, Val Acc: {val_acc:.3f}, Test Acc: {test_acc:.3f}")
    print(f"  Train AUC: {train_auc:.3f}, Val AUC: {val_auc:.3f}, Test AUC: {test_auc:.3f}")

# Find best model
best_model_name = max(results.keys(), key=lambda x: results[x]['test_acc'])
best_model = results[best_model_name]['model']

print("MODEL COMPARISON SUMMARY")
print("="*60)
print(f"Best model: {best_model_name}")
print(f"Test accuracy: {results[best_model_name]['test_acc']:.3f}")
print(f"Test AUC: {results[best_model_name]['test_auc']:.3f}")

# Feature importance analysis for tree-based models
if best_model_name in ['Random Forest', 'Gradient Boosting']:
    feature_importance = best_model.feature_importances_
    
    # Plot feature importance
    plt.figure(figsize=(12, 8))
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=True)
    
    plt.barh(range(len(importance_df)), importance_df['importance'])
    plt.yticks(range(len(importance_df)), importance_df['feature'])
    plt.xlabel('Feature Importance')
    plt.title(f'Feature Importance - {best_model_name}')
    plt.tight_layout()
    plt.show()

# Generate confusion matrix
best_results = results[best_model_name]
y_pred_best = best_results['test_predictions']

plt.figure(figsize=(8, 6))
cm = confusion_matrix(y_test, y_pred_best)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['No Pile', 'Pile'], yticklabels=['No Pile', 'Pile'])
plt.title(f'Confusion Matrix - {best_model_name}')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

# Print classification report
print(f"Classification Report - {best_model_name}:")
print(classification_report(y_test, y_pred_best, target_names=['No Pile', 'Pile']))

def reconstruct_coordinates_from_pile_ids():
    """
    Reconstruct spatial coordinates from pile IDs in test metadata.
    """
    try:
        # Load original pile dataset
        # TODO: fix path

        pile_df = pd.read_csv(f'{input_data_path}/harmonized_pile_dataset_final.csv')
        print(f"Loaded original pile dataset: {len(pile_df)} samples")
        
        # Load test metadata
        test_metadata = datasets['test']['metadata']
        labels = datasets['test']['labels']
        
        # Reconstruct coordinates by matching pile IDs
        reconstructed_coords = []
        
        for i, (meta, label) in enumerate(zip(test_metadata, labels)):
            pile_id = meta.get('pile_id') if isinstance(meta, dict) else None
            
            if pile_id and pile_id in pile_df['pile_id'].values:
                pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]
                reconstructed_coords.append({
                    'sample_idx': i,
                    'x': pile_row['x'],
                    'y': pile_row['y'],
                    'z': pile_row.get('z', 0),
                    'pile_id': pile_id,
                    'actual_label': label
                })
            else:
                # Use placeholder coordinates for samples without matches
                reconstructed_coords.append({
                    'sample_idx': i, 'x': 0, 'y': 0, 'z': 0,
                    'pile_id': f'unknown_{i}', 'actual_label': label
                })
        
        coords_df = pd.DataFrame(reconstructed_coords)
        
        # Filter out zero coordinates
        valid_coords_df = coords_df[(coords_df['x'] != 0) | (coords_df['y'] != 0)]
        
        print(f"Reconstructed coordinates: {len(valid_coords_df)}/{len(coords_df)} valid")
        
        if len(valid_coords_df) > 0:
            print(f"Coordinate ranges:")
            print(f"  X: {valid_coords_df['x'].min():.2f} to {valid_coords_df['x'].max():.2f}")
            print(f"  Y: {valid_coords_df['y'].min():.2f} to {valid_coords_df['y'].max():.2f}")
        
        return valid_coords_df
        
    except FileNotFoundError:
        print("Warning: Original pile dataset not found")
        return None
    except Exception as e:
        print(f"Error reconstructing coordinates: {e}")
        return None

# Run coordinate reconstruction
print("Reconstructing coordinates for spatial analysis...")
coords_df = reconstruct_coordinates_from_pile_ids()

if coords_df is not None and len(coords_df) > 0:
    print(f"Successfully reconstructed {len(coords_df)} coordinates")
    print(f"Coordinate ranges: X[{coords_df['x'].min():.0f}, {coords_df['x'].max():.0f}], "
          f"Y[{coords_df['y'].min():.0f}, {coords_df['y'].max():.0f}]")
else:
    print("Warning: Could not reconstruct coordinates")

def generate_spatial_predictions():
    """Generate prediction files with coordinates for all models."""
    
    if coords_df is None or len(coords_df) == 0:
        print("Warning: No valid coordinates available for spatial analysis")
        return None, []
    
    # Create comprehensive predictions DataFrame
    predictions_data = []
    
    for i, row in coords_df.iterrows():
        sample_idx = row['sample_idx']
        
        # Get predictions from all models for this sample
        sample_data = {
            'sample_id': sample_idx,
            'pile_id': row['pile_id'],
            'x': row['x'],
            'y': row['y'],
            'z': row['z'],
            'actual_label': row['actual_label']
        }
        
        # Add model predictions and probabilities
        for model_name, model_results in results.items():
            sample_data[f'{model_name}_prediction'] = model_results['test_predictions'][sample_idx]
            sample_data[f'{model_name}_probability'] = model_results['test_probabilities'][sample_idx]
            sample_data[f'{model_name}_correct'] = (
                model_results['test_predictions'][sample_idx] == row['actual_label']
            )
        
        predictions_data.append(sample_data)
    
    # Create comprehensive DataFrame
    predictions_df = pd.DataFrame(predictions_data)
    
    # Save comprehensive CSV
    csv_filename = output_path / 'predictions' / 'ml_predictions_all_models_with_coordinates.csv'
    predictions_df.to_csv(csv_filename, index=False)
    print(f"Saved comprehensive predictions: {csv_filename}")
    
    # Generate individual model files
    output_files = [csv_filename]
    
    for model_name in results.keys():
        # Create model-specific DataFrame
        model_cols = [
            'sample_id', 'pile_id', 'x', 'y', 'z', 'actual_label',
            f'{model_name}_prediction', f'{model_name}_probability', f'{model_name}_correct'
        ]
        model_df = predictions_df[model_cols].copy()
        
        # Rename columns for clarity
        model_df = model_df.rename(columns={
            f'{model_name}_prediction': 'predicted_label',
            f'{model_name}_probability': 'pile_probability',
            f'{model_name}_correct': 'correct_prediction'
        })
        
        # Save model-specific CSV
        model_csv = output_path / 'predictions' / f'predictions_{model_name.lower().replace(" ", "_")}.csv'
        model_df.to_csv(model_csv, index=False)
        output_files.append(model_csv)
        
        # Generate KML for predicted piles
        predicted_piles = model_df[model_df['predicted_label'] == 1].copy()
        
        if len(predicted_piles) > 0:
            try:
                # Create GeoDataFrame
                geometry = [Point(x, y) for x, y in zip(predicted_piles['x'], predicted_piles['y'])]
                gdf_predictions = gpd.GeoDataFrame(predicted_piles, geometry=geometry, crs='EPSG:32632')
                
                # Prepare KML-compatible data
                kml_data = gdf_predictions[['pile_id', 'pile_probability', 'actual_label', 'correct_prediction', 'geometry']].copy()
                kml_data['Name'] = kml_data['pile_id'].astype(str)
                kml_data['Description'] = (
                    'Prob: ' + kml_data['pile_probability'].round(3).astype(str) +
                    ' | Actual: ' + kml_data['actual_label'].astype(str) +
                    ' | Correct: ' + kml_data['correct_prediction'].astype(str)
                )
                
                # Keep only essential columns and reproject to WGS84
                kml_final = kml_data[['Name', 'Description', 'geometry']].to_crs('EPSG:4326')
                
                # Save KML
                model_kml = output_path / 'kmls' / f'predicted_piles_{model_name.lower().replace(" ", "_")}.kml'
                kml_final.to_file(model_kml, driver='KML')
                output_files.append(model_kml)
                
                print(f"{model_name}: {model_csv}, {model_kml} ({len(predicted_piles)} predicted piles)")
                
            except Exception as e:
                print(f"Warning: KML export failed for {model_name}: {e}")
        else:
            print(f"{model_name}: {model_csv} (no piles predicted)")
    
    return predictions_df, output_files

# Generate spatial prediction files
if coords_df is not None:
    print("Generating spatial predictions...")
    predictions_df, output_files = generate_spatial_predictions()
    
    if predictions_df is not None:
        print(f"Generated predictions for {len(predictions_df)} samples")
        print(f"Output files: {output_files}")
else:
    print("Skipping spatial predictions - no coordinates available")

def create_spatial_visualization():
    """Create comprehensive spatial overlay visualization."""
    
    if predictions_df is None or len(predictions_df) == 0:
        print("Warning: No prediction data available for visualization")
        return
    
    # Load reference spatial data
    try:
        # Load IFC coordinates
        ifc_df = pd.read_csv('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv')
        ifc_coords = ifc_df[['X', 'Y']].values
        
        # Load KML coordinates  
        kml_path = "/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/kml/pile.kml"
        gdf_kml = gpd.read_file(kml_path, driver='KML')
        gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)
        gdf_kml['geometry'] = gdf_kml.geometry.centroid
        kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)
        
        reference_available = True
        print(f"Reference data loaded: IFC ({len(ifc_coords)}), KML ({len(kml_coords)})")
        
    except Exception as e:
        print(f"Warning: Could not load reference data: {e}")
        reference_available = False
        ifc_coords = None
        kml_coords = None
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    axes = axes.flatten()
    
    model_names = list(results.keys())
    
    for idx, model_name in enumerate(model_names):
        ax = axes[idx]
        
        # Plot reference data if available
        if reference_available:
            if ifc_coords is not None:
                ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1], 
                          s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')
            
            if kml_coords is not None:
                ax.scatter(kml_coords[:, 0], kml_coords[:, 1], 
                          s=8, alpha=0.7, color='green', label='KML Ground Truth')
        
        # Get model predictions
        model_results = results[model_name]
        
        # Map predictions to coordinates
        coords_with_preds = predictions_df.copy()
        sample_indices = coords_with_preds['sample_id'].values
        y_pred_subset = model_results['test_predictions'][sample_indices]
        y_test_subset = coords_with_preds['actual_label'].values
        
        # Calculate accuracy for this subset
        subset_accuracy = np.mean(y_pred_subset == y_test_subset)
        
        # Plot predictions
        correct_mask = (y_pred_subset == y_test_subset)
        incorrect_mask = ~correct_mask
        
        if np.sum(correct_mask) > 0:
            ax.scatter(coords_with_preds.loc[correct_mask, 'x'], 
                      coords_with_preds.loc[correct_mask, 'y'],
                      c='blue', s=30, alpha=0.8, marker='o', 
                      label=f'Correct ({np.sum(correct_mask)})')
        
        if np.sum(incorrect_mask) > 0:
            ax.scatter(coords_with_preds.loc[incorrect_mask, 'x'], 
                      coords_with_preds.loc[incorrect_mask, 'y'],
                      c='red', s=50, alpha=0.9, marker='x', linewidth=2,
                      label=f'Errors ({np.sum(incorrect_mask)})')
        
        # Formatting
        ax.set_title(f'{model_name}\nAccuracy: {model_results["test_acc"]:.3f}, AUC: {model_results["test_auc"]:.3f}')
        ax.set_xlabel('X (UTM)')
        ax.set_ylabel('Y (UTM)')
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal', adjustable='box')
    
    plt.tight_layout()
    plt.savefig(output_path / 'figures' / 'spatial_overlay_all_models.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("Saved spatial overlay: spatial_overlay_all_models.png")


# Create spatial overlay
if 'predictions_df' in locals() and predictions_df is not None:
    print("Creating spatial overlay visualization...")
    create_spatial_visualization()
else:
    print("Skipping spatial visualization - no prediction data available")

# Save best model and scaler
model_filename = output_path / 'models' / f'best_model_{best_model_name.lower().replace(" ", "_")}.pkl'
scaler_filename = output_path / 'models' / 'feature_scaler.pkl'

joblib.dump(best_model, model_filename)
joblib.dump(scaler, scaler_filename)

# Save comprehensive results
final_results = {
    'model_comparison': {
        name: {
            'test_accuracy': float(res['test_acc']),
            'test_auc': float(res['test_auc']),
            'validation_accuracy': float(res['val_acc'])
        }
        for name, res in results.items()
    },
    'best_model': {
        'name': best_model_name,
        'test_accuracy': float(results[best_model_name]['test_acc']),
        'test_auc': float(results[best_model_name]['test_auc'])
    },
    'feature_names': feature_names,
    'dataset_info': {
        'n_features': len(feature_names),
        'train_samples': len(X_train),
        'val_samples': len(X_val),
        'test_samples': len(X_test)
    }
}

results_filename = output_path / 'results' / 'classical_ml_results.json'
with open(results_filename, 'w') as f:
    json.dump(final_results, f, indent=2)

print(f"Saved model: {model_filename}")
print(f"Saved results: {results_filename}")